# 🎓 高校人才网管理后台

<div align="center">
  <img src="https://img.gaoxiaojob.com/uploads/static/image/logo/logo_6.png" alt="高才信息科技" width="120">
  
  <h3>专业的高校人才管理平台</h3>
  <p>基于 Vue 3 + Element Plus 构建的现代化管理后台</p>

  ![Vue](https://img.shields.io/badge/Vue-3.5.13-4FC08D?style=flat-square&logo=vue.js)
  ![Element Plus](https://img.shields.io/badge/Element%20Plus-2.9.5-409EFF?style=flat-square&logo=element)
  ![TypeScript](https://img.shields.io/badge/TypeScript-5.2.2-3178C6?style=flat-square&logo=typescript)
  ![Vite](https://img.shields.io/badge/Vite-4.5.0-646CFF?style=flat-square&logo=vite)
</div>

## ✨ 特性

- 🎨 **现代化UI设计** - 基于最新设计趋势，提供优雅的用户体验
- 🚀 **高性能** - 基于 Vue 3 Composition API 和 Vite 构建
- 📱 **响应式布局** - 完美适配桌面端、平板和移动设备
- 🎯 **企微登录集成** - 支持企业微信扫码登录，安全便捷
- 🛡️ **权限管理** - 完整的 RBAC 权限控制系统
- 🔧 **开箱即用** - 丰富的组件和工具，快速开发
- 📊 **数据可视化** - 支持图表展示和数据分析
- 🌙 **主题定制** - 支持动态主题切换和个性化配置

## 🏗️ 技术栈

| 技术 | 说明 | 版本 |
| --- | --- | --- |
| [Vue 3](https://vuejs.org/) | 渐进式 JavaScript 框架 | 3.5.13 |
| [Element Plus](https://element-plus.org/) | Vue 3 组件库 | 2.9.5 |
| [TypeScript](https://www.typescriptlang.org/) | JavaScript 的超集 | 5.2.2 |
| [Vite](https://vitejs.dev/) | 下一代前端构建工具 | 4.5.0 |
| [Vue Router](https://router.vuejs.org/) | Vue.js 官方路由 | 4.2.5 |
| [Vuex](https://vuex.vuejs.org/) | Vue.js 状态管理 | 4.1.0 |
| [Axios](https://axios-http.com/) | HTTP 客户端 | 1.6.0 |
| [SCSS](https://sass-lang.com/) | CSS 预处理器 | 1.70.0 |

## 📦 项目结构

```
src/
├── api/                    # API 接口
├── assets/                 # 静态资源
├── components/             # 公共组件
│   ├── auth/              # 权限组件
│   ├── base/              # 基础组件
│   ├── business/          # 业务组件
│   └── upload/            # 上传组件
├── layout/                 # 布局组件
│   ├── component/         # 布局子组件
│   ├── navBars/           # 导航栏
│   └── main/              # 主布局
├── router/                 # 路由配置
├── store/                  # 状态管理
├── theme/                  # 主题样式
├── utils/                  # 工具函数
└── views/                  # 页面组件
    ├── login/             # 登录页面
    ├── home/              # 首页
    ├── job/               # 职位管理
    ├── person/            # 人才管理
    ├── company/           # 企业管理
    ├── cms/               # 内容管理
    └── system/            # 系统设置
```

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 16.0.0
- **pnpm**: >= 8.0.0 (推荐)

### 安装依赖

```bash
# 推荐使用 pnpm
pnpm install

# 或使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发环境

```bash
# 启动开发服务器
pnpm dev

# 测试环境
pnpm test

# 预发布环境
pnpm pre
```

访问 [http://localhost:5173](http://localhost:5173) 查看应用

### 构建部署

```bash
# 构建生产版本
pnpm build

# 自动化开发部署
pnpm develop

# 自动化生产部署
pnpm release
```

## 🎨 UI 设计

### 设计原则

- **一致性** - 统一的设计语言和交互规范
- **易用性** - 简洁直观的操作流程
- **美观性** - 现代化的视觉设计
- **响应式** - 适配多种设备尺寸

### 主题色彩

```scss
// 品牌主色
$primary: #ff9a41;          // 橙色主题
$primary-light: #ffb366;    // 浅橙色
$primary-dark: #e8873a;     // 深橙色

// 功能色彩
$success: #67c23a;          // 成功绿
$warning: #e6a23c;          // 警告黄
$danger: #f56c6c;           // 危险红
$info: #909399;             // 信息灰
```

### 组件库

项目提供了丰富的业务组件：

- **表单组件** - 智能表单、搜索器、选择器等
- **表格组件** - 数据表格、分页、排序等
- **上传组件** - 图片上传、文件上传等
- **编辑器** - 富文本编辑器、代码编辑器等
- **业务组件** - 地址选择、薪资组件等

## 🔧 开发指南

### 代码规范

项目使用 ESLint + Prettier 进行代码格式化：

```bash
# 代码检查
pnpm lint-fix
```

### 提交规范

使用 Angular 提交信息规范：

```
feat: 新功能
fix: 修复 bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

### 分支管理

```bash
# 创建新分支
git checkout -b feature/new-feature

# 切换分支
git checkout main

# 删除本地分支
git branch -d feature/old-feature

# 删除远程分支
git push origin --delete feature/old-feature
```

## 🌟 核心功能

### 1. 用户认证
- 账号密码登录
- 企业微信扫码登录
- 自动登录状态管理
- 登录安全验证

### 2. 权限管理
- 基于角色的访问控制(RBAC)
- 动态路由权限
- 按钮级权限控制
- 数据权限隔离

### 3. 职位管理
- 职位发布与编辑
- 职位审核流程
- 职位状态管理
- 投递数据统计

### 4. 人才管理
- 简历库管理
- 人才筛选搜索
- 投递记录跟踪
- 面试邀约管理

### 5. 企业管理
- 企业信息维护
- 账户权限设置
- 合作状态管理
- 服务包配置

### 6. 内容管理
- 公告发布管理
- 新闻资讯编辑
- 广告位管理
- SEO 优化工具

## 📊 数据统计

项目提供丰富的数据统计功能：

- 📈 实时访问统计
- 📊 用户行为分析
- 💼 职位发布趋势
- 📋 简历投递数据
- 🏢 企业活跃度分析

## 🛠️ 部署说明

### 服务器要求

- **操作系统**: Linux/Windows/MacOS
- **Node.js**: >= 16.0.0
- **Nginx**: >= 1.18.0 (推荐)

### 自动化部署

项目支持自动化部署脚本：

```bash
# 开发环境部署
./docs/server_build/develop

# 生产环境部署
./docs/server_build/release
```

部署脚本会自动：
1. 拉取最新代码
2. 安装依赖
3. 构建项目
4. 备份旧版本
5. 部署新版本

## 🤝 贡献指南

1. Fork 本仓库
2. 创建你的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交你的修改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📝 更新日志

### v1.0.0 (2024-01-15)

#### ✨ 新增功能
- 🎨 全新UI设计，现代化登录界面
- 🏠 重构主页面，添加快速操作面板
- 🎯 集成企业微信登录功能
- 📱 完善响应式布局支持

#### 🐛 问题修复
- 修复登录状态异常问题
- 优化表格性能和加载速度
- 解决移动端适配问题

#### 🔧 技术优化
- 升级到 Vue 3.5.13
- 更新 Element Plus 到 2.9.5
- 优化构建配置和打包体积
- 完善 TypeScript 类型定义

## 📞 技术支持

- **公司**: 广州高才信息科技有限公司
- **邮箱**: [<EMAIL>](mailto:<EMAIL>)
- **官网**: [https://www.gaoxiaojob.com](https://www.gaoxiaojob.com)

## 📄 许可证

本项目仅供 广州高才信息科技有限公司 内部使用，未经授权不得用于商业用途。

---

<div align="center">
  <p>由 ❤️ 和 ☕ 驱动开发</p>
  <p>© 2024 广州高才信息科技有限公司. 保留所有权利.</p>
</div>
