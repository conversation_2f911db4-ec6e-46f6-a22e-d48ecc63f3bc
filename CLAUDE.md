# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vue 3 + TypeScript management system for a university talent network platform. The project uses modern frontend technologies with Element Plus UI components, Vite build tool, and follows enterprise-grade development practices.

## Key Commands

### Development
```bash
# Start development server
pnpm dev          # Development environment (--mode development)
pnpm test         # Test environment (--mode test)
pnpm pre          # Pre-production environment (--mode pre)
pnpm prod         # Production environment (--mode production)
pnpm gray         # Gray environment

# Build for different environments
pnpm build        # Default production build
pnpm build:test   # Build for test environment
pnpm build:pre    # Build for pre-production
pnpm build:prod   # Build for production

# Deployment
pnpm develop      # Deploy to development server
pnpm release      # Deploy to production server

# Code quality
pnpm lint-fix     # Fix ESLint issues
```

### Environment URLs
- Development: https://dev.admin.gcjob.jugaocai.com
- Test: https://test.admin.gcjob.jugaocai.com
- Pre-production: https://pre.admin.gcjob.jugaocai.com
- Production: https://admin.gaoxiaojob.com

## Environment Configuration

### Environment Files
- `.env` - Base configuration
- `.env.development` - Development environment
- `.env.test` - Test environment  
- `.env.pre` - Pre-production environment
- `.env.production` - Production environment

### Environment Variables Usage
```typescript
// Use the EnvUtils class (recommended approach)
import EnvUtils, { SmartLogger } from '/@/utils/env'

// Environment checks
if (EnvUtils.isDevelopment()) {
  SmartLogger.debug('Development mode enabled')
}

// Get configuration
const apiUrl = EnvUtils.getApiUrl()
const appTitle = EnvUtils.getAppTitle()
const showEnvTag = EnvUtils.shouldShowEnvTag()
```

### Key Environment Variables
- `VITE_API_URL` - API base URL
- `VITE_APP_TITLE` - Application title with environment suffix
- `VITE_SHOW_ENV_TAG` - Whether to show environment tag in UI
- `VITE_ENABLE_MOCK` - Enable mock data in development
- `VITE_LOG_LEVEL` - Logging level (debug/info/warn/error)
- `VITE_ENABLE_ERROR_LOG` - Enable error monitoring
- `VITE_UPLOAD_URL` - File upload service URL

### Environment-Specific Logic
```typescript
import { FeatureToggle, handleError } from '/@/utils/envLogic'

// Feature toggles based on environment
if (FeatureToggle.isNewFeatureEnabled()) {
  // Enable new features in dev/test only
}

// Environment-aware error handling
try {
  // Business logic
} catch (error) {
  handleError(error) // Shows different messages per environment
}
```

## Architecture Overview

### Tech Stack
- **Vue 3.5.13** with Composition API
- **TypeScript 5.2.2** for type safety
- **Element Plus 2.9.5** UI components
- **Vite 4.5.0** build tool
- **Vue Router 4.2.5** for routing
- **Vuex 4.1.0** for state management
- **Axios 1.6.0** for HTTP requests

### Directory Structure
```
src/
├── api/                    # API modules (30+ endpoint modules)
├── components/             # Reusable components
│   ├── auth/              # Authentication components
│   ├── base/              # Base UI components (50+ selectors)
│   ├── business/          # Business logic components
│   ├── upload/            # File upload components
│   └── GlobalToolbox/     # Global utility components
├── layout/                # Layout system (4 layout modes)
├── router/                # Dynamic routing with permissions
├── store/                 # Vuex state management
├── utils/                 # Utility functions
├── views/                 # Page components (16 modules)
└── theme/                 # Theme and styling
```

### Key Architecture Patterns

#### 1. Dynamic Routing System
- **Permission-based routing**: Routes are dynamically loaded based on user permissions
- **Module-based organization**: 16 business modules with lazy loading
- **Route guards**: Authentication and authorization checks
- **Keep-alive caching**: Two-level caching for performance

#### 2. API Layer Design
- **Modular API structure**: Each business module has its own API file
- **Request interceptors**: Automatic token handling, loading states, error handling
- **Response standardization**: Consistent response format across all endpoints
- **QS serialization**: Proper parameter serialization for backend compatibility

#### 3. Component Architecture
- **Component hierarchy**: auth/ → base/ → business/ → page components
- **Smart form generators**: Dynamic form generation with validation
- **Rich text integration**: wangEditor for content management
- **Upload system**: Image compression, cropping, and multi-format support

#### 4. State Management
- **Vuex modules**: app, userInfos, themeConfig, routesList, selectList, clipboard, tagsViewRoutes
- **Persistent storage**: Local storage integration for user preferences
- **Cache optimization**: Selector data caching to reduce API calls

## Development Guidelines

### Component Development
- Use **PascalCase** for component files and names
- Follow **Vue 3 Composition API** patterns
- Use **TypeScript interfaces** for props and data structures
- Implement **scoped styles** with SCSS

### API Integration
- Place API definitions in `src/api/` organized by business module
- Use the centralized request utility at `src/utils/request.ts`
- Handle loading states automatically through request interceptors
- Implement proper error handling with user-friendly messages

### Image Handling
- **PNG images**: Preserve transparency during compression
- **JPEG images**: Use quality compression with white background fill
- **Upload components**: Support drag-and-drop, cropping, and format conversion
- **Compression logic**: Check `src/components/GlobalToolbox/ImageTool.vue` for implementation

### Layout System
- **4 layout modes**: defaults, classic, transverse, columns
- **Responsive design**: Mobile breakpoint at 1000px
- **Dynamic sidebar**: Collapsible navigation with state persistence
- **Theme system**: Customizable colors and component sizes

### Permission System
- **RBAC model**: Role-based access control
- **Route-level permissions**: Dynamic menu generation
- **Button-level permissions**: Conditional UI elements
- **Data isolation**: User-specific data access

## Important Configuration Files

### Build Configuration
- `vite.config.ts`: Multi-environment proxy setup, build optimization
- `tsconfig.json`: TypeScript configuration
- `package.json`: Scripts and dependencies

### Application Configuration
- `src/utils/request.ts`: HTTP client with interceptors (500s timeout)
- `src/router/index.ts`: Dynamic routing with permission guards
- `src/store/index.ts`: Vuex store configuration

## Business Domain Context

This system manages university talent recruitment with these core modules:

### Primary Functions
- **Job Management**: Position publishing, approval workflows, application tracking
- **Talent Management**: Resume database, candidate screening, interview scheduling
- **Company Management**: Enterprise profiles, account permissions, service packages
- **Content Management**: Announcements, news, advertisements, SEO tools

### Authentication Methods
- Username/password login
- WeChat Work QR code login
- Automatic session management

### Data Flow
1. User authentication → Permission loading → Dynamic route generation
2. API requests → Interceptors → Loading states → Response handling
3. Component interactions → Event emission → State updates → UI re-rendering

## Testing and Quality

### Code Quality
- ESLint configuration for consistent code style
- Prettier for code formatting
- TypeScript strict mode enabled
- Component unit tests in place

### Performance Optimization
- Module lazy loading
- Component caching with keep-alive
- Virtual scrolling for large lists
- Image compression and optimization

## Deployment

### Automated Deployment
- Development: `./docs/server_build/develop`
- Production: `./docs/server_build/release`
- Scripts handle: code pull, dependency installation, build, backup, deployment

### Environment Management
- Multiple environment support through NODE_ENV_PROXY
- Separate API endpoints for each environment
- SSL support for development servers