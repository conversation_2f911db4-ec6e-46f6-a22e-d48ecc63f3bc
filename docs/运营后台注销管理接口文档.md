# 运营后台求职者注销管理接口文档

**版本**: V2.7.2  
**创建时间**: 2024-01-17  
**文档状态**: 已完成

## 1. 接口概述

本文档描述了运营后台用于管理求职者注销申请的相关接口，包括列表查询、详情查看、统计数据、人工操作等功能。

## 2. 接口列表

### 2.1 获取注销申请列表

#### 基本信息

- **接口名称**: 获取注销申请列表
- **请求方式**: GET
- **接口地址**: `/admin/resume-cancel/get-list`
- **需要登录**: 是（运营后台）

#### 请求参数

| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| cancelLogId | Number | 否 | 123 | 注销日志ID |
| mobile | String | 否 | 138 | 手机号（模糊搜索） |
| name | String | 否 | 张三 | 用户姓名（模糊搜索） |
| email | String | 否 | <EMAIL> | 邮箱（模糊搜索） |
| status | Number | 否 | 1 | 注销状态：1=申请中，2=已撤回，3=已完成 |
| cancelReasonType | Number | 否 | 1 | 注销原因类型 |
| smsStatus | Number | 否 | 1 | 短信发送状态 |
| applyTimeStart | String | 否 | 2024-01-01 | 申请时间开始 |
| applyTimeEnd | String | 否 | 2024-01-31 | 申请时间结束 |
| cooldownEndTimeStart | String | 否 | 2024-01-01 | 冷静期结束时间开始 |
| cooldownEndTimeEnd | String | 否 | 2024-01-31 | 冷静期结束时间结束 |
| completeTimeStart | String | 否 | 2024-01-01 | 完成时间开始 |
| completeTimeEnd | String | 否 | 2024-01-31 | 完成时间结束 |
| adminId | Number | 否 | 1 | 操作管理员ID |
| ip | String | 否 | *********** | IP地址（模糊搜索） |
| sortField | String | 否 | apply_time | 排序字段 |
| sortOrder | String | 否 | DESC | 排序方向：ASC/DESC |
| page | Number | 否 | 1 | 页码，从1开始 |
| pageSize | Number | 否 | 20 | 每页数量 |

#### 返回参数

```json
{
  "msg": "操作成功",
  "result": 1,
  "data": {
    "list": [
      {
        "id": 123,
        "memberId": 456,
        "resumeId": 789,
        "adminId": 0,
        "cancelReasonType": 1,
        "cancelReasonTypeText": "已找到新工作，以后不再打算找工作",
        "cancelReasonDetail": "",
        "applyTime": "2024-01-17 10:30:00",
        "applyTimeFormat": "2024-01-17 10:30:00",
        "cooldownEndTime": "2024-01-24 10:30:00",
        "cooldownEndTimeFormat": "2024-01-24 10:30:00",
        "completeTime": "0000-00-00 00:00:00",
        "completeTimeFormat": "",
        "withdrawTime": "0000-00-00 00:00:00",
        "withdrawTimeFormat": "",
        "status": 1,
        "statusText": "申请中",
        "ip": "*************",
        "smsStatus": 1,
        "smsStatusText": "已发送申请成功短信",
        "mobile": "13800138000",
        "mobileMasked": "138****8000",
        "mobileCode": "86",
        "email": "<EMAIL>",
        "emailMasked": "t***@example.com",
        "name": "张三",
        "username": "zhangsan",
        "adminName": null,
        "memberStatus": 1,
        "memberCancelStatus": 9,
        "remainingCooldownDays": 5,
        "operationType": "system",
        "operationTypeText": "系统自动",
        "resumeSettingParsed": {}
      }
    ],
    "pages": {
      "total": 100,
      "pageSize": 20,
      "page": 1,
      "totalPages": 5
    }
  }
}
```

### 2.2 获取注销申请详情

#### 基本信息

- **接口名称**: 获取注销申请详情
- **请求方式**: GET
- **接口地址**: `/admin/resume-cancel/get-detail`
- **需要登录**: 是（运营后台）

#### 请求参数

| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| cancelLogId | Number | 是 | 123 | 注销日志ID |

#### 返回参数

```json
{
  "msg": "操作成功",
  "result": 1,
  "data": {
    "id": 123,
    "memberId": 456,
    "resumeId": 789,
    "adminId": 0,
    "cancelReasonType": 1,
    "cancelReasonTypeText": "已找到新工作，以后不再打算找工作",
    "cancelReasonDetail": "已经找到满意的工作，不再需要求职",
    "applyTime": "2024-01-17 10:30:00",
    "applyTimeFormat": "2024-01-17 10:30:00",
    "cooldownEndTime": "2024-01-24 10:30:00",
    "cooldownEndTimeFormat": "2024-01-24 10:30:00",
    "completeTime": "0000-00-00 00:00:00",
    "completeTimeFormat": "",
    "withdrawTime": "0000-00-00 00:00:00",
    "withdrawTimeFormat": "",
    "status": 1,
    "statusText": "申请中",
    "ip": "*************",
    "smsStatus": 1,
    "smsStatusText": "已发送申请成功短信",
    "resumeSetting": "{\"jobSubscribe\":false,\"messageNotify\":false}",
    "mobile": "13800138000",
    "mobileMasked": "138****8000",
    "mobileCode": "86",
    "email": "<EMAIL>",
    "emailMasked": "t***@example.com",
    "name": "张三",
    "username": "zhangsan",
    "adminName": null,
    "memberStatus": 1,
    "memberCancelStatus": 9,
    "resumeDataJson": "{\"name\":\"张三\",\"mobile\":\"13800138000\",...}",
    "remainingCooldownDays": 5,
    "operationType": "system",
    "operationTypeText": "系统自动",
    "resumeSettingParsed": {
      "jobSubscribe": false,
      "messageNotify": false
    },
    "resumeDataParsed": {
      "name": "张三",
      "mobile": "13800138000"
    }
  }
}
```

### 2.3 获取统计数据

#### 基本信息

- **接口名称**: 获取统计数据
- **请求方式**: GET
- **接口地址**: `/admin/resume-cancel/get-statistics`
- **需要登录**: 是（运营后台）

#### 请求参数

| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| startDate | String | 否 | 2024-01-01 | 统计开始日期 |
| endDate | String | 否 | 2024-01-31 | 统计结束日期 |

#### 返回参数

```json
{
  "msg": "操作成功",
  "result": 1,
  "data": {
    "overview": {
      "totalApplies": 1000,
      "todayApplies": 10,
      "weekApplies": 50,
      "monthApplies": 200
    },
    "statusStats": [
      {
        "status": 1,
        "statusName": "申请中",
        "count": 300,
        "percentage": 30.0
      },
      {
        "status": 2,
        "statusName": "已撤回",
        "count": 200,
        "percentage": 20.0
      },
      {
        "status": 3,
        "statusName": "已完成",
        "count": 500,
        "percentage": 50.0
      }
    ],
    "reasonStats": [
      {
        "reasonType": 1,
        "reasonName": "已找到新工作，以后不再打算找工作",
        "count": 400,
        "percentage": 40.0
      }
    ],
    "smsStats": [
      {
        "smsStatus": 1,
        "smsStatusName": "已发送申请成功短信",
        "count": 800,
        "percentage": 80.0
      }
    ]
  }
}
```

### 2.4 获取筛选选项

#### 基本信息

- **接口名称**: 获取筛选选项
- **请求方式**: GET
- **接口地址**: `/admin/resume-cancel/get-filter-options`
- **需要登录**: 是（运营后台）

#### 请求参数

无

#### 返回参数

```json
{
  "msg": "操作成功",
  "result": 1,
  "data": {
    "statusOptions": [
      {
        "value": 1,
        "label": "申请中"
      },
      {
        "value": 2,
        "label": "已撤回"
      },
      {
        "value": 3,
        "label": "已完成"
      }
    ],
    "cancelReasonOptions": [
      {
        "value": 1,
        "label": "已找到新工作，以后不再打算找工作"
      }
    ],
    "smsStatusOptions": [
      {
        "value": 0,
        "label": "未发送"
      },
      {
        "value": 1,
        "label": "已发送申请成功短信"
      }
    ]
  }
}
```

### 2.5 人工执行注销

#### 基本信息

- **接口名称**: 人工执行注销
- **请求方式**: POST
- **接口地址**: `/admin/resume-cancel/manual-cancel`
- **需要登录**: 是（运营后台）

#### 请求参数

| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| cancelLogId | Number | 是 | 123 | 注销日志ID |

#### 返回参数

```json
{
  "msg": "注销操作执行成功",
  "result": 1,
  "data": {
    "success": true,
    "cancelLogId": 123,
    "snapshotId": 456,
    "completeTime": "2024-01-17 15:30:00",
    "adminId": 1
  }
}
```

### 2.6 导出注销申请列表

#### 基本信息

- **接口名称**: 导出注销申请列表
- **请求方式**: GET
- **接口地址**: `/admin/resume-cancel/export`
- **需要登录**: 是（运营后台）

#### 请求参数

支持与列表查询相同的筛选参数

#### 返回参数

```json
{
  "msg": "数据开始导出，成功下载后会在企业微信通知，请后续留意",
  "result": 1,
  "data": null
}
```

## 3. Apifox导入参数格式

### 3.1 获取注销申请列表

```
cancelLogId,Number,否,123,-,注销日志ID
mobile,String,否,138,-,手机号（模糊搜索）
name,String,否,张三,-,用户姓名（模糊搜索）
email,String,否,<EMAIL>,-,邮箱（模糊搜索）
status,Number,否,1,1=申请中/2=已撤回/3=已完成,注销状态
cancelReasonType,Number,否,1,-,注销原因类型
smsStatus,Number,否,1,-,短信发送状态
applyTimeStart,String,否,2024-01-01,-,申请时间开始
applyTimeEnd,String,否,2024-01-31,-,申请时间结束
cooldownEndTimeStart,String,否,2024-01-01,-,冷静期结束时间开始
cooldownEndTimeEnd,String,否,2024-01-31,-,冷静期结束时间结束
completeTimeStart,String,否,2024-01-01,-,完成时间开始
completeTimeEnd,String,否,2024-01-31,-,完成时间结束
adminId,Number,否,1,-,操作管理员ID
ip,String,否,***********,-,IP地址（模糊搜索）
sortField,String,否,apply_time,apply_time/cooldown_end_time/complete_time/status,排序字段
sortOrder,String,否,DESC,ASC/DESC,排序方向
page,Number,否,1,-,页码，从1开始
pageSize,Number,否,20,-,每页数量
```

### 3.2 获取注销申请详情

```
cancelLogId,Number,是,123,-,注销日志ID
```

### 3.3 人工执行注销

```
cancelLogId,Number,是,123,-,注销日志ID
```

## 4. 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 0 | 注销日志ID不能为空 | 必填参数缺失 |
| 0 | 注销申请记录不存在 | 查询的记录不存在 |
| 0 | 注销申请状态异常，无法执行操作 | 业务状态不符合操作条件 |

## 5. 使用说明

### 5.1 列表查询功能

- 支持多种条件组合查询
- 支持时间范围筛选
- 支持排序功能
- 自动脱敏处理敏感信息
- 显示剩余冷静期天数

### 5.2 详情查看功能

- 显示完整的注销申请信息
- 包含用户数据快照
- 显示操作历史记录
- 支持查看原始简历数据

### 5.3 统计功能

- 提供多维度统计数据
- 支持时间范围统计
- 按状态、原因、短信状态分类统计
- 提供百分比计算

### 5.4 人工操作功能

- 运营人员可手动执行注销
- 记录操作管理员信息
- 不发送短信通知（区别于系统自动）
- 完整的操作日志记录

### 5.5 数据安全

- 敏感信息自动脱敏
- 完整的数据快照备份
- 操作权限控制
- 详细的操作日志
