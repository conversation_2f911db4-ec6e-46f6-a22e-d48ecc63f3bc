# 导航和标签操作日志功能说明

## 概述
已为项目添加了详细的日志记录功能，用于跟踪用户的导航行为和标签操作。所有日志都会在浏览器控制台中显示，方便开发调试和用户行为分析。

## 日志类型

### 1. 侧边栏菜单点击 🔍
**文件位置**: `src/layout/navMenu/vertical.vue`
**触发时机**: 用户点击垂直侧边栏菜单项时
**日志格式**:
```javascript
console.log('🔍 [侧边栏菜单点击]', {
  type: '侧边栏菜单导航',
  targetPath: '/example/path',
  targetTitle: '菜单标题',
  isDynamic: false,
  timestamp: '2024-01-01 12:00:00'
})
```

### 2. 水平菜单点击 🔍
**文件位置**: `src/layout/navMenu/horizontal.vue`
**触发时机**: 用户点击水平菜单项时
**日志格式**:
```javascript
console.log('🔍 [水平菜单点击]', {
  type: '水平菜单导航',
  targetPath: '/example/path',
  targetTitle: '菜单标题',
  isDynamic: false,
  timestamp: '2024-01-01 12:00:00'
})
```

### 3. 分栏菜单点击 🔍
**文件位置**: `src/layout/component/columnsAside.vue`
**触发时机**: 用户点击分栏布局的菜单项时
**日志格式**:
```javascript
console.log('🔍 [分栏菜单点击]', {
  type: '分栏菜单导航',
  targetPath: '/example/path',
  targetTitle: '菜单标题',
  menuIndex: 0,
  timestamp: '2024-01-01 12:00:00'
})
```

### 4. 顶部标签切换 📋
**文件位置**: `src/layout/navBars/tagsView/tagsView.vue`
**触发时机**: 用户点击顶部标签页切换时
**日志格式**:
```javascript
console.log('📋 [顶部标签切换]', {
  type: '标签页切换',
  targetPath: '/example/path',
  targetTitle: '标签标题',
  tagIndex: 0,
  isActive: true,
  currentTagsList: [
    { title: '首页', path: '/home', isActive: false },
    { title: '当前页', path: '/current', isActive: true }
  ],
  timestamp: '2024-01-01 12:00:00'
})
```

### 5. 标签删除操作 ❌
**文件位置**: `src/layout/navBars/tagsView/tagsView.vue`
**触发时机**: 用户删除单个标签时
**日志格式**:
```javascript
console.log('❌ [标签删除]', {
  type: '删除标签',
  deletedTag: {
    title: '被删除的标签',
    path: '/deleted/path',
    index: 1
  },
  beforeDelete: [/* 删除前的标签列表 */],
  timestamp: '2024-01-01 12:00:00'
})
```

### 6. 删除后剩余标签 📋
**触发时机**: 标签删除操作完成后
**日志格式**:
```javascript
console.log('📋 [删除后剩余标签]', {
  type: '剩余标签列表',
  remainingTags: [
    { title: '首页', path: '/home', isActive: true },
    { title: '其他页', path: '/other', isActive: false }
  ],
  totalCount: 2,
  timestamp: '2024-01-01 12:00:00'
})
```

### 7. 关闭其他标签 🗑️
**触发时机**: 用户通过右键菜单选择"关闭其他标签"时
**日志格式**:
```javascript
console.log('🗑️ [关闭其他标签]', {
  type: '关闭其他标签',
  keepPath: '/current/path',
  beforeClose: [/* 关闭前的标签列表 */],
  timestamp: '2024-01-01 12:00:00'
})
```

### 8. 关闭全部标签 🗑️
**触发时机**: 用户通过右键菜单选择"关闭全部标签"时
**日志格式**:
```javascript
console.log('🗑️ [关闭全部标签]', {
  type: '关闭全部标签',
  beforeClose: [/* 关闭前的标签列表 */],
  timestamp: '2024-01-01 12:00:00'
})
```

### 9. 右键菜单操作 🖱️
**触发时机**: 用户在标签上右键并选择操作时
**日志格式**:
```javascript
console.log('🖱️ [右键菜单操作]', {
  type: '右键菜单',
  action: '刷新当前', // 可能的值: 刷新当前、关闭当前、关闭其它、关闭全部、全屏当前
  actionId: 0,
  targetTag: {
    title: '目标标签',
    path: '/target/path',
    url: '/target/url'
  },
  timestamp: '2024-01-01 12:00:00'
})
```

### 10. 面包屑导航点击 🍞
**文件位置**: `src/layout/navBars/breadcrumb/breadcrumb.vue`
**触发时机**: 用户点击面包屑导航项时
**日志格式**:
```javascript
console.log('🍞 [面包屑导航点击]', {
  type: '面包屑导航',
  targetPath: '/target/path',
  targetTitle: '导航标题',
  timestamp: '2024-01-01 12:00:00'
})
```

## 使用方法

1. **查看日志**: 打开浏览器开发者工具的控制台(Console)标签页
2. **过滤日志**: 可以使用以下关键词过滤特定类型的日志：
   - `侧边栏菜单点击`
   - `顶部标签切换`
   - `标签删除`
   - `剩余标签`
   - `右键菜单操作`
   - `面包屑导航点击`

3. **日志图标含义**:
   - 🔍 菜单导航操作
   - 📋 标签相关操作
   - ❌ 删除操作
   - 🗑️ 批量关闭操作
   - 🖱️ 右键菜单操作
   - 🍞 面包屑导航操作

## 注意事项

1. 所有日志都包含时间戳，方便追踪操作时间
2. 标签相关的日志会显示完整的标签列表状态，便于调试
3. 日志仅在开发环境中显示，生产环境可以通过构建配置移除
4. 所有路径和标题信息都会被记录，便于分析用户行为

## 调试建议

1. 在进行导航测试时，保持控制台打开
2. 可以通过时间戳关联不同的操作序列
3. 注意观察标签列表的变化，确保操作符合预期
4. 如需要更详细的信息，可以在相应的函数中添加更多日志点
