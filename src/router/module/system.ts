import { RouteRecordRaw } from 'vue-router'

export const systemRoutes: Array<RouteRecordRaw> = [
  {
    path: '/system',
    name: 'system',
    component: () => import('/@/layout/routerView/parent.vue'),
    redirect: '/system/memu',
    meta: {
      title: '系统管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/system.svg'
    },
    children: [
      {
        path: '/cms/columnList',
        name: 'cmsColumnList',
        component: () => import('/@/views/cms/column/list.vue'),
        meta: {
          title: '栏目管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/system/menu',
        name: 'systemMenu',
        component: () => import('/@/views/system/menu/index.vue'),
        meta: {
          title: '系统菜单',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/demo/demo2/list',
        name: 'yincangMenu',
        component: () => import('/@/views/demo/demo2/list.vue'),
        meta: {
          title: '隐藏菜单',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/system/classification/jobType',
        name: 'jobType',
        component: () => import('/@/views/system/classification/jobType.vue'),
        meta: {
          title: '职位类型',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/system/majorList',
        name: 'systemMajorList',
        component: () => import('/@/views/system/majorList/index.vue'),
        meta: {
          title: '学科分类',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/system/resumeLibrary',
        name: 'systemResumeLibrary',
        component: () => import('/@/views/system/resumeLibrary/index.vue'),
        meta: {
          title: '人才库管理',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/system/operationSetting',
        name: 'systemOperationSetting',
        component: () => import('/@/views/system/operationSetting/index.vue'),
        meta: {
          title: '运营配置',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/system/config',
        name: 'systemConfig',
        component: () => import('/@/views/system/config/index.vue'),
        meta: {
          title: '开发配置',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      // 微信机器人配置
      {
        path: '/system/wxWorkRobot',
        name: 'wxWorkRobot',
        component: () => import('/@/views/system/wxWorkRobot/index.vue'),
        meta: {
          title: '企微机器人',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/system/qiniu',
        name: 'systemQiniu',
        component: () => import('/@/views/system/qiniu/index.vue'),
        meta: {
          title: '七牛管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
