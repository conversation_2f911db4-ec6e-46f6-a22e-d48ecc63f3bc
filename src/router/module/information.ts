import { RouteRecordRaw } from 'vue-router'

export const informationRoutes: Array<RouteRecordRaw> = [
  {
    path: '/information',
    name: 'cmsNews',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '资讯管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/cms.svg'
    },
    children: [
      {
        path: '/cms/newsAdd',
        name: 'cmsNewsAdd',
        component: () => import('/@/views/cms/news/add.vue'),
        meta: {
          title: '发布资讯',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/newsList',
        name: 'cmsNewsList',
        component: () => import('/@/views/cms/news/list.vue'),
        meta: {
          title: '资讯查询',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/newsEdit/:id',
        name: 'cmsNewsEdit',
        component: () => import('/@/views/cms/news/add.vue'),
        meta: {
          title: '编辑资讯',
          isLink: '',
          isHide: true,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/topic',
        name: 'cmsTopic',
        component: () => import('/@/views/cms/topic/list.vue'),
        meta: {
          title: '话题管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
