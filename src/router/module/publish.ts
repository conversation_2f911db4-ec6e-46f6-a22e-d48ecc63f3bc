import { RouteRecordRaw } from 'vue-router'

export const publishRoutes: Array<RouteRecordRaw> = [
  {
    path: '/publish',
    name: 'cmsPublish',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '内容发布',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/cms.svg'
    },
    children: [
      {
        path: '/cms/announcementAdd',
        name: 'cmsAnnouncementAdd',
        component: () => import('/@/views/cms/announcement/add.vue'),
        meta: {
          title: '新增公告',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/jobAdd',
        name: 'cmsJobAdd',
        component: () => import('/@/views/cms/job/add/index.vue'),
        meta: {
          title: '新增职位',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
