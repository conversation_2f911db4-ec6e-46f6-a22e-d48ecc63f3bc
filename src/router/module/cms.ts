import { RouteRecordRaw } from 'vue-router'

export const cmsRoutes: Array<RouteRecordRaw> = [
  {
    path: '/cms',
    name: 'cms',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '内容管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/cms.svg'
    },
    children: [
      {
        path: '/cms/cmsAnnouncementAuditList',
        name: 'cmsAnnouncementAuditList',
        // component: () => import('/@/views/cms/announcement/cmsAnnouncementAuditList.vue'),
        component: () => import('/@/views/announcement/auditList.vue'),
        meta: {
          title: '公告审核',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/announcementList',
        name: 'cmsAnnouncementList',
        component: () => import('/@/views/cms/announcement/list.vue'),
        meta: {
          title: '公告查询',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/announcementJobEdit',
        name: 'announcementJobEdit',
        component: () => import('/@/views/cms/announcement/announcementJobEdit.vue'),
        meta: {
          title: '职位编辑',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      // {
      //   path: '/cms/announcementAdd',
      //   name: 'cmsAnnouncementAdd',
      //   component: () => import('/@/views/cms/announcement/add.vue'),
      //   meta: {
      //     title: '发布公告',
      //     isLink: '',
      //     isHide: true,
      //     isKeepAlive: false,
      //     isAffix: false,
      //     isIframe: false,
      //     icon: ''
      //   }
      // },
      {
        path: '/cms/announcementEdit/:id',
        name: 'cmsAnnouncementEdit',
        component: () => import('/@/views/cms/announcement/add.vue'),
        meta: {
          title: '编辑公告',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/announcementDetail/:id/:type',
        name: 'cmsannouncementDetail',
        component: () => import('/@/views/cms/announcement/announcementDetail.vue'),
        meta: {
          title: '公告详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/announcementAuditDetail/:id',
        name: 'announcementAuditDetail',
        component: () => import('/@/views/cms/announcement/detail.vue'),
        meta: {
          title: '审核公告',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      // {
      //   path: '/cms/advertisingList',
      //   name: 'cmsAdvertisingList',
      //   component: () => import('/@/views/cms/advertising/management/index.vue'),
      //   meta: {
      //     title: '广告管理',
      //     isLink: '',
      //     isHide: false,
      //     isKeepAlive: true,
      //     isAffix: false,
      //     isIframe: false,
      //     icon: ''
      //   }
      // },
      // {
      //   path: '/cms/miniAdvertisingList',
      //   name: 'cmsMiniAdvertisingList',
      //   component: () => import('/@/views/cms/miniAdvertising/management/index.vue'),
      //   meta: {
      //     title: '小程序广告管理',
      //     isLink: '',
      //     isHide: false,
      //     isKeepAlive: true,
      //     isAffix: false,
      //     isIframe: false,
      //     icon: ''
      //   }
      // },
      // {
      //   path: '/cms/boShiHouList',
      //   name: 'cmsBoShiHouList',
      //   component: () => import('/@/views/cms/boShiHou/management/index.vue'),
      //   meta: {
      //     title: '高才博士后广告管理',
      //     isLink: '',
      //     isHide: false,
      //     isKeepAlive: true,
      //     isAffix: false,
      //     isIframe: false,
      //     icon: ''
      //   }
      // },
      // {
      //   path: '/cms/advertisingPositionList',
      //   name: 'cmsAdvertisingPositionList',
      //   component: () => import('/@/views/cms/advertising/positionManagement/index.vue'),
      //   meta: {
      //     title: '广告位管理',
      //     isLink: '',
      //     isHide: false,
      //     isKeepAlive: true,
      //     isAffix: false,
      //     isIframe: false,
      //     icon: ''
      //   }
      // },
      // {
      //   path: '/cms/newsList',
      //   name: 'cmsNewsList',
      //   component: () => import('/@/views/cms/news/list.vue'),
      //   meta: {
      //     title: '资讯管理',
      //     isLink: '',
      //     isHide: false,
      //     isKeepAlive: true,
      //     isAffix: false,
      //     isIframe: false,
      //     icon: ''
      //   }
      // },
      // {
      //   path: '/cms/newsAdd',
      //   name: 'cmsNewsAdd',
      //   component: () => import('/@/views/cms/news/add.vue'),
      //   meta: {
      //     title: '发布资讯',
      //     isLink: '',
      //     isHide: true,
      //     isKeepAlive: true,
      //     isAffix: false,
      //     isIframe: false,
      //     icon: ''
      //   }
      // },
      // {
      //   path: '/cms/newsEdit/:id',
      //   name: 'cmsNewsEdit',
      //   component: () => import('/@/views/cms/news/add.vue'),
      //   meta: {
      //     title: '编辑资讯',
      //     isLink: '',
      //     isHide: true,
      //     isKeepAlive: true,
      //     isAffix: false,
      //     isIframe: false,
      //     icon: ''
      //   }
      // },
      // {
      //   path: '/cms/topic',
      //   name: 'cmsTopic',
      //   component: () => import('/@/views/cms/topic/list.vue'),
      //   meta: {
      //     title: '话题管理',
      //     isLink: '',
      //     isHide: false,
      //     isKeepAlive: true,
      //     isAffix: false,
      //     isIframe: false,
      //     icon: ''
      //   }
      // },
      {
        path: '/cms/jobAuditList',
        name: 'cmsJobAuditList',
        component: () => import('/@/views/job/audit/index.vue'),
        meta: {
          title: '职位审核',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/jobList',
        name: 'cmsJobList',
        component: () => import('/@/views/cms/job/list/index.vue'),
        meta: {
          title: '职位查询',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      // {
      //   path: '/cms/jobAdd',
      //   name: 'cmsJobAdd',
      //   component: () => import('/@/views/cms/job/add/index.vue'),
      //   meta: {
      //     title: '发布职位',
      //     isLink: '',
      //     isHide: true,
      //     isKeepAlive: false,
      //     isAffix: false,
      //     isIframe: false,
      //     icon: ''
      //   }
      // },
      {
        path: '/cms/jobEdit/:id',
        name: 'cmsJobEdit',
        component: () => import('/@/views/cms/job/add/index.vue'),
        meta: {
          title: '编辑职位',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/jobAudit/:id',
        name: 'cmsJobAudit',
        component: () => import('/@/views/cms/job/audit/index.vue'),
        meta: {
          title: '职位审核',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/dailyAnnouncementSummaryList',
        name: 'cmsDailyAnnouncementSummaryList',
        component: () => import('/@/views/cms/dailyAnnouncementSummary/list.vue'),
        meta: {
          title: '每日汇总',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/dailyAnnouncementSummaryDetail/:id',
        name: 'cmsDailyAnnouncementSummaryDetail',
        component: () => import('/@/views/cms/dailyAnnouncementSummary/detail.vue'),
        meta: {
          title: '每日汇总详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/cmsRecycle',
        name: 'cmsRecycle',
        component: () => import('/@/views/cms/recycle/list.vue'),
        meta: {
          title: '回收站',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/suggestList',
        name: 'cmsSuggestList',
        component: () => import('/@/views/cms/suggest/list.vue'),
        meta: {
          title: '意见反馈',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/double-meeting/add',
        name: 'cmsDoubleMeetingAdd',
        component: () => import('/@/views/cms/doubleMeeting/add.vue'),
        meta: {
          title: '新增活动',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/double-meeting/:id',
        name: 'cmsDoubleMeetingEdit',
        component: () => import('/@/views/cms/doubleMeeting/add.vue'),
        meta: {
          title: '编辑活动',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/double-meeting',
        name: 'cmsDoubleMeeting',
        component: () => import('/@/views/cms/doubleMeeting/list.vue'),
        meta: {
          title: '双会报名管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
