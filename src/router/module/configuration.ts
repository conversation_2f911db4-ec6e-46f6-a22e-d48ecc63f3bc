// 运营管理
import { RouteRecordRaw } from 'vue-router'

export const configurationRoutes: Array<RouteRecordRaw> = [
  {
    path: '/configuration',
    name: 'configuration',
    component: () => import('/@/layout/routerView/parent.vue'),
    redirect: '/configuration/business',
    meta: {
      title: '运营管理',
      isLink: '',
      isHide: false,
      isKeepAlive: false,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/configuration.svg'
    },
    children: [
      {
        path: '/configuration/business',
        name: 'configurationBusiness',
        component: () => import('/@/views/configuration/business/index.vue'),
        meta: {
          title: '业务配置管理',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/packageLog',
        name: 'configurationPackageLog',
        component: () => import('/@/views/configuration/packageLog/index.vue'),
        meta: {
          title: '权益明细管理',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/order',
        name: 'configurationOrder',
        component: () => import('/@/views/configuration/order/index.vue'),
        meta: {
          title: '订单中心',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/order/:id',
        name: 'configurationOrderDetail',
        component: () => import('/@/views/configuration/order/detail.vue'),
        meta: {
          title: '订单详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/chat',
        name: 'configurationChat',
        component: () => import('/@/views/configuration/chat/index.vue'),
        meta: {
          title: '聊天记录查询',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/jobTop',
        name: 'configurationJobTopList',
        component: () => import('/@/views/configuration/jobTop/index.vue'),
        meta: {
          title: '置顶配置',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/invite',
        name: 'configurationInviteList',
        component: () => import('/@/views/configuration/invite/index.vue'),
        meta: {
          title: '投递邀约查询',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/companySource',
        name: 'configurationCompanySource',
        component: () => import('/@/views/configuration/companySource/index.vue'),
        meta: {
          title: '单位资源配置',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/resumeTemplateConfig',
        name: 'configurationResumeTemplateConfig',
        component: () => import('/@/views/configuration/resumeTemplateConfig/index.vue'),
        meta: {
          title: '简历模板管理',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/equityPackageConfig',
        name: 'configurationEquityPackageConfig',
        component: () => import('/@/views/configuration/equityPackageConfig/index.vue'),
        meta: {
          title: '权益套餐配置',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/agreementConfig',
        name: 'configurationAgreementConfig',
        component: () => import('/@/views/configuration/agreementConfig/index.vue'),
        meta: {
          title: '协议管理',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/dataExport',
        name: 'dataExport',
        component: () => import('/@/views/configuration/dataExport/index.vue'),
        meta: {
          title: '埋点数据导出',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/newResumeActivity',
        name: 'newResumeActivity',
        component: () => import('/@/views/configuration/newResumeActivity/index.vue'),
        meta: {
          title: '分享查询',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/setMeal',
        name: 'setMeal',
        component: () => import('/@/views/configuration/setMeal/index.vue'),
        meta: {
          title: '人才套餐配置',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/configuration/deliveryLimit',
        name: 'deliveryLimit',
        component: () => import('/@/views/configuration/deliveryLimit/index.vue'),
        meta: {
          title: ' 投递限制管理',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
