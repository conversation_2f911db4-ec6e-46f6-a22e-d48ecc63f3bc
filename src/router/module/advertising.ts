import { RouteRecordRaw } from 'vue-router'

export const advertisingRoutes: Array<RouteRecordRaw> = [
  {
    path: '/advertising',
    name: 'advertising',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '广告管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/cms.svg'
    },
    children: [
      {
        path: '/cms/advertisingList',
        name: 'cmsAdvertisingList',
        component: () => import('/@/views/cms/advertising/management/index.vue'),
        meta: {
          title: '广告查询',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/miniAdvertisingList',
        name: 'cmsMiniAdvertisingList',
        component: () => import('/@/views/cms/miniAdvertising/management/index.vue'),
        meta: {
          title: '小程序广告管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/boShiHouList',
        name: 'cmsBoShiHouList',
        component: () => import('/@/views/cms/boShiHou/management/index.vue'),
        meta: {
          title: '高才博士后广告管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/abroad/advertisingList',
        name: 'abroadAdvertising',
        component: () => import('/@/views/abroad/advertising/index.vue'),
        meta: {
          title: '高才海外广告管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/cms/advertisingPositionList',
        name: 'cmsAdvertisingPositionList',
        component: () => import('/@/views/cms/advertising/positionManagement/index.vue'),
        meta: {
          title: '广告位管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
