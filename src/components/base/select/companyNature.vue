<!-- 单位性质 -->
<template>
  <el-select class="w100" :placeholder="placeholder" clearable :multiple="multiple">
    <el-option v-for="item in list" :key="item.k" :label="item.v" :value="item.k"> </el-option>
  </el-select>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getCompanyNatureList } from '/@/api/config'
import { useStore } from '/@/store/index'

export default {
  name: 'companyNatureSelect',
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const state = reactive({
      list: <any>[]
    })

    const getList = async () => {
      const store = useStore()
      const { selectList } = store.state.selectList
      if (selectList.companyNatureList.length > 0) {
        state.list = selectList.companyNatureList
      } else {
        await getCompanyNatureList().then((resp: any) => {
          state.list = resp
          selectList.companyNatureList = resp
        })
      }
    }
    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
