<!-- 专业类型/学科专业 -->
<template>
  <el-cascader
    class="w100"
    :size="size"
    :options="list"
    :props="{
      value: 'k',
      label: 'v',
      emitPath: false,
      multiple: multiple,
      checkStrictly: checkStrictly
    }"
    collapse-tags
    collapse-tags-tooltip
    :placeholder="placeholder"
    :show-all-levels="showAllLevels"
    clearable
    :filterable="filter"
  ></el-cascader>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import { getMajorList, getSecondMajorList } from '/@/api/config.ts'
import { useStore } from '/@/store/index'

export default {
  name: 'majorCategory',
  props: {
    showAllLevels: {
      type: Boolean,
      default: () => false
    },
    filter: {
      type: Boolean,
      default: () => true
    },
    placeholder: {
      type: String,
      default: () => '请选择学科专业'
    },
    deep: {
      type: Number,
      default: () => 3
    },
    multiple: {
      type: <PERSON>olean,
      default: () => true
    },
    checkStrictly: {
      type: Boolean,
      default: () => false
    },
    size: {
      type: String,
      default: 'default'
    }
    // isLimit: {
    //   type: Boolean,
    //   default: () => false
    // }
  },
  setup(props: any) {
    const store = useStore()
    const { selectList } = store.state.selectList

    const state = reactive({
      list: <any>[]
    })

    const getFullList = async () => {
      if (selectList.majorList.length > 0) {
        state.list = selectList.majorList
      } else {
        await getMajorList().then((resp: any) => {
          selectList.majorList = resp
          state.list = resp
        })
      }
      // if (props.isLimit && !state.list.filter((i) => i.k === '-1').length) {
      //   state.list.unshift({
      //     k: '-1',
      //     v: '不限'
      //   })
      // }
    }

    const getSecondList = async () => {
      if (selectList.majorSecondList.length > 0) {
        state.list = selectList.majorSecondList
      } else {
        await getSecondMajorList().then((resp: any) => {
          selectList.majorSecondList = resp
          state.list = resp
        })
      }
      // if (props.isLimit && !state.list.filter((i) => i.k === '-1').length) {
      //   state.list.push({
      //     k: '-1',
      //     v: '专业不限'
      //   })
      // }
    }

    onMounted(() => {
      if (props.deep === 2) {
        getSecondList()
      } else {
        getFullList()
      }
    })

    return {
      ...toRefs(state)
    }
  }
}
</script>
