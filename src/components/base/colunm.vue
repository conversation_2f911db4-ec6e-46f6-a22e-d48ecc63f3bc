<template>
  <!-- 所属栏目 -->
  <div class="full-width">
    <el-cascader
      class="full-width"
      v-model="columnId"
      :options="columnList"
      :collapse-tags="collapseTags"
      :props="{ label: 'v', value: 'k', checkStrictly: check, emitPath, multiple }"
      :filterable="filter"
      @change="changeColumnId"
      clearable
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'

export default defineComponent({
  name: 'colunm',

  components: {},
  props: {
    modelValue: {
      type: String,
      default: () => ''
    },
    columnList: {
      type: Array,
      default: () => []
    },
    filter: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    emitPath: {
      type: Boolean,
      default: false
    },
    check: {
      type: Boolean,
      default: true
    },
    collapseTags: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const state = reactive({
      columnId: ''
    })
    watch(
      () => props.modelValue,
      (val: any) => {
        state.columnId = val
      },
      { deep: true }
    )
    const changeColumnId = (val: any) => {
      emit('update:modelValue', val)
    }
    return { ...toRefs(state), changeColumnId }
  }
})
</script>

<style lang="scss" scoped>
:deep(.full-width) {
  width: 100%;
}
.full-width {
  width: 100%;
}
</style>
