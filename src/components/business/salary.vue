<!-- 薪资 -->
<template>
  <el-form-item label="薪资范围" prop="wageType" :rules="rules.wageType">
    <el-row :gutter="10" style="margin-left: 0">
      <div class="flex-1">
        <WageType v-model="formData.wageType" placeholder="请选择" />
      </div>
      <!-- 月薪/日薪-->
      <div
        class="flex mx-10"
        :class="[formData.isNegotiable == 1 ? 'flex-3' : 'flex-2']"
        v-if="formData.wageType != '2'"
      >
        <div class="flex-1">
          <!-- 是否自定义薪资：2否，1是 -->
          <div v-if="formData.isNegotiable != 1">
            <el-form-item prop="wageId" :rules="rules.wageId">
              <ExpectSalary v-model="formData.wageId" />
            </el-form-item>
          </div>
          <div v-else class="ai-center">
            <el-form-item prop="minWage" :rules="rules.minSalary">
              <el-input v-model.trim="formData.minSalary" placeholder="最低"></el-input>
            </el-form-item>
            <div class="line mx-3">-</div>
            <el-form-item prop="maxWage" :rules="rules.maxSalary">
              <el-input v-model.trim="formData.maxSalary" placeholder="最高"></el-input>
            </el-form-item>
          </div>
        </div>
        <div
          class="shrink-0 w-70 color-primary fs-13 cursor-pointer select-none ta-right"
          @click="handleCustomChange"
        >
          {{ formData.isNegotiable == '1' ? '取消自定义' : '自定义薪资' }}
        </div>
      </div>
      <!-- 年薪 -->
      <div
        class="flex mx-10 annual"
        :class="[formData.isNegotiable == 1 ? 'flex-3' : 'flex-2']"
        v-else
      >
        <div v-if="formData.isNegotiable != 1" class="ai-center flex-1">
          <el-form-item prop="annualSalary" :rules="rules.annualSalary" class="flex-1">
            <el-input placeholder="请输入年薪" v-model.trim="formData.annualSalary"></el-input>
          </el-form-item>
          <div class="mx-2">万</div>
        </div>
        <div v-else class="ai-center flex-1">
          <el-form-item prop="minWage" :rules="rules.minAnnualSalary" class="flex-1">
            <el-input v-model.trim="formData.minAnnualSalary" placeholder="最低"></el-input>
          </el-form-item>
          <div class="ml-2">万</div>
          <div class="line mx-3">-</div>
          <el-form-item prop="maxWage" :rules="rules.maxAnnualSalary" class="flex-1">
            <el-input v-model.trim="formData.maxAnnualSalary" placeholder="最高"></el-input>
          </el-form-item>
          <div class="mx-2">万</div>
        </div>
        <div class="switch" @click="handleCustomChange">
          {{ formData.isNegotiable == '1' ? '取消录入范围' : '录入范围' }}
        </div>
      </div>
    </el-row>
    <div v-show="false">
      <el-form-item prop="minWage">
        <el-input v-model.trim="formData.minWage" placeholder="最低"></el-input>
      </el-form-item>
      <div class="line mx-3">-</div>
      <el-form-item prop="maxWage">
        <el-input v-model.trim="formData.maxWage" placeholder="最高"></el-input>
      </el-form-item>
    </div>
  </el-form-item>
</template>
<script lang="ts">
import { defineComponent, onMounted, ref, reactive, toRefs, watch, computed } from 'vue'

import { verifyNumberIntegerAndFloat } from '/@/utils/toolsValidate'

import WageType from '/@select/wageType.vue'
import ExpectSalary from '/@select/expectSalary.vue'

export default defineComponent({
  name: 'salary',
  props: {
    data: {
      type: Object,
      default: () => {
        return {
          wageType: '',
          wageId: '',
          minWage: '',
          maxWage: '',
          isNegotiable: ''
        }
      }
    },
    required: {
      type: Boolean,
      default: () => false
    }
  },
  emits: ['change'],
  components: { WageType, ExpectSalary },
  setup(props: any, { emit }) {
    const state = reactive({
      isEmitChange: false,

      formData: <any>{
        annualSalary: '',
        minAnnualSalary: '',
        maxAnnualSalary: '',
        /**
         * 1月，2年，3日
         */
        wageType: '',
        wageId: '',
        minSalary: '',
        maxSalary: '',
        /**
         * 1自定义(范围)，2下拉(或年固定值)
         */
        isNegotiable: 2,
        // 用于重置表单
        minWage: '',
        maxWage: ''
      },
      isRequired: computed(() => props.required)
    })

    // 最低薪资校验
    const validateMinSalary = (rule, value, callback) => {
      if (!state.isRequired) {
        return callback()
      }

      const { minSalary, maxSalary } = state.formData
      state.formData.minSalary = verifyNumberIntegerAndFloat(minSalary.toString())
      if (!minSalary) {
        return callback(new Error('最低薪资'))
      }
      if (maxSalary && +maxSalary < +minSalary) {
        return callback(new Error('高于最高薪资'))
      }
      return callback()
    }

    // 最高薪资校验
    const validateMaxSalary = (rule, value, callback) => {
      if (!state.isRequired) {
        return callback()
      }

      const { minSalary, maxSalary } = state.formData
      state.formData.maxSalary = verifyNumberIntegerAndFloat(maxSalary.toString())
      if (!maxSalary) {
        return callback(new Error('最高薪资'))
      }
      if (minSalary && +maxSalary < +minSalary) {
        return callback(new Error('低于最低薪资'))
      }
      return callback()
    }

    // 年薪校验
    const validateAnnualSalary = (rule, value, callback) => {
      if (!state.isRequired) {
        return callback()
      }
      const { annualSalary } = state.formData
      state.formData.annualSalary = verifyNumberIntegerAndFloat(annualSalary.toString())
      if (!annualSalary) {
        return callback(new Error('填写年薪'))
      }
      return callback()
    }

    // 最低年薪校验
    const validateMinAnnuslSalary = (rule, value, callback) => {
      if (!state.isRequired) {
        return callback()
      }

      const { minAnnualSalary, maxAnnualSalary } = state.formData
      state.formData.minAnnualSalary = verifyNumberIntegerAndFloat(minAnnualSalary.toString())
      if (!minAnnualSalary) {
        return callback(new Error('最低年薪'))
      }
      if (maxAnnualSalary && +maxAnnualSalary < +minAnnualSalary) {
        return callback(new Error('高于最高年薪'))
      }
      return callback()
    }

    // 最高年薪校验
    const validateMaxAnnuslSalary = (rule, value, callback) => {
      if (!state.isRequired) {
        return callback()
      }

      const { minAnnualSalary, maxAnnualSalary } = state.formData
      state.formData.maxAnnualSalary = verifyNumberIntegerAndFloat(maxAnnualSalary.toString())
      if (!maxAnnualSalary) {
        return callback(new Error('最高年薪'))
      }
      if (minAnnualSalary && +maxAnnualSalary < +minAnnualSalary) {
        return callback(new Error('低于最低年薪'))
      }
      return callback()
    }

    const rules = ref({
      wageType: [
        {
          required: state.isRequired,
          message: '请选择薪资类型',
          trigger: 'change'
        }
      ],
      wageId: [
        {
          required: state.isRequired,
          message: '请选择你的薪资',
          trigger: 'change'
        }
      ],
      minSalary: [
        {
          validator: validateMinSalary,
          trigger: ['change', 'blur']
        }
      ],
      maxSalary: [
        {
          validator: validateMaxSalary,
          trigger: ['change', 'blur']
        }
      ],
      annualSalary: [
        {
          validator: validateAnnualSalary,
          trigger: ['change', 'blur']
        }
      ],
      minAnnualSalary: [
        {
          validator: validateMinAnnuslSalary,
          trigger: ['change', 'blur']
        }
      ],
      maxAnnualSalary: [
        {
          required: state.isRequired,
          validator: validateMaxAnnuslSalary,
          trigger: ['change', 'blur']
        }
      ]
    })

    watch(
      () => props.data,
      (value: any) => {
        const { minWage, maxWage, wageType = '', wageId = '', isNegotiable } = value

        Object.assign(state.formData, value)
        // 年薪
        if (/2/.test(wageType)) {
          if (/1/.test(isNegotiable)) {
            state.formData.minAnnualSalary = +minWage / 10000 || ''
            state.formData.maxAnnualSalary = +maxWage / 10000 || ''
          } else {
            state.formData.annualSalary = +maxWage / 10000 || +minWage / 10000 || ''
          }
        } else if (/1/.test(isNegotiable)) {
          state.formData.minSalary = +minWage || ''
          state.formData.maxSalary = +maxWage || ''
        } else {
          state.formData.minSalary = ''
          state.formData.maxSalary = ''
        }

        // 清空
        const flag = !(wageType || wageId || minWage || maxWage)
        if (flag) {
          Object.assign(state.formData, value)
        }
      },
      {
        deep: true,
        immediate: true
      }
    )

    const handleEmitData = (data) => {
      const {
        isNegotiable,
        wageType,
        minAnnualSalary,
        maxAnnualSalary,
        annualSalary,
        wageId,
        minSalary,
        maxSalary
      } = data
      const emitData = <any>{
        isNegotiable,
        wageType,
        wageId
      }
      if (/2/.test(wageType)) {
        if (/1/.test(isNegotiable)) {
          emitData.minWage = +minAnnualSalary * 10000 || ''
          emitData.maxWage = +maxAnnualSalary * 10000 || ''
        } else {
          emitData.minWage = +annualSalary * 10000 || ''
          emitData.maxWage = +annualSalary * 10000 || ''
        }
      } else if (/1/.test(isNegotiable)) {
        emitData.minWage = +minSalary || ''
        emitData.maxWage = +maxSalary || ''
        // 范围值需清空薪资下拉值
        emitData.wageId = ''
      } else {
        // 非范围值需清空薪资
        emitData.minWage = ''
        emitData.maxWage = ''
      }
      return emitData
    }

    watch(
      () => state.formData,
      (value) => {
        emit('change', handleEmitData(value))
      },
      {
        deep: true
      }
    )

    onMounted(() => {})

    const handleCustomChange = () => {
      const flag = /1/.test(state.formData.isNegotiable)
      state.formData.isNegotiable = flag ? '2' : '1'
    }

    return {
      rules,
      handleCustomChange,
      ...toRefs(state)
    }
  }
})
</script>
<style lang="scss" scoped>
.annual {
  .switch {
    flex-shrink: 0;
    color: var(--color-primary);
    font-size: 13px;
    cursor: pointer;
    user-select: none;
    text-align: right;
  }
}
</style>
