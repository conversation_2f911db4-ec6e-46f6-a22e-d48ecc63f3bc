<template>
  <div class="editor-container" v-loading="loading">
    <el-upload
      v-show="false"
      :on-success="uploadSuccess"
      :on-progress="uploadLoading"
      action="upload/file"
      :on-error="uploadError"
    >
      <span ref="uploadRef"></span>
    </el-upload>

    <div ref="editorRef" class="flex-1"></div>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, ref, unref, onMounted } from 'vue'
import { watchOnce } from '@vueuse/shared'

import { ElMessage } from 'element-plus'

import WangEditor from 'wangeditor'
import CustomUploadFile from './extendEditor'

export default {
  name: 'wangEditor',

  props: {
    modelValue: {
      type: String,
      required: true
    },
    showCustomUploadFile: {
      type: Boolean,
      default: false
    },
    height: {
      type: Number,
      default: 300
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    }
  },

  setup(props, { emit }) {
    const uploadRef = ref()
    const editorRef = ref()

    const state: any = reactive({
      loading: false,

      editorInstance: null,

      initEditor() {
        if (props.showCustomUploadFile) {
          // 注册菜单
          WangEditor.registerMenu('customUploadFile', CustomUploadFile)
        }

        state.editorInstance = new WangEditor(unref(editorRef))

        state.editorInstance.config = {
          ...state.editorInstance.config,
          pasteFilterStyle: false,
          placeholder: props.placeholder,
          uploadImgServer: '/upload/editor-image',
          pasteIgnoreImg: true,
          height: props.height
        }

        state.editorInstance.config.customUpload = () => {
          unref(uploadRef).click()
        }

        state.editorInstance.config.onchange = (html: string) => {
          emit('update:modelValue', html)
        }

        state.editorInstance.create()
      },

      updateEditor(val) {
        state.editorInstance.txt.html(val)
      },

      clearEditor() {
        state.updateEditor('')
      },

      uploadSuccess(val: any) {
        if (val.msg.length) {
          ElMessage.error(val.msg)
          return
        }

        const { data } = val
        const fileFullPath = `<a href="${data.fullUrl}">${data.name}</a>`

        state.loading = false
        ElMessage.success('上传成功')
        state.editorInstance.txt.append(fileFullPath)
      },

      uploadLoading(uploadProgressEvent: any) {
        if (uploadProgressEvent.percent !== 100) state.loading = true
      },

      uploadError(error: any) {
        ElMessage.error(error)
      }
    })

    watchOnce(
      () => props.modelValue,
      (val) => {
        if (val === '<p><br/></p>') return
        state.editorInstance.txt.html(val)
      }
    )

    onMounted(() => {
      state.initEditor()
      // 以下这段代码复制有图片的word文件，图片显示异常，故注释，还是通过css处理吧
      // 粘贴处理逻辑，复制进去的内容如果有a标签，但是没有href属性，则添加href属性，禅道story#1210,富文本框a标签识别需求
      // setTimeout(() => {
      //   // 获取编辑区真实 DOM
      //   const editorDom = unref(editorRef)
      //   if (!editorDom) return
      //   // wangEditor v4 会在 editorDom 下生成 .w-e-text 区域
      //   const textArea = editorDom.querySelector('.w-e-text')
      //   if (!textArea) return
      //   textArea.addEventListener(
      //     'paste',
      //     (e: ClipboardEvent) => {
      //       // 立即阻止默认行为
      //       e.preventDefault()
      //       e.stopPropagation()

      //       if (!e.clipboardData) return
      //       const html = e.clipboardData.getData('text/html')
      //       if (!html) return

      //       // 解析 html
      //       const tempDiv = document.createElement('div')
      //       tempDiv.innerHTML = html
      //       // 遍历所有 a 标签
      //       tempDiv.querySelectorAll('a').forEach((a) => {
      //         if (!a.hasAttribute('href')) {
      //           a.setAttribute('href', '')
      //         }
      //       })

      //       // 插入处理后的内容
      //       state.editorInstance.cmd.do('insertHTML', tempDiv.innerHTML)
      //     },
      //     true
      //   ) // 使用捕获阶段确保优先处理
      // }, 100)
    })

    return { ...toRefs(state), uploadRef, editorRef } as any
  }
}
</script>

<style lang="scss">
.editor-container {
  .w-e-text-container {
    a {
      color: rgb(0, 14, 229) !important;
      text-decoration: underline;
    }
  }
}
</style>
