<template>
  <div>
    <div class="box" v-loading="loading">
      <div class="flex mb-15" v-if="info.announcementId != 0">
        关联公告：
        <router-link
          :to="`/cms/announcementDetail/${info.announcementId}/${info.announcementStatus}`"
          >{{ info.announcementTitle }}</router-link
        >
      </div>
      <!-- 没有审核通过历史的职位详情 -->
      <div v-if="info.historyStatus != 1">
        <div class="p-20 pt-0 border-bottom">
          <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">基本信息</div>
          <div class="flex mb-10">
            <div class="span-3 pr-20">职位名称：{{ info.name }}</div>
            <div class="span-3 pr-20">职位类型：{{ info.jobCategoryTitle }}</div>
            <div class="span-3 pr-20">用人部门：{{ info.department }}</div>
          </div>
          <div class="flex mb-10">
            <div class="span-3 pr-20">截止日期：{{ info.periodDate }}</div>
            <div class="span-3 pr-20">薪资待遇：{{ info.wage }}</div>
            <div class="span-3 pr-20">福利待遇：{{ info.welfareTagTitle }}</div>
          </div>
          <div class="flex mb-10">
            <div class="span-3 pr-20">工作性质：{{ info.natureTypeTitle }}</div>
            <div class="span-3 pr-20">招聘人数：{{ info.amount }}</div>
            <div class="span-3 pr-20">
              工作地点：{{ info.provinceTitle }}{{ info.cityTitle }}{{ info.districtTitle
              }}{{ info.address }}
            </div>
          </div>
          <div class="flex mb-10">
            <div class="span-3 pr-20">编制类型：{{ info.establishmentTxt }}</div>
          </div>
          <div class="fw-bold mb-10 color-dark">其他要求</div>
          <div class="flex mb-10">
            <div class="span-3 pr-20">海外经历：{{ info.abroadTypeTitle }}</div>

            <div class="span-3 pr-20">学历要求：{{ info.educationTypeTitle }}</div>
            <div class="span-3 pr-20">职称要求：{{ info.titleTypeTitle }}</div>
          </div>
          <div class="flex mb-10">
            <div class="span-3 pr-20">工作经验：{{ info.experienceTypeTitle }}</div>
            <div class="span-3 pr-20">年龄要求：{{ info.ageType }}</div>
            <!-- <div class="span-3">
              <span v-if="info.genderType === ''">性别要求：不限</span>
              <span v-if="info.genderType === '0'">性别要求：不限</span>
              <span v-if="info.genderType === '1'">性别要求：男</span>
              <span v-if="info.genderType === '2'">性别要求：女</span>
            </div> -->
            <div class="span-3 pr-20">政治面貌：{{ info.politicalTypeTitle }}</div>
          </div>
          <div class="flex">
            <div class="span-3 pr-20">专业要求：{{ info.majorTitle }}</div>
            <div class="span-3 pr-20">投递限制：{{ info.deliveryLimitTypeTxt }}</div>
            <div class="span-3 pr-20">投递类型：{{ info.deliveryTypeTxt }}</div>
          </div>
        </div>
        <div class="p-20 border-bottom">
          <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">岗位职责</div>
          <div>{{ info.duty }}</div>
        </div>
        <div class="p-20 border-bottom">
          <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">任职要求</div>
          <div>{{ info.requirement }}</div>
        </div>
        <div class="p-20 border-bottom">
          <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">其他说明</div>
          <div>{{ info.remark }}</div>
        </div>
        <div class="p-20 border-bottom" v-if="info.companyDeliveryType === '2'">
          <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">投递通知邮箱</div>
          <div>{{ info.extraNotifyAddress }}</div>
        </div>
        <div class="p-20 border-bottom">
          <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">报名方式</div>
          <div>报名方式：{{ info.applyTypeTxt }}</div>
          <div>投递地址：{{ info.applyAddress }}</div>
        </div>

        <div class="p-20 border-bottom" v-if="info.isCooperation === 1">
          <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">协同子账号</div>
          <table class="info" border="1" v-if="info.jobContactSynergy?.length">
            <tr>
              <td class="table-title">账号ID</td>
              <td class="table-title">姓名</td>
              <td class="table-title">所在部门</td>
              <td class="table-title">邮箱</td>
              <td class="table-title">手机号</td>
            </tr>
            <tr v-for="item in info.jobContactSynergy" :key="item.id">
              <td>{{ item.companyMemberInfoId }}</td>
              <td>{{ item.contact }}</td>
              <td>{{ item.department }}</td>
              <td>{{ item.email }}</td>
              <td>{{ item.mobile }}</td>
            </tr>
          </table>
        </div>

        <div class="p-20 border-bottom" v-if="info.isCooperation === 1">
          <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">职位联系人</div>
          <div>
            <span class="mr-5">{{ info.jobContact?.companyMemberType === '0' ? '主' : '子' }}</span>
            <span>{{ info.jobContact?.contact }}</span>
            <span>/{{ info.jobContact?.department }}</span>
          </div>
          <div>
            <span v-if="info.jobContact?.email">{{ info.jobContact?.email }}</span>
            <span v-if="info.jobContact?.mobile">{{ info.jobContact?.mobile }}</span>
          </div>
        </div>

        <div id="job-attachment" class="p-20 border-bottom" v-if="info.fileList?.length">
          <JobAttachment :fileList="info.fileList" />
        </div>
      </div>

      <!-- 有审核记录的职位详情 -->
      <div v-else>
        <div class="p-20 border-bottom">
          <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">修改前</div>
          <div class="mb-15" v-for="(v, k, i) in info.before" :key="i">
            <span class="fw-bold mb-5 color-dark">{{ k }}：</span>
            <span>{{ v }}</span>
          </div>
          <span class="fw-bold mb-5 color-dark" v-if="beforeFileList">职位附件：</span>
          <div class="mb-15" v-for="item in beforeFileList as any" :key="item.id">
            <span>{{ item.name }}</span>
          </div>
        </div>
        <div class="p-20 border-bottom">
          <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">修改后</div>
          <div class="mb-15" v-for="(v, k, i) in info.after" :key="i">
            <span class="fw-bold mb-5 color-dark">{{ k }}：</span>
            <span>{{ v }}</span>
          </div>
          <span class="fw-bold mb-15 color-dark" v-if="afterFileList">职位附件：</span>
          <div class="mb-15" v-for="item in afterFileList as any" :key="item.id">
            <a :href="item.path" :download="item.name" target="_blank">{{ item.name }}</a>
          </div>
        </div>
      </div>

      <div class="p-20">
        <div class="fw-bold mb-15 color-dark title fs-16 pl-15 line-1">审核处理意见</div>
        <el-input
          type="textarea"
          rows="5"
          resize="none"
          v-model="formData.opinion"
          placeholder="审核拒绝须明确拒绝原因"
        ></el-input>
        <div class="mt-20">
          <el-button :disabled="disabled" type="primary" @click="handleAudit(1)"
            >审核通过</el-button
          >
          <el-button :disabled="disabled" type="primary" @click="handleAudit(-1)"
            >审核拒绝</el-button
          >
        </div>
      </div>
      <div class="pl-20 pr-20">
        <div>
          <el-button @click="showRecord = !showRecord">审核处理历史</el-button>
        </div>
        <div v-show="showRecord">
          <el-table border :data="list" size="small" class="mt-15">
            <el-table-column
              prop="handlerName"
              align="center"
              header-align="center"
              label="审核人"
            ></el-table-column>
            <el-table-column
              prop="addTime"
              align="center"
              header-align="center"
              label="审核时间"
              min-width="120px"
            ></el-table-column>
            <el-table-column
              prop="auditStatusTitle"
              align="center"
              header-align="center"
              label="审核状态"
            ></el-table-column>
            <el-table-column
              prop="opinion"
              align="center"
              header-align="center"
              min-width="150px"
              label="处理意见"
              show-overflow-tooltip
            ></el-table-column>
            <template #empty>
              <el-empty description="暂无记录" :image-size="100"></el-empty>
            </template>
          </el-table>
          <Pagination
            v-if="list.length"
            @change="handlePaginationChange"
            class="mt-15"
            :total="pagination.total"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, toRefs, onMounted, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '/@/components/base/paging.vue'
import { getJobAuditInfo, getJobAuditHandleList, jobExamine } from '/@/api/job'
import JobAttachment from '/@/views/cms/announcement/component/jobAttachment.vue'

const { proxy } = getCurrentInstance() as any
const route = useRoute()
const router = useRouter()

const state = reactive({
  loading: false,
  disabled: false,
  id: '',
  showRecord: false,
  info: <any>{},
  pagination: {
    total: 0,
    page: 1,
    limit: 20
  },
  formData: {
    opinion: ''
  },
  list: [],
  jobAttachmentList: [],
  beforeFileList: [],
  afterFileList: []
})

function getInfo() {
  state.loading = true
  getJobAuditInfo({ jobId: state.id }).then((resp: any) => {
    // 1有审核通过记录   2无审核通过记录
    if (resp.historyStatus === 2) {
      const info = <any>{}
      Object.keys(resp.data).forEach((key: any) => {
        info[key] = resp.data[key] || '-'
      })
      state.info = {
        ...info,
        historyStatus: resp.historyStatus
      }
    } else if (resp.historyStatus === 1) {
      const { fileList: afterFileList, ...handleAfter } = resp.data.handleAfter
      const { fileList: beforeFileList, ...handleBefore } = resp.data.handleBefore
      state.info = {
        after: { ...handleAfter },
        before: { ...handleBefore },
        historyStatus: resp.historyStatus
      }
      state.beforeFileList = beforeFileList
      state.afterFileList = afterFileList
    }
    state.loading = false
  })
}

function getList() {
  getJobAuditHandleList({
    jobId: state.id,
    limit: state.pagination.limit,
    page: state.pagination.page
  }).then((resp: any) => {
    state.list = resp.list
    state.pagination.total = Number(resp.page.count)
  })
}

onMounted(() => {
  if (route.params.id) {
    state.id = String(route.params.id)
    getInfo()
    getList()
  }
})

function handleAudit(status: any) {
  // if (status === -1 && !state.formData.opinion) {
  //   ElMessage.error('请填写审核处理意见')
  //   return
  // }
  jobExamine({
    ...state.formData,
    id: state.id,
    auditStatus: status
  })
    .then(() => {
      state.disabled = true
      ElMessageBox({
        title: '提示',
        message: '操作成功',
        confirmButtonText: '我知道了',
        center: true,
        callback: () => {
          proxy.mittBus.emit('closeCurrentViewTag')
          router.push('/job/audit')
        }
      })
    })
    .catch(() => {
      state.disabled = false
    })
}

function handlePaginationChange(data: any) {
  state.pagination.limit = data.limit
  state.pagination.page = data.page
  getList()
}

const {
  loading,
  disabled,
  showRecord,
  info,
  pagination,
  formData,
  list,
  beforeFileList,
  afterFileList
} = toRefs(state)
</script>

<style scoped lang="scss">
.title {
  margin-left: -18px;
  // margin-top: 15px;
  border-left: 2px solid var(--color-primary);
}

.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  a {
    text-decoration: none;
    color: var(--color-primary);
  }
  .border-bottom {
    border-bottom: 1px solid #efefef;
  }

  .info {
    margin: 20px 0;
    color: #606266;
    border-collapse: collapse;
    border-color: rgba(#909399, 0.3);

    td {
      padding: 10px;
      width: 250px;
    }

    .table-title {
      background-color: #f3f6f9;
      width: 120px;
    }
  }

  #job-attachment {
    :deep() {
      .fw-bold {
        line-height: 1;
        padding-left: 16px;
        font-size: 16px;
        margin-top: 0px !important;
        margin-left: -18px;
        border-left: 2px solid var(--color-primary);
      }
      .file-list {
        a {
          margin-left: 0;
        }
      }
    }
  }
}
</style>
