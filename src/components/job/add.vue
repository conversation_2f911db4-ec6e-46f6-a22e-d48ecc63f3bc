<template>
  <div>
    <div class="box" v-loading="loading">
      <el-form class="flex" ref="form" :rules="formRules" :model="formData" label-width="100px">
        <div class="left flex-2">
          <el-row v-show="!isEdit" :gutter="10" class="pl-20 pr-40">
            <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
              <el-form-item label="添加方式">
                <el-radio-group v-model="addType">
                  <el-radio class="mr-10" border :label="1">单个新增职位</el-radio>
                  <el-radio border :label="2">批量新增职位</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-show="addType == 1" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
              <el-form-item label="职位模板">
                <el-select
                  class="block"
                  v-model="templateId"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请选择职位模板"
                  :remote-method="getTemplate"
                  @change="templateChange"
                  :loading="remoteLoading"
                  @focus="templateFocus"
                  clearable
                >
                  <el-option
                    v-for="item in templateListOption"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div v-if="addType == 1">
            <div class="title line-1 mb-20 fs-16 fw-bold pl-15">基本信息</div>
            <div class="pl-20 pr-40" style="overflow: hidden">
              <el-row :gutter="10" v-if="formData.announcementId != 0 && isEdit">
                <el-col :span="24">
                  <el-form-item label="所属公告" prop="tile">
                    <div class="to-1" style="color: var(--color-primary)">
                      {{ formData.announcementTitle }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-row :gutter="10" align="middle">
                    <el-col :span="24">
                      <el-form-item label="职位名称" prop="name">
                        <el-input
                          type="input"
                          v-model="formData.name"
                          show-word-limit
                          resize="none"
                          clearable
                          placeholder="请填写职位名称"
                        ></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="职位类型" prop="jobCategoryId">
                    <JobCategory :multiple="false" v-model="formData.jobCategoryId" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="学历要求" prop="educationType">
                    <el-row :gutter="10">
                      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                        <Education v-model="formData.educationType" />
                      </el-col>
                      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                        <el-form-item label-width="0px" prop="code">
                          <el-input
                            type="input"
                            v-model="formData.code"
                            resize="none"
                            clearable
                            placeholder="职位代码:非必填"
                          ></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="需求专业" prop="majorId">
                    <el-row :gutter="10">
                      <el-col :span="10">
                        <MajorDialog
                          v-model="formData.majorId"
                          v-model:title="formData.majorTitle"
                        />
                        <!-- <MajorCategory :deep="2" v-model="formData.majorId" /> -->
                      </el-col>
                      <el-col :span="10">
                        <el-input
                          v-model="majorText"
                          placeholder="输入识别文案"
                          clearable
                          maxlength="2000"
                        />
                      </el-col>
                      <el-col :span="4">
                        <el-button type="primary" @click="recognition">识别</el-button>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="工作性质" prop="natureType">
                    <WorkNature v-model="formData.natureType" placeholder="请选择" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <Salary
                    :data="{
                      wageType: formData.wageType,
                      wageId: formData.wageId,
                      minWage: formData.minWage,
                      maxWage: formData.maxWage,
                      isNegotiable: formData.isNegotiable
                    }"
                    @change="salarChage"
                  />
                </el-col>
              </el-row>
              <el-form-item label="其他要求">
                <div class="flex">
                  <div class="flex-1 mr-10">
                    <el-form-item label-width="0px" prop="experienceType">
                      <Experience
                        v-model="formData.experienceType"
                        placeholder="请选择经验"
                        is-limit
                      />
                    </el-form-item>
                  </div>
                  <div class="flex-1 mr-10">
                    <el-form-item label-width="0px" prop="ageType">
                      <Age v-model="formData.ageType" placeholder="请选择年龄" is-limit />
                    </el-form-item>
                  </div>
                  <div class="flex-1 mr-10">
                    <el-form-item label-width="0px" prop="titleType">
                      <LevelTitle v-model="formData.titleType" placeholder="请选择职称" is-limit />
                    </el-form-item>
                  </div>
                  <div class="flex-1 mr-10">
                    <el-form-item label-width="0px" prop="politicalType">
                      <Political
                        v-model="formData.politicalType"
                        placeholder="请选择政治面貌"
                        is-limit
                      />
                    </el-form-item>
                  </div>
                  <div class="flex-1">
                    <el-form-item label-width="0px" prop="abroadType">
                      <AbroadExperience
                        v-model="formData.abroadType"
                        placeholder="请选择海外经历"
                        is-limit
                      />
                    </el-form-item>
                  </div>
                </div>
              </el-form-item>
            </div>
            <div class="title line-1 mb-20 fs-16 fw-bold pl-15">职位详情</div>
            <div class="pl-20 pr-40">
              <el-row :gutter="10">
                <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="招聘人数" prop="amount">
                    <el-input
                      v-model="formData.amount"
                      clearable
                      placeholder="请输入招聘人数"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="工作地点" prop="provinceId">
                    <!-- <Address
                      :address="{
                        provinceId: formData.provinceId,
                        cityId: formData.cityId,
                        districtId: formData.districtId,
                        areaName: formData.areaName,
                        address: formData.address
                      }"
                      :member-id="companyInfo.memberId"
                      @confirm="handleAddress"
                    /> -->
                    <el-row :gutter="10">
                      <el-col :span="8">
                        <Region
                          v-model="regionValue"
                          v-model:province="formData.provinceId"
                          v-model:city="formData.cityId"
                      /></el-col>
                      <el-col :span="16">
                        <el-form-item label-width="0px" prop="address">
                          <el-input
                            v-model="formData.address"
                            placeholder="请输入详细地址(非必填)"
                          ></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10">
                <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="职位福利">
                    <el-input
                      readonly
                      class="cursor-default"
                      v-model="welfareText"
                      @click="openDialogWelfare"
                      placeholder="请选择职位福利"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="用人部门" prop="department">
                    <el-input
                      v-model="formData.department"
                      clearable
                      placeholder="请输入用人部门"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="0">
                <el-col :xs="24" :sm="24" :md="23" :lg="23" :xl="23">
                  <el-form-item label="职位编制" prop="establishmentType">
                    <announcement-check-box
                      v-model="formData.establishmentType"
                      :check-box-list="establishmentTypeList"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="0">
                <el-col :span="24">
                  <el-form-item label="岗位职责" prop="duty">
                    <el-input
                      v-model="formData.duty"
                      type="textarea"
                      :rows="4"
                      resize="none"
                      maxlength="2000"
                      placeholder="请填写岗位职责(0/2000)"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="0">
                <el-col :span="24">
                  <el-form-item label="任职要求" prop="requirement">
                    <el-input
                      v-model="formData.requirement"
                      type="textarea"
                      :rows="4"
                      resize="none"
                      maxlength="2000"
                      placeholder="请填写任职要求(0/2000)"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="0">
                <el-col :span="24">
                  <el-form-item inline-message class="mb-0" label="其他说明" prop="remark">
                    <el-input
                      v-model="formData.remark"
                      type="textarea"
                      :rows="4"
                      resize="none"
                      maxlength="2000"
                      placeholder="请填写其他说明(0/2000)"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="0">
                <el-form-item>
                  <span class="color-danger fs-12">
                    *职位详情请勿输入单位邮箱，移动电话，性别歧视字眼及其他外链。
                  </span>
                </el-form-item>
              </el-row>
              <el-row justify="center">
                <el-form-item>
                  <el-button :loading="submitLoading" class="btn" type="primary" @click="submit(7)"
                    >发布</el-button
                  >
                  <!-- 新增职位或者审核拒绝的职位显示保存按钮 -->
                  <el-button
                    :loading="saveLoading"
                    v-if="!state.formData.announcementId && !disabled"
                    class="btn"
                    @click="submit(3)"
                    >保存</el-button
                  >
                  <!-- <el-button class="btn">预览</el-button> -->
                  <el-button class="btn" @click="handleBack">取消</el-button>
                </el-form-item>
              </el-row>
            </div>
          </div>
          <div v-if="addType == 2">
            <div class="title line-1 mb-20 fs-16 fw-bold pl-15">批量发布职位</div>
            <div class="pl-20">
              <el-form-item class="mb-0" :inline-message="true" label="选择文件" prop="filePath">
                <div class="flex">
                  <el-upload
                    ref="upload"
                    :action="excelUploadPath"
                    :limit="1"
                    :on-success="excelSuccess"
                    :on-remove="removeExcel"
                  >
                    <template #trigger>
                      <el-button class="mx-10" type="primary">上传文件</el-button>
                    </template>
                    <a class="color-primary fs-13 td-none" download :href="downloadHref"
                      >下载职位上传模板</a
                    >
                  </el-upload>
                </div>
              </el-form-item>
              <el-form-item>
                <span class="color-danger fs-12">
                  *职位详情请勿输入单位邮箱，移动电话，性别歧视字眼及其他外链。
                </span>
              </el-form-item>
              <el-form-item>
                <el-button class="mx-10" type="primary" @click="batchSubmit">确认提交</el-button>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="flex-1 right">
          <div class="title line-1 mb-20 fs-16 fw-bold pl-15">职位属性</div>
          <el-form-item label="截止日期" prop="periodDate">
            <DatePicker
              placeholder="选择日期"
              v-model="formData.periodDate"
              :disabledDate="handleDisabledDate"
            />
          </el-form-item>
          <el-form-item label="合作类型">
            <el-radio-group
              :disabled="disabled"
              v-model="cooperationType"
              @change="cooperationChange"
            >
              <el-radio :label="2">非合作单位</el-radio>
              <el-radio :label="1">合作单位</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选择单位" prop="companyId">
            <el-select
              class="block"
              :disabled="disabled"
              v-model="formData.companyId"
              filterable
              remote
              reserve-keyword
              placeholder="请选择单位"
              :remote-method="getCompany"
              @change="companyChange"
              :loading="remoteLoading"
              clearable
            >
              <el-option
                v-for="item in companyListOption"
                :key="item.id"
                :label="item.fullName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="flex" label="单位属性">
            <div class="flex">
              <el-form-item class="flex-1 mr-5" label-width="0px">
                <el-input disabled v-model="companyInfo.typeTitle"></el-input>
              </el-form-item>
              <el-form-item class="flex-1 ml-5" label-width="0px">
                <el-input disabled v-model="companyInfo.natureTitle"></el-input>
              </el-form-item>
            </div>
          </el-form-item>

          <el-form-item label="投递配置" v-if="isCooperation">
            <el-input disabled v-model="companyInfo.deliveryTypeTxt" />
          </el-form-item>

          <ApplyMethods
            v-model="applyMethodsData"
            :accountType="isCooperation ? 2 : 1"
            :isAnnouncement="false"
            :isCooperation="isCooperation"
            :cmsJobIsAnnouncement="formData.announcementId ? false : true"
          />

          <DeliveryLimitType
            v-if="isCooperation && addType == 1"
            v-model="formData.deliveryLimitType"
          />
          <el-form-item label="职位附件">
            <el-button class="button" type="primary" @click="openJobFileUpload"
              >+上传附件</el-button
            >
            <JobFileUpload ref="jobFile" v-model="fileList" />
          </el-form-item>

          <div v-if="isCooperation && addType == 1">
            <JobCooperate
              :companyId="formData.companyId"
              :hasAccount="hasAccount"
              v-model="synergyData"
              :email="formData.applyAddress"
              identifyShow
            />
          </div>
        </div>
      </el-form>
    </div>
    <DialogWelfare
      :member-id="companyInfo.memberId"
      title="职位福利"
      ref="dialogWelfare"
      @confirm="handleWelfare"
    />
  </div>
</template>

<script setup lang="ts">
import {
  toRefs,
  reactive,
  ref,
  onMounted,
  nextTick,
  watch,
  computed,
  getCurrentInstance
} from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

import JobCategory from '/@select/jobCategory.vue'
import Education from '/@select/education.vue'
// import MajorCategory from '/@select/majorCategory.vue'
import MajorDialog from '/@/views/cms/announcement/component/majorDialog.vue'
import WorkNature from '/@select/workNature.vue'
import Experience from '/@select/experience.vue'
import Salary from '/@/components/business/salary.vue'
import Age from '/@select/age.vue'
import LevelTitle from '/@select/levelTitle.vue'
import Political from '/@select/political.vue'
import AbroadExperience from '/@select/abroadExperience.vue'
// import Address from '/@/components/business/address.vue'
import Region from '/@select/region.vue'
import DatePicker from '/@/components/base/datePicker.vue'
import DialogWelfare from '/@/components/base/welfare.vue'

import { jobUploadPath } from '/@/config/upload'
import { getJobDetails, getJobTemplateDetails } from '/@/api/job'
import {
  getSearchJobList,
  getCompanyList,
  jobAdd,
  jobEdit,
  jobBatchImport,
  getJobEstablishmentList
} from '/@/api/cmsJob'
import DeliveryLimitType from '/@/views/cms/job/components/deliveryLimitType.vue'
import JobFileUpload from '/@/views/cms/announcement/component/jobFileUpload.vue'
import ApplyMethods from '/@/components/business/applyMethods.vue'
import AnnouncementCheckBox from '/@/components/base/announcementCheckBox.vue'
import JobCooperate from '/@/components/job/jobCooperate.vue'
import { aiRecognition } from '/@/api/major'

const { proxy } = getCurrentInstance() as any
const route = useRoute()
const router = useRouter()

const form = ref()
const upload = ref()
const dialogWelfare = ref()
const jobFile = ref()

const state = reactive({
  loading: false,
  disabled: false,
  submitLoading: false,
  saveLoading: false,
  isEdit: false,
  addType: 1, // 添加方式 1单个新增职位、2批量新增职位
  isFirstImportTemplate: true, // 是否第一次请求
  type: '', // 添加还是编辑，默认添加、edit为编辑
  cooperationType: 2, // 合作类型 2非合作单位、1合作单位
  remoteLoading: false, // 远程搜索loding
  companyListOption: [], // 单位列表

  templateId: '',
  templateListOption: [], // 职位模板列表
  establishmentTypeList: [], // 职位编制列表
  regionValue: <any>[],

  fileList: [],
  formData: <any>{
    // jobId: '',
    name: '', // 职位名称
    code: '', // 职位代码
    jobCategoryId: '', // 职位类型Id
    educationType: '', // 要求学历类型
    majorId: <any>'', // 需求专业ID
    natureType: null, // 工作性质
    wageType: '', // 薪资类型:1月，2年，3日
    isNegotiable: '2', // 是否自定义 1是，2否
    wageId: '', // 下拉薪资范围值
    amount: '', // 招聘人数
    provinceId: '',
    cityId: '',
    districtId: '',
    address: '',
    periodDate: '', // 有效期
    duty: '', // 岗位职责
    requirement: '', // 任职要求
    minWage: '',
    maxWage: '',
    experienceType: '', // 经验要求
    ageType: '',
    titleType: '', // 称职类型
    politicalType: '', // 政治面貌
    abroadType: '', // 海外经历
    department: '', // 用人部门
    welfareTag: '', // 福利待遇
    remark: '', // 其他说明
    companyId: '', // 单位ID
    areaName: '',
    filePath: '',
    deliveryLimitType: '',
    applyType: '',
    applyAddress: '',
    deliveryWay: '66',
    deliveryType: [],
    extraNotifyAddress: '',
    establishmentType: [],
    jobContactId: '',
    jobContactSynergyIds: []
  },
  excelUploadPath: jobUploadPath,

  welfareText: '',
  welfareArray: [],

  signList: [], // 应聘方式

  companyInfo: {
    id: '',
    memberId: '',
    typeTitle: '', // 单位类型
    natureTitle: '', // 单位性质
    deliveryTypeTxt: '',
    deliveryType: null
  },
  downloadHref: '/static/template/job_template.xlsx',
  isCooperation: computed(() => state.cooperationType === 1),

  applyMethodsData: computed({
    get() {
      const {
        formData: { applyType, applyAddress, deliveryType, extraNotifyAddress, deliveryWay }
      } = state
      return { applyType, applyAddress, deliveryType, extraNotifyAddress, deliveryWay }
    },

    set(val: Object) {
      Object.keys(val).forEach((key) => {
        state.formData[key] = val[key]
      })
    }
  }),

  synergyData: computed({
    get() {
      const {
        formData: { jobContactId, jobContactSynergyIds }
      } = state
      return { jobContactId, jobContactSynergyIds }
    },
    set(val: object) {
      Object.keys(val).forEach((key) => {
        state.formData[key] = val[key]
      })
    }
  }),

  subaccountUsed: '',
  hasAccount: computed(() => state.subaccountUsed !== '0'),
  majorText: '',
  formRules: {
    name: [{ required: true, message: '请输入职位名称', trigger: 'blur' }],
    jobCategoryId: [{ required: true, message: '请选择职位类型', trigger: 'change' }],
    educationType: [{ required: true, message: '请选择学历要求', trigger: 'change' }],
    // majorId: [{ required: true, message: '请选择需求专业', trigger: 'change' }],
    // natureType: [{ required: true, message: '请选择工作性质', trigger: 'change' }],
    amount: [
      {
        required: true,
        message: '请输入招聘人数',
        trigger: 'blur'
      },
      {
        pattern: /^([1-9]\d{0,3}|\u82e5\u5e72)$/,
        message: '请输入数字或者"若干"',
        trigger: 'blur'
      }
    ],
    provinceId: [{ required: true, message: '请选择工作地点', trigger: 'change' }],
    periodDate: [
      {
        required: computed(() => !state.formData.announcementId),
        message: '请输入有效期',
        trigger: 'change'
      }
    ],
    duty: [{ required: true, message: '请输入岗位职责', trigger: 'blur' }],
    requirement: [{ required: true, message: '请输入任职要求', trigger: 'blur' }],
    companyId: [{ required: true, message: '请选择单位', trigger: 'change' }],
    filePath: [{ required: true, message: '请上传文件', trigger: ['change', 'blur'] }]
  }
})

const {
  cooperationType,
  loading,
  formData,
  formRules,
  isEdit,
  addType,
  templateId,
  templateListOption,
  remoteLoading,
  disabled,
  regionValue,
  majorText,
  welfareText,
  establishmentTypeList,
  companyListOption,
  companyInfo,
  submitLoading,
  saveLoading,
  fileList,
  downloadHref,
  excelUploadPath,
  isCooperation,
  applyMethodsData,
  synergyData,
  hasAccount
} = toRefs(state)

// 模糊查询单位
const getCompany = (kw: string) => {
  state.remoteLoading = true
  getCompanyList({
    isCooperation: state.cooperationType,
    fullName: kw
  }).then((resp: any) => {
    state.companyListOption = resp
    state.remoteLoading = false
  })
}
// 模糊查询单位end

// 获取职位编制选项
const getJobEstablishment = () => {
  getJobEstablishmentList().then((resp: any) => {
    state.establishmentTypeList = resp
  })
}
getJobEstablishment()

// 处理后端返回的默认值为"0"或空字符串的情况，转为前端友好值
const handleDefaultValue = (data: Object) => {
  const newObject = <any>{}
  Object.keys(data).forEach((k: string) => {
    const v = data[k]
    newObject[k] = /^0$/.test(v) ? '' : v
  })
  return newObject
}

const fillJobFormData = (resp: any, jobId?: any, isTemplate = false) => {
  state.formData = handleDefaultValue(resp)
  state.formData.majorId = resp.majorId ? resp.majorId.split(',') : []
  state.formData.wageId = resp.wageId || ''
  // 职位福利标签处理
  const welfareTage = resp.welfareTage || []
  state.formData.welfareTag = welfareTage.map((item: any) => item.k).join(',')
  state.welfareText = welfareTage.map((item: any) => item.v).join(',')
  state.welfareArray = welfareTage
  // 单位信息处理
  const {
    companyId,
    memberId,
    companyTypeTitle,
    companyNatureTitle,
    companyDeliveryTypeTxt,
    companyDeliveryType
  } = resp
  state.companyInfo.id = companyId
  state.companyInfo.memberId = memberId
  state.companyInfo.typeTitle = companyTypeTitle
  state.companyInfo.natureTitle = companyNatureTitle
  state.companyInfo.memberId = resp.memberId
  state.companyInfo.deliveryTypeTxt = companyDeliveryTypeTxt
  state.companyInfo.deliveryType = Number(companyDeliveryType)
  state.regionValue = [resp.provinceId, resp.cityId]
  state.cooperationType = resp.isCooperation
  state.fileList = resp.fileList

  // 日期格式校验
  // 2025-05-16 看不懂为什么做这个校验，故注释
  // const { periodDate } = resp
  // const dateReg = /^(\d{4})-(\d{2})-(\d{2})$/
  // if (!dateReg.test(periodDate)) {
  //   state.formData.periodDate = ''
  // }

  getCompany(resp.companyName)

  // 有审核通过历史的不可以编辑
  state.disabled = ['1', '0'].includes(resp.status)

  if (isTemplate) {
    // 导入职位清除职位ID，防止干扰
    state.formData.jobId = ''
    state.formData.announcementId = ''
  } else {
    state.formData.jobId = jobId
  }
}

/**
 * 获取职位详情
 * @param jobId 职位id
 */
const getDetails = async (jobId: any) => {
  state.loading = true
  await getJobDetails({ jobId }).then((resp: any) => {
    fillJobFormData(resp, jobId, false)
    state.loading = false
  })
}

/**
 * 选择职位模板时，获取职位详情
 * @param jobId 职位id
 */
const getTemplateDetails = async (jobId: any) => {
  state.loading = true
  await getJobTemplateDetails({ id: jobId }).then((resp: any) => {
    fillJobFormData(resp, jobId, true)
    state.loading = false
  })
}

// const getData = () => {
//   getJobSignUpList().then((resp: any) => {
//     state.signList = resp
//   })
// }

/**
 * 重置表单
 * type为2(批量发布)，1单个职位发布
 */
const formReset = (type: number = 1) => {
  state.disabled = false
  state.welfareText = ''
  state.templateId = ''
  state.templateListOption = []
  const { filePath } = state.formData
  try {
    form.value.resetFields()
  } catch (err) {
    return null
  }
  state.formData.majorTitle = ''

  state.welfareText = ''
  state.welfareArray = []

  state.fileList = []
  state.formData.isNegotiable = '2'

  state.companyInfo = {
    id: '',
    memberId: '',
    typeTitle: '', // 单位类型
    natureTitle: '', // 单位性质
    deliveryTypeTxt: '',
    deliveryType: null
  }

  nextTick(() => {
    form.value.clearValidate()
    // 批量发布职位切换合作类型不清空文件
    if (state.addType === 2 && type === 2) {
      state.formData.filePath = filePath
    }
  })
  return true
}

onMounted(() => {
  // getData()
  if (route.params.id) {
    const jobId = <string>route.params.id
    state.isEdit = true
    getDetails(jobId)
  } else {
    getCompany('')
  }
})

// 模糊查询职位模板
const getTemplate = (kw: string) => {
  if (!state.formData.companyId) {
    // ElMessage.error('请先在右侧选择单位')
    return
  }
  state.remoteLoading = true
  getSearchJobList({
    companyId: state.formData.companyId,
    name: kw
  }).then((resp: any) => {
    state.templateListOption = resp
    state.remoteLoading = false
  })
}
const templateChange = (id: any) => {
  if (!id) return
  getTemplateDetails(id)
}
const templateFocus = () => {
  if (!state.formData.companyId) {
    ElMessage.error('请先在右侧选择单位')
  }
}

const openDialogWelfare = () => {
  dialogWelfare.value.openDialog(state.welfareArray)
}

const handleWelfare = (welfare: any) => {
  state.welfareArray = welfare
  state.welfareText = welfare.map((item: any) => item.v).join('，')
  state.formData.welfareTag = welfare.map((item: any) => item.k).join()
}

// const handleAddress = (ad: any) => {
//   state.formData.provinceId = ad.provinceId
//   state.formData.cityId = ad.cityId
//   state.formData.districtId = ad.districtId
//   state.formData.areaName = ad.areaName
//   state.formData.address = ad.address
// }

// 公告有效期不能早于当前日也不能晚于两年时间
const handleDisabledDate = (time: Date) => {
  const oneDayInMs = 24 * 60 * 60 * 1000
  const twoYearsInMs = 365 * 2 * oneDayInMs
  const now = Date.now()

  return time.getTime() < now - oneDayInMs || time.getTime() > now + twoYearsInMs
}

const cooperationChange = () => {
  state.formData.companyId = ''
  state.formData.jobContactId = ''
  state.formData.jobContactSynergyIds = []
  formReset(state.addType)
  getCompany('')
}

const companyChange = (id: string) => {
  // if (state.addType === 1) {
  //   formReset()
  // }
  if (id) {
    const arr = state.companyListOption.filter((item: any) => {
      return item.id === id
    })
    state.companyInfo = <any>arr[0]
    state.companyInfo.deliveryType = Number(arr[0].deliveryType)
    state.subaccountUsed = arr[0].subaccountUsed
    state.formData.companyId = id
    state.formData.deliveryWay = '66'
    getTemplate('')
  } else {
    state.templateId = ''
    state.templateListOption = []
    state.companyInfo = {
      id: '',
      memberId: '',
      typeTitle: '',
      natureTitle: '',
      deliveryTypeTxt: '',
      deliveryType: null
    }
  }
}

const excelSuccess = (res: any) => {
  if (res.result === 0) return
  state.formData.filePath = res.data.url
}
const removeExcel = () => {
  state.formData.filePath = ''
}

const handleJobSubmit = async (
  apiFunc: Function,
  postData: any,
  auditStatus: any,
  needReset = false
) => {
  try {
    await apiFunc(postData)
    if (auditStatus === 7) {
      state.submitLoading = false
    } else {
      state.saveLoading = false
    }
    if (needReset) {
      formReset()
    } else {
      proxy.mittBus.emit('closeCurrentViewTag')
    }
  } catch {
    if (auditStatus === 7) {
      state.submitLoading = false
    } else {
      state.saveLoading = false
    }
  }
}

const jobAddSubmit = (postData: any, auditStatus: any) => {
  handleJobSubmit(jobAdd, postData, auditStatus, true)
}

const jobEditSubmit = (postData: any, auditStatus: any) => {
  handleJobSubmit(jobEdit, postData, auditStatus)
}

const submit = (auditStatus: any) => {
  if (state.formData.companyId === -2) {
    state.formData.companyId = ''
    form.value.validateField('companyId', () => {})
  }
  if (Date.now() > new Date(state.formData.periodDate).getTime()) {
    if (auditStatus === 7) {
      state.submitLoading = true
    } else {
      state.saveLoading = true
    }
    ElMessage({
      message: '截止日期不能小于当前时间',
      type: 'error',
      onClose: () => {
        if (auditStatus === 7) {
          state.submitLoading = false
        } else {
          state.saveLoading = false
        }
      }
    })
    return
  }
  if (state.formData.minWage > state.formData.maxWage) {
    ElMessage.error('最低薪资大于最高薪资')
    return
  }
  // auditStatus 发布：7；保存：3
  form.value.validate((valid: any) => {
    if (valid) {
      const majorId = state.formData.majorId ? state.formData.majorId.join() : ''
      const postData = {
        ...state.formData,
        auditStatus,
        status: auditStatus,
        majorId,
        fileIds: state.fileList?.map((item: any) => item.id).join()
      }

      if (auditStatus === 7) {
        state.submitLoading = true
      } else {
        state.saveLoading = true
      }

      if (state.isEdit) {
        jobEditSubmit(postData, auditStatus)
      } else {
        jobAddSubmit(postData, auditStatus)
      }
    }
  })
}

const batchSubmit = () => {
  form.value.validate((valid: any) => {
    if (valid) {
      state.loading = true
      const {
        filePath,
        periodDate,
        companyId,
        applyType,
        applyAddress,
        extraNotifyAddress,
        deliveryWay
      } = state.formData
      const postData = {
        filePath,
        periodDate,
        companyId,
        applyType,
        applyAddress,
        extraNotifyAddress,
        deliveryWay,
        fileIds: state.fileList?.map((item: any) => item.id).join()
      }
      jobBatchImport(postData)
        .then(() => {
          upload.value.clearFiles()
          formReset()
          state.loading = false
          ElMessage.success('导入成功')
        })
        .catch(() => {
          state.loading = false
        })
    }
  })
}

const handleBack = () => {
  router.back()
}

const salarChage = (data) => {
  Object.assign(state.formData, data)
}

const openJobFileUpload = () => {
  jobFile.value.openJobFileUpload()
}

const recognition = () => {
  aiRecognition(state.majorText).then((r) => {
    state.formData.majorId = r.majorIds
    state.formData.majorTitle = r.majorNames.join(',')
  })
}

watch(
  () => state.addType,
  (val) => {
    if (val === 1) {
      removeExcel()
    }
  }
)
</script>

<style scoped lang="scss">
.title {
  margin-top: 15px;
  border-left: 2px solid var(--color-primary);
}

.box {
  border-radius: 10px;
  overflow: hidden;
  .left {
    background-color: #fff;
    padding: 20px 15px;
    border-right: 15px solid #f2f2f2;
  }
  .right {
    background-color: #fff;
    padding: 20px 15px;
  }
}

:deep(.el-form-item__content) {
  display: block;
}
</style>
