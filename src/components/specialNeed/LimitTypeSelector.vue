<template>
  <div class="limit-type-selector">
    <el-select
      :model-value="modelValue"
      @update:model-value="handleChange"
      placeholder="请选择限制类型"
      style="width: 100%"
    >
      <el-option
        v-for="option in limitTypeOptions"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>

    <!-- 次数限制字段 -->
    <div v-if="modelValue === 'count'" class="limit-fields">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="时间限制(天)" prop="timeLimit">
            <el-input-number
              :model-value="timeLimit"
              @update:model-value="handleTimeLimitChange"
              :min="0"
              :max="365"
              placeholder="请输入天数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="次数限制" prop="countLimit">
            <el-input-number
              :model-value="countLimit"
              @update:model-value="handleCountLimitChange"
              :min="1"
              :max="100"
              placeholder="请输入次数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>

    <!-- 条件限制字段 -->
    <div v-if="modelValue === 'condition'" class="limit-fields">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="条件字段" prop="conditionField">
            <el-select
              :model-value="conditionField"
              @update:model-value="handleConditionFieldChange"
              placeholder="请选择条件字段"
              style="width: 100%"
            >
              <el-option
                v-for="option in conditionFieldOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="条件值" prop="conditionValue">
            <el-input
              :model-value="conditionValue"
              @update:model-value="handleConditionValueChange"
              placeholder="请输入条件值"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  modelValue?: string
  timeLimit?: number
  countLimit?: number
  conditionField?: string
  conditionValue?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'update:timeLimit', value: number): void
  (e: 'update:countLimit', value: number): void
  (e: 'update:conditionField', value: string): void
  (e: 'update:conditionValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  timeLimit: 0,
  countLimit: 1,
  conditionField: '',
  conditionValue: ''
})

const emit = defineEmits<Emits>()

// 限制类型选项
const limitTypeOptions = [
  { label: '次数限制', value: 'count' },
  { label: '条件限制', value: 'condition' }
]

// 条件字段选项
const conditionFieldOptions = [
  { label: '海外经历', value: 'is_abroad' },
  { label: '学历水平', value: 'education_level' },
  { label: '工作经验', value: 'work_experience' }
]

// 处理限制类型变化
const handleChange = (value: string) => {
  emit('update:modelValue', value)
}

// 处理时间限制变化
const handleTimeLimitChange = (value: number) => {
  emit('update:timeLimit', value)
}

// 处理次数限制变化
const handleCountLimitChange = (value: number) => {
  emit('update:countLimit', value)
}

// 处理条件字段变化
const handleConditionFieldChange = (value: string) => {
  emit('update:conditionField', value)
}

// 处理条件值变化
const handleConditionValueChange = (value: string) => {
  emit('update:conditionValue', value)
}
</script>

<style scoped lang="scss">
.limit-type-selector {
  .limit-fields {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }
}
</style>
