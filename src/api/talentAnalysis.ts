import request from '/@/utils/request'

/**
 * 人才国籍分析API接口
 */

/**
 * 分析人才国籍信息
 * @param resumeId 简历ID
 * @returns 返回分析结果
 */
export function analyzeTalentNationality(resumeId: number) {
  return request({
    url: '/talent-analysis/analyze',
    method: 'post',
    data: { resumeId }
  })
}

/**
 * 人才国籍分析接口类型定义
 */
export interface TalentInfo {
  name: string
  englishName?: string
  age: number
  gender: string
  householdRegister: string
  residence: string
  avatar?: string
}

export interface AnalysisResult {
  isForeign: boolean
  confidence: number
  level: string
  suggestion: string
}

export interface DetailInfo {
  education: Array<{
    school: string
    major: string
    degree: string
    isAbroad: boolean
  }>
  workExperience: Array<{
    company: string
    position: string
    duration: string
    isAbroad: boolean
  }>
  languages: string[]
}

export interface TalentAnalysisResponse {
  resumeId: number
  talentInfo: TalentInfo
  analysisResult: AnalysisResult
  analysisReasons: string
  riskFactors: string[]
  detailedAnalysis: string
  detailInfo: DetailInfo
  llmResponse: string
}
