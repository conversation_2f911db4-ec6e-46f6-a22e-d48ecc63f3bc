import request from '/@/utils/request'

/**
 * 获取人才导出筛选条件配置
 * @returns 返回筛选条件配置数据
 */
export function getFilterConfig() {
  return request({
    url: '/talent-export/get-filter-config',
    method: 'get'
  })
}

/**
 * 预览符合条件的人才数据
 * @param params 筛选参数
 * @returns 返回预览数据
 */
export function previewTalentData(params: object) {
  return request({
    url: '/talent-export/preview',
    method: 'get',
    params
  })
}

/**
 * 导出人才数据
 * @param data 筛选参数
 * @returns 返回导出结果
 */
export function exportTalentData(data: object) {
  return request({
    url: '/talent-export/export',
    method: 'post',
    data
  })
}

/**
 * 导出选中的人才数据
 * @param data 选中的人才数据
 * @returns 返回导出结果
 */
export function exportSelectedTalents(data: object) {
  return request({
    url: '/talent-export/export-selected',
    method: 'post',
    data
  })
}
