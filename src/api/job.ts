import request from '/@/utils/request'

/**
 * 审核状态
 * @returns 返回接口数据
 */
export function getJobAuditStatusList() {
  return request({
    url: '/job/job-audit-status-list',
    method: 'get'
  })
}

/**
 * 招聘状态
 * @returns 返回接口数据
 */
export function getJobStatusList() {
  return request({
    url: '/job/job-status-list',
    method: 'get'
  })
}

/**
 * 职位查询列表
 * @returns 返回接口数据
 */
export function getJobList(params: object) {
  return request({
    url: '/job/job-list',
    method: 'get',
    params
  })
}

/**
 * 职位查询列表-v2
 * @returns 返回接口数据
 */
export function getCooperationJobList(params: object) {
  return request({
    url: '/job/cooperation-index',
    method: 'get',
    params
  })
}

/**
 * 职位查询列表(简版)
 * @returns 返回接口数据
 */
export function getSimpleJobList(params: object) {
  return request({
    url: '/job/simple-job-list',
    method: 'get',
    params
  })
}
/**
 * 职位查询列表(v2)
 * @returns 返回接口数据
 */
export function getSliceJobList(params: object) {
  return request({
    url: '/job/slice-job-list',
    method: 'get',
    params
  })
}

/**
 * 职位下线前置检查
 * @returns 返回接口数据
 */
export function jobOfflineCheck(params: object) {
  return request({
    url: '/job/offline-check',
    method: 'get',
    params
  })
}

/**
 * 职位隐藏前置检查
 * @returns 返回接口数据
 */
export function jobHideCheck(params: object) {
  return request({
    url: '/job/hide-check',
    method: 'get',
    params
  })
}

/**
 * 职位删除
 * @returns 返回接口数据
 */
export function jobDelete(params: object) {
  return request({
    url: '/job/delete',
    method: 'post',
    data: params
  })
}

/**
 * 职位删除(批量)
 * @returns 返回接口数据
 */
export function batchJobDelete(params: object) {
  return request({
    url: '/job/delete-batch',
    method: 'post',
    data: params
  })
}

/**
 * 职位刷新
 * @returns 返回接口数据
 */
export function jobRefresh(params: object) {
  return request({
    url: '/job/refresh',
    method: 'post',
    data: params
  })
}

export function batchJobRefresh(data: object) {
  return request({
    url: '/job/refresh-batch',
    method: 'post',
    data
  })
}

/**
 * 职位再发布
 * @returns 返回接口数据
 */
export function jobReleaseAgain(params: object) {
  return request({
    url: '/job/republish',
    method: 'post',
    data: params
  })
}
/**
 * 职位批量再发布
 * @returns 返回接口数据
 */
export function batchJobReleaseAgain(params: object) {
  return request({
    url: '/job/republish-batch',
    method: 'post',
    data: params
  })
}

/**
 * 职位下线
 * @returns 返回接口数据
 */
export function jobOffline(params: object) {
  return request({
    url: '/job/offline',
    method: 'post',
    data: params
  })
}

/**
 * 批量职位下线
 * @returns 返回接口数据
 */
export function batchJobOffline(params: object) {
  return request({
    url: '/job/offline-batch',
    method: 'post',
    data: params
  })
}

/**
 * 职位修改编制
 * @returns 返回接口数据
 */
export function jobEstablishment(params: object) {
  return request({
    url: '/job/establishment',
    method: 'post',
    data: params
  })
}

/**
 * 批量职位修改编制
 * @returns 返回接口数据
 */
export function batchJobEstablishment(params: object) {
  return request({
    url: '/job/establishment-batch',
    method: 'post',
    data: params
  })
}

/**
 * 显示/隐藏职位
 * @returns 返回接口数据
 */
export function changeJobShow(params: object) {
  return request({
    url: '/job/is-show',
    method: 'post',
    data: params
  })
}

export function batchChangeJobShow(data: object) {
  return request({
    url: '/job/is-show-batch',
    method: 'post',
    data: { ...data, isShow: '1' }
  })
}

export function batchChangeJobHide(data: object) {
  return request({
    url: '/job/is-show-batch',
    method: 'post',
    data: { ...data, isShow: '2' }
  })
}

/**
 * 职位操作日志
 * @returns 返回接口数据
 */
export function getJobHandleLogList(params: object) {
  return request({
    url: '/job/get-handle-log-list',
    method: 'get',
    params
  })
}

/**
 * 职位变更联系人及协同联系人
 * @returns 返回接口数据
 */
export function changeJobContact(params: object) {
  return request({
    url: '/job/contact',
    method: 'post',
    data: params
  })
}

/**
 * 业务收到简历列表
 * @returns 返回接口数据
 */
export function getApplyList(params: object) {
  return request({
    url: '/job/job-apply-list',
    method: 'get',
    params
  })
}

/**
 * 业务面试邀约列表
 * @returns 返回接口数据
 */
export function getInvitationList(params: object) {
  return request({
    url: '/job/job-invitation-list',
    method: 'get',
    params
  })
}

/**
 * 业务下载简历列表
 * @returns 返回接口数据
 */
export function getResumeDownloadList(params: object) {
  return request({
    url: '/job/job-resume-download-list',
    method: 'get',
    params
  })
}

/**
 * 职位详情
 * @returns 返回接口数据
 */
export function getJobDetailsV2(params: object) {
  return request({
    url: '/job/detail',
    method: 'get',
    params
  })
}

/**
 * 职位详情初始化
 * @returns 返回接口数据
 */
export function getJobDetails(params: object) {
  return request({
    url: '/job/edit-init',
    method: 'get',
    params
  })
}

/**
 * 选择职位模板时，获取职位详情
 * @returns 返回接口数据
 */
export function getJobTemplateDetails(params: object) {
  return request({
    url: '/job/template-init-add',
    method: 'get',
    params
  })
}

/**
 * 编辑职位
 * @returns 返回接口数据
 */
export function jobEdit(params: object) {
  return request({
    url: '/job/job-edit',
    method: 'post',
    data: params
  })
}

/**
 * 职位审核列表(合作单位)
 * @returns 返回接口数据
 */
export function getJobAuditList(params: object) {
  return request({
    url: '/job/cooperation-audit-index',
    method: 'get',
    params
  })
}

/**
 * 职位审核列表(非合作单位)
 * @returns 返回接口数据
 */
export function getUnJobAuditList(params: object) {
  return request({
    url: '/job/un-cooperation-audit-index',
    method: 'get',
    params
  })
}

/**
 * 职位审核详情
 * @returns 返回接口数据
 */
export function jobExamine(params: object) {
  return request({
    url: '/job/audit-status',
    method: 'post',
    data: params
  })
}

/**
 * 职位审核详情
 * @returns 返回接口数据
 */
export function getJobAuditInfo(params: object) {
  return request({
    url: '/job/audit-detail',
    method: 'get',
    params
  })
}

/**
 * 职位审核处理历史
 * @returns 返回接口数据
 */
export function getJobAuditHandleList(params: object) {
  return request({
    url: '/job/audit-history-log',
    method: 'get',
    params
  })
}

/**
 * 获取职位地址列表
 * @returns 返回接口数据
 */
export function getAddressList(params: Object) {
  return request({
    url: '/job/get-company-address-list',
    method: 'get',
    params
  })
}
/**
 *添加职位福利标签
 * @returns 返回接口数据
 */
export function createWelfareTag(params: Object) {
  return request({
    url: '/welfare-label/create',
    method: 'post',
    data: params
  })
}
/**
 *删除职位福利标签
 * @returns 返回接口数据
 */
export function deleteWelfareTag(params: Object) {
  return request({
    url: '/welfare-label/del',
    method: 'post',
    data: params
  })
}
/**
 *面试详情查看
 * @returns 返回接口数据
 */
export function getCompanyInterviewDetail(params: Object) {
  return request({
    url: '/company/interview-detail',
    method: 'get',
    params
  })
}

/**
 *修改投递学历限制
 * @returns 返回接口数据
 */

export function batchAddDeliveryEducationLimit(params: Object) {
  return request({
    url: '/job/limit-education-batch',
    method: 'post',
    data: { ...params, isLimit: '1' }
  })
}
export function batchDelDeliveryEducationLimit(params: Object) {
  return request({
    url: '/job/limit-education-batch',
    method: 'post',
    data: { ...params, isLimit: '2' }
  })
}
export function changeDeliveryEducationLimit(params: Object) {
  return request({
    url: '/job/limit-education',
    method: 'post',
    data: params
  })
}

/**
 *修改投递应聘材料限制
 * @returns 返回接口数据
 */
export function changeDeliveryFileLimit(params: Object) {
  return request({
    url: '/job/limit-attachment',
    method: 'post',
    data: params
  })
}
export function batchAddDeliveryFileLimit(params: Object) {
  return request({
    url: '/job/limit-attachment-batch',
    method: 'post',
    data: { ...params, isLimit: '1' }
  })
}
export function batchDelDeliveryFileLimit(params: Object) {
  return request({
    url: '/job/limit-attachment-batch',
    method: 'post',
    data: { ...params, isLimit: '2' }
  })
}

/**
 * 获取投递邀约配置
 * @param params
 */
export function getInviteConfig() {
  return request({
    url: '/delivery-invite/get-config-text',
    method: 'get'
  })
}

/**
 * 获取投递邀约检测
 * @param params
 */
export function getInviteCheck(params: Object) {
  return request({
    url: '/delivery-invite/check',
    method: 'post',
    data: params
  })
}

/**
 * 添加投递邀约检测
 * @param params
 */
export function getInviteAdd(params: Object) {
  return request({
    url: '/delivery-invite/add',
    method: 'post',
    data: params
  })
}

/**
 * 职位协同筛选
 * @returns 返回接口数据
 */
export function searchJobAccount(params: Object) {
  return request({
    url: '/job/get-company-sub-member-list',
    params
  })
}

/**
 * 识别职位协同子账号
 * @returns 返回接口数据
 */
export function identifyMemberAccount(params: Object) {
  return request({
    url: '/job/member-account-identify',
    params
  })
}

/**
 * 职位变更联系人及协同联系人
 * @returns 返回接口数据
 */
export function accountEdit(params: Object) {
  return request({
    url: '/job/contact',
    method: 'post',
    data: params
  })
}

/**
 * 批量修改职位联系人与协同子账号
 * @returns 返回接口数据
 */
export function accountBatchEdit(params: Object) {
  return request({
    url: '/job/contact-batch',
    method: 'post',
    data: params
  })
}

/**
 * 批量修改职位福利标签
 * @returns 返回接口数据
 */
export function batchEditWelfare(params: Object) {
  return request({
    url: '/job/batch-edit-welfare',
    method: 'post',
    data: params
  })
}

// 批量修改职位福利检查
export function batchEditWelfareCheck(params: Object) {
  return request({
    url: '/job/batch-edit-welfare-check',
    method: 'post',
    data: params
  })
}
