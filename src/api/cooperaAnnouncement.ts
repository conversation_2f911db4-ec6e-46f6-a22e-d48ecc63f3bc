import request from '/@/utils/request'

// 公告审核列表

export function getCooperaAuditList(params: Object) {
  return request({
    url: '/announcement/audit-list',
    params
  })
}

// 公告操作日志

export function getAnnouncementHandleLog(params: Object) {
  return request({
    url: '/announcement/get-announcement-handle-log',
    params
  })
}

// 职位投递初始接口
export function getApplyInit(params: Object) {
  return request({
    url: '/apply/get-apply-init',
    params
  })
}

// 业务--站内投递列表
export function getOnSideApplyList(params: Object) {
  return request({
    url: '/apply/in-station-apply-index',
    params
  })
}

// 业务--站内投递列表-统计
export function getOnSideApplyStat(params: Object) {
  return request({
    url: '/apply/in-station-apply-stat',
    params
  })
}

// 业务--面试邀约列表
export function getInterviewList(params: Object) {
  return request({
    url: '/apply/interview-index',
    params
  })
}

// 业务--面试邀约列表-统计
export function getInterviewStat(params: Object) {
  return request({
    url: '/apply/interview-stat',
    params
  })
}

// 业务--面试邀约列表-详情
export function getInterviewDetail(params: Object) {
  return request({
    url: '/apply/interview-detail',
    params
  })
}

// 业务--下载的简历列表
export function getResumeDownloadLog(params: Object) {
  return request({
    url: '/apply/resume-download-log-index',
    params
  })
}

// 业务--下载的简历列表-统计
export function getResumeDownloadLogStat(params: Object) {
  return request({
    url: '/apply/resume-download-log-stat',
    params
  })
}

// 业务--站外投递列表
export function getOutsideApplyList(params: Object) {
  return request({
    url: '/apply/off-site-apply-index',
    params
  })
}

// 业务--站外投递列表-统计
export function getOutsideApplyStat(params: Object) {
  return request({
    url: '/apply/off-site-apply-stat',
    params
  })
}
