import request from '/@/utils/request'

/**
 * 合作申请的类型
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getCompanyCooperationTypeList() {
  return request({
    url: '/config/get-company-cooperation-type-list',
    method: 'get'
  })
}

/**
 * 获取专业类型列表
 * @returns 返回接口数据
 */
export function getMajorList() {
  return request({
    url: '/config/get-major-list',
    method: 'get'
  })
}

/**
 * 获取二级专业类型列表
 * @returns 返回接口数据
 */
export function getSecondMajorList() {
  return request({
    url: '/config/get-level2-major-list',
    method: 'get'
  })
}

/**
 * 获取专业类型列表
 * @returns 返回接口数据
 */
export function getLevel2MajorList() {
  return request({
    url: '/config/get-level2-major-list',
    method: 'get'
  })
}

/**
 * 获取海外经历
 * @returns 返回接口数据
 */
export function getAbroadList() {
  return request({
    url: '/config/get-abroad-list',
    method: 'get'
  })
}

/**
 * 获取求职状态
 * @returns 返回接口数据
 */
export function getPersonStatusList() {
  return request({
    url: '/config/get-job-status-list',
    method: 'get'
  })
}

/**
 * 获取会员账号状态列表
 * @returns 返回接口数据
 */
export function getMemberStatusList() {
  return request({
    url: '/config/member-status-list',
    method: 'get'
  })
}

/**
 * 获取年龄列表
 * @returns 返回接口数据
 */
export function getAgeList() {
  return request({
    url: '/config/get-age-list',
    method: 'get'
  })
}

/**
 * 获取职称列表(多级)
 * @returns 返回接口数据
 */
export function getTitleList() {
  return request({
    url: '/config/get-title-list',
    method: 'get'
  })
}

/**
 * 获取职称列表(一级)
 * @returns 返回接口数据
 */
export function getFirstTitleList() {
  return request({
    url: '/config/get-first-title-list',
    method: 'get'
  })
}

/**
 * 获取学历要求
 * @returns 返回接口数据
 */
export function getEducationList() {
  return request({
    url: '/config/get-education-list',
    method: 'get'
  })
}

/**
 * 获取筛选用的学历要求
 * @returns 返回接口数据
 */
export function getEducationSearchList() {
  return request({
    url: '/config/get-education-search-list',
    method: 'get'
  })
}

/**
 * 获取职位类型
 * @returns 返回接口数据
 */
export function getCategoryJobList() {
  return request({
    url: '/config/get-category-job-list',
    method: 'get'
  })
}

/**
 * 获取工作性质
 * @returns 返回接口数据
 */
export function getNatureList() {
  return request({
    url: '/config/get-nature-list',
    method: 'get'
  })
}

/**
 * 获取工作经验(工作年限)
 * @returns 返回接口数据
 */
export function getWorkExperienceList() {
  return request({
    url: '/config/get-experience-list',
    method: 'get'
  })
}

/**
 * 获取政治面貌列表
 * @returns 返回接口数据
 */
export function getPoliticalList() {
  return request({
    url: '/config/get-political-status-list',
    method: 'get'
  })
}

/**
 * 获取户籍/国籍
 * @returns 返回接口数据
 */
export function getNativePlaceList() {
  return request({
    url: '/config/get-household-register-list',
    method: 'get'
  })
}
/**
 * 获取地区二级列表
 * @returns 返回接口数据
 */
export function getSecondAreaList() {
  return request({
    url: '/config/get-area-list',
    method: 'get'
  })
}

/**
 * 获取地区三级列表
 * @returns 返回接口数据
 */
export function getFullAreaList() {
  return request({
    url: '/config/get-full-area-list',
    method: 'get'
  })
}

/**
 * 行业(求职意向)
 * @returns 返回接口数据
 */
export function getTradeList() {
  return request({
    url: '/config/get-trade-list',
    method: 'get'
  })
}

/**
 * 薪资要求
 * @returns 返回接口数据
 */
export function getWageList() {
  return request({
    url: '/config/get-wage-list',
    method: 'get'
  })
}
/**
 * 注册来源
 * @returns 返回接口数据
 */
export function getMemberSourceList() {
  return request({
    url: '/config/get-member-source-type-list',
    method: 'get'
  })
}

/**
 * 获取默认福利标签
 * @returns 返回接口数据
 */
export function getWelfareLabelList(params: Object) {
  return request({
    url: '/config/get-welfare-label-list',
    method: 'get',
    params
  })
}

/**
 * 获取到岗时间
 * @returns 返回接口数据
 */
export function getArriveDateList() {
  return request({
    url: '/config/get-arrive-date-list',
    method: 'get'
  })
}

/**
 * 获取暂存表格显示字段
 * @returns 返回接口数据
 */
export function getTableStagingField(key: string) {
  return request({
    url: '/config/get-table-staging-field',
    method: 'get',
    params: { key }
  })
}

/**
 * 获取暂存表格显示字段
 * @returns 返回接口数据
 */
export function setTableStagingField(key: string, value: string) {
  return request({
    url: '/config/set-table-staging-field',
    method: 'post',
    data: { key, value }
  })
}

/**
 * 获取操作类型列表字段
 * @returns 返回接口数据
 */
export function getAnnouncementTypeList() {
  return request({
    url: '/announcement/get-handle-type-list'
  })
}

/*
 * 获取投递限制列表
 * @returns 返回接口数据
 */
export function getDeliveryLimit() {
  return request({
    url: '/config/get-delivery-limit-list'
  })
}

/*
 * 获取筛选投递类型配置项
 * @returns 返回接口数据
 */
export function getJobDeliveryType() {
  return request({
    url: '/job/delivery-type-search'
  })
}

/*
 * 获取投递方式与报名方式列表
 * @returns 返回接口数据
 */
export function getDeliveryApply() {
  return request({
    url: '/config/get-delivery-apply'
  })
}

/*
 * 人才应聘详情报名方式筛选项
 * @returns 返回接口数据
 */
export function getDeliveryWay(params: Object) {
  return request({
    url: '/person/get-delivery-way-select',
    params
  })
}

/*
 * 获取是否绑定微信类型
 * @returns 返回接口数据
 */
export function getWxBind() {
  return request({
    url: '/config/get-wx-bind'
  })
}

/*
 * 获取职位置顶类型列表
 * @returns 返回接口数据
 */
export function getJobTopTypeList() {
  return request({
    url: '/config/get-job-top-type-list'
  })
}

/*
 * 获取职位置顶状态列表
 * @returns 返回接口数据
 */
export function getJobTopStatusList() {
  return request({
    url: '/config/get-job-top-status-list'
  })
}

/**
 * 获取广告位跳转类型
 */
export function getShowcaseTargetLinkTypeList() {
  return request({
    url: '/config/get-showcase-target-link-type-list'
  })
}

/**
 * 获取广告位跳转页面类型
 */
export function getShowcasePageLinkTypeList() {
  return request({
    url: '/config/get-showcase-page-link-type-list'
  })
}
/*
 * 获取投递端口
 * @returns 返回接口数据
 */
export function getJobApplyPlatformList() {
  return request({
    url: '/config/get-job-apply-platform-list'
  })
}

/**
 * 获取是否小程序的列表
 * @returns 返回接口数据
 */
export function getIsMiniappList() {
  return request({
    url: '/config/get-is-miniapp-list',
    method: 'get'
  })
}

// 更新是否小程序
export function changeIsMiniapp(data: Object) {
  return request({
    url: '/config/change-is-miniapp',
    method: 'post',
    data
  })
}

// 获取编制类型列表
export function getEstablishmentTypeList() {
  return request({
    url: '/config/get-establishment-type-list'
  })
}

// 获取手机区号
export function getCountrytMobileCode() {
  return request({
    url: '/config/load-country-mobile-code'
  })
}

/*
 * 获取是否海外配置
 * @returns 返回接口数据
 */
export function getIsAbroad() {
  return request({
    url: '/config/get-is-abroad-list'
  })
}

/*
 * 修改是否海外配置
 * @returns 返回接口数据
 */
export function changeIsAbroad(data: Object) {
  return request({
    url: '/config/change-is-abroad',
    method: 'post',
    data
  })
}

/*
 * 修改是否海外配置
 * @returns 返回接口数据
 */
export function hwAreaList() {
  return request({
    url: '/config/get-hw-activity-area-list'
  })
}
// 获取单位群组
export function getCompanyGroup() {
  return request({
    url: '/config/company-group'
  })
}

// 获取单位类型
export function getCompanyTypeList() {
  return request({
    url: '/config/get-company-type-list'
  })
}

// 获取单位性质
export function getCompanyNatureList() {
  return request({
    url: '/config/get-company-nature-list'
  })
}

// 获取编辑单位时的特色标签列表
export function getCompanyFeaturedTagList() {
  return request({
    url: '/config/get-featured-tag-list'
  })
}

/**
 * 获取简历项目类型
 * @returns 返回接口数据
 */
export function getProjectCateList() {
  return request({
    url: '/config/get-project-cate-list',
    method: 'get'
  })
}
// 获取首写字母的缩写
export function getFirstLetter(text: string) {
  return request({
    url: '/config/get-first-letter',
    method: 'get',
    params: { text }
  })
}

/**
 * 获取资讯调用站点所属列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getUseSiteTypeList(params: object) {
  return request({
    url: '/config/get-use-site-type-list',
    method: 'get',
    params
  })
}
