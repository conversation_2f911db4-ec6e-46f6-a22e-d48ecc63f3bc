import request from '/@/utils/request'

/**
 * 用户账号登录
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function accountLogin(params: object) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: params
  })
}

export function getMenu() {
  return request({
    url: '/home/<USER>',
    method: 'get'
  })
}

/**
 * 修改密码
 *
 * @export
 * @param {object} params
 * @returns
 */
export function editPassord(params: object) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: params
  })
}

/**
 * 企业微信JS-SDK登录验证接口
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function checkWxWorkLogin(params: object) {
  return request({
    url: '/wx-work/login-by-code',
    method: 'post',
    data: params
  })
}

/**
 * 检查登录
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getWxWorkLoginConfig() {
  return request({
    url: '/wx-work/get-login-config',
    method: 'get'
  })
}

/**
 * 获取微信的全部用户
 * @param params 要传的参数值
 * @returns 返回接口数据1
 */
export function getWxUser() {
  return request({
    url: '/wx-work/get-all-user',
    method: 'get'
  })
}
