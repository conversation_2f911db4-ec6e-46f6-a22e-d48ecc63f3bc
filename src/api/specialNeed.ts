import request from '/@/utils/request'

// ==================== 字段替换配置管理 ====================

/**
 * 获取配置列表
 * @param params 查询参数
 * @returns 返回配置列表数据
 */
export function getConfigList(params: object) {
  return request({
    url: '/special-need/config-index',
    method: 'get',
    params
  })
}

/**
 * 获取配置详情
 * @param params 查询参数
 * @returns 返回配置详情数据
 */
export function getConfigDetail(params: object) {
  return request({
    url: '/special-need/config-view',
    method: 'get',
    params
  })
}

/**
 * 创建配置
 * @param data 配置数据
 * @returns 返回创建结果
 */
export function createConfig(data: object) {
  return request({
    url: '/special-need/config-create',
    method: 'post',
    data
  })
}

/**
 * 更新配置
 * @param data 配置数据
 * @returns 返回更新结果
 */
export function updateConfig(data: object) {
  return request({
    url: '/special-need/config-update',
    method: 'post',
    data
  })
}

/**
 * 删除配置
 * @param params 删除参数
 * @returns 返回删除结果
 */
export function deleteConfig(params: any) {
  return request({
    url: '/special-need/config-delete',
    method: 'post',
    data: params
  })
}

/**
 * 批量状态操作
 * @param data 批量操作数据
 * @returns 返回操作结果
 */
export function batchUpdateConfigStatus(data: object) {
  return request({
    url: '/special-need/config-batch-status',
    method: 'post',
    data
  })
}

/**
 * 获取字段选项
 * @param params 查询参数
 * @returns 返回字段选项数据
 */
export function getFieldOptions(params: object) {
  return request({
    url: '/special-need/get-field-options',
    method: 'get',
    params
  })
}

/**
 * 测试配置
 * @param data 测试数据
 * @returns 返回测试结果
 */
export function testConfig(data: object) {
  return request({
    url: '/special-need/test-config',
    method: 'post',
    data
  })
}

// ==================== 投递限制配置管理 ====================

/**
 * 获取限制列表
 * @param params 查询参数
 * @returns 返回限制列表数据
 */
export function getLimitList(params: object) {
  return request({
    url: '/special-need/limit-index',
    method: 'get',
    params
  })
}

/**
 * 获取限制详情
 * @param params 查询参数
 * @returns 返回限制详情数据
 */
export function getLimitDetail(params: object) {
  return request({
    url: '/special-need/limit-view',
    method: 'get',
    params
  })
}

/**
 * 创建投递限制
 * @param data 限制数据
 * @returns 返回创建结果
 */
export function createLimit(data: object) {
  return request({
    url: '/special-need/limit-create',
    method: 'post',
    data
  })
}

/**
 * 更新投递限制
 * @param data 限制数据
 * @returns 返回更新结果
 */
export function updateLimit(data: object) {
  return request({
    url: '/special-need/limit-update',
    method: 'post',
    data
  })
}

/**
 * 删除投递限制
 * @param params 删除参数
 * @returns 返回删除结果
 */
export function deleteLimit(params: any) {
  return request({
    url: '/special-need/limit-delete',
    method: 'post',
    data: params
  })
}

// ==================== 缓存管理 ====================

/**
 * 清除缓存
 * @param data 清除缓存参数
 * @returns 返回清除结果
 */
export function clearCache(data: object) {
  return request({
    url: '/special-need/clear-cache',
    method: 'post',
    data
  })
}

/**
 * 预热缓存
 * @param data 预热缓存参数
 * @returns 返回预热结果
 */
export function warmupCache(data: object) {
  return request({
    url: '/special-need/warmup-cache',
    method: 'post',
    data
  })
}

/**
 * 获取缓存统计
 * @returns 返回缓存统计数据
 */
export function getCacheStats() {
  return request({
    url: '/special-need/cache-stats',
    method: 'get'
  })
}

// ==================== 配置导入导出 ====================

/**
 * 导出配置
 * @returns 返回导出文件
 */
export function exportConfig() {
  return request({
    url: '/special-need/export-config',
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 导入配置
 * @param data 导入数据
 * @returns 返回导入结果
 */
export function importConfig(data: FormData) {
  return request({
    url: '/special-need/import-config',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
