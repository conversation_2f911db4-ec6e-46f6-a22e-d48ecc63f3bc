import request from '/@/utils/request'

/**
 * 获取单位全部基本信息
 * @returns 返回接口数据
 */
export function getFullInfo() {
  return request({
    url: '/company/account/get-full-info',
    method: 'get'
  })
}

/**
 * 更新单位全部基本信息
 * @returns 返回接口数据
 */
export function updateFullInfo(params: object) {
  return request({
    url: '/company/account/update-full-info',
    method: 'post',
    data: params
  })
}

/**
 * 获取单位的子账号配置
 * @returns 返回接口数据
 */
export function getAccountConfig(params: object) {
  return request({
    url: '/company/get-company-sub-account-config',
    params
  })
}

/**
 * 保存单位的子账号配置
 * @returns 返回接口数据
 */
export function saveAccountConfig(params: object) {
  return request({
    url: '/company/save-company-sub-account-config',
    method: 'post',
    data: params
  })
}

/**
 * 账号查询列表
 * @description 展示所有合作单位的账号信息（含主&子账号），不区分单位当前审核状态
 * @returns 返回接口数据，包含审核状态和编辑权限字段
 */
export function getAccountList(params: object) {
  return request({
    url: '/company-account/index',
    params
  })
}

/**
 * 账号查询列表
 * @returns 返回接口数据
 */
export function getAccountFilter() {
  return request({
    url: '/company-account/filter'
  })
}

/**
 * 获取子账号权限配置
 * @returns 返回接口数据
 */
export function getAccountEditStatus(params: object) {
  return request({
    url: '/company-account/edit-permission-text',
    params
  })
}

/**
 * 编辑子账号权限
 * @returns 返回接口数据
 */
export function saveAccountPermission(params: object) {
  return request({
    url: '/company-account/edit-permission',
    method: 'post',
    data: params
  })
}

/**
 * 获取账号详情弹窗信息
 * @description 获取账号详情，包含审核状态和编辑权限信息
 * @returns 返回接口数据
 */
export function getAccountDetail(params: object) {
  return request({
    url: '/company-account/detail',
    params
  })
}

/**
 * 单位账号配置列表
 * @returns 返回接口数据
 */
export function getCompanyAccountList(params: object) {
  return request({
    url: '/company-account/config-list',
    params
  })
}

/**
 * 新增子账号
 * @returns 返回接口数据
 */
export function companyAccountAdd(params: object) {
  return request({
    url: '/company-account/add',
    method: 'post',
    data: params
  })
}

/**
 * 获取账号设置详情
 * @description 获取账号编辑初始化数据，包含审核状态和编辑权限信息
 * @returns 返回接口数据
 */
export function getAccountSetting(params: object) {
  return request({
    url: '/company-account/edit-init',
    params
  })
}

/**
 * 编辑账号
 * @description 编辑账号信息，支持修改单位名称（在符合条件的情况下）
 * @param params 包含companyName字段用于修改单位名称
 * @returns 返回接口数据
 */
export function editAccount(params: object) {
  return request({
    url: '/company-account/edit',
    method: 'post',
    data: params
  })
}

/**
 * 单位日志账号变更日志
 * @returns 返回接口数据
 */
export function getAccountLog(params: object) {
  return request({
    url: '/company-account/log',
    params
  })
}

/**
 * 单位日志账号变更日志
 * @returns 返回接口数据
 */
export function editAccountMessageConfig(params: object) {
  return request({
    url: '/company-account/edit-message-config',
    method: 'post',
    data: params
  })
}

/**
 * 直聊消息提醒
 * @returns 返回接口数据
 */
export function editChatConfig(params: object) {
  return request({
    url: '/company-account/edit-is-chat',
    method: 'post',
    data: params
  })
}
