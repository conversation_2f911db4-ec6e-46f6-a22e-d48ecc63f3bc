import request from '/@/utils/request'
import axios from 'axios'
import qs from 'qs'
/**
 * 人才列表(其实就是在线简历)
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getList(params: Object) {
  return request({
    url: '/person/search-list',
    method: 'get',
    params
  })
}

/**
 * 人才列表的数据统计
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getListStatistics(params: Object) {
  return request({
    url: '/person/get-person-statistics-data',
    method: 'get',
    params
  })
}

/**
 * 人才列表的操作日志
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getOperationLogList(params: Object) {
  return request({
    url: '/person/get-operation-log',
    method: 'get',
    params
  })
}

/**
 * 人才列表的登录日志
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getLogList(params: Object) {
  return request({
    url: '/person/get-login-log',
    method: 'get',
    params
  })
}

/**
 * 修改求职者用户状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeStatus(id: string) {
  return request({
    url: '/person/change-person-status',
    method: 'post',
    data: { id }
  })
}

/**
 * 获取站内投递列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getOnSiteApplyList(params: Object) {
  return request({
    url: '/person/get-on-site-apply-list',
    method: 'get',
    params
  })
}

/**
 * 获取站外投递列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getOffSiteApplyList(params: Object) {
  return request({
    url: '/person/get-off-site-apply-list',
    method: 'get',
    params
  })
}

/**
 * 获取面试列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getInterviewList(params: Object) {
  return request({
    url: '/person/get-interview-list',
    method: 'get',
    params
  })
}

/**
 * 获取求职日程列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getScheduleList(params: Object) {
  return request({
    url: '/person/get-member-schedule',
    method: 'get',
    params
  })
}

// /**
//  * 获取内部下载记录
//  * @param params 要传的参数值
//  * @returns 返回接口数据
//  */
// export function getInsideDownloadList(params: Object) {
//   return request({
//     url: '/person/get-inside-download-list',
//     method: 'get',
//     params
//   })
// }

// /**
//  * 获取单位下载记录
//  * @param params 要传的参数值
//  * @returns 返回接口数据
//  */
// export function getCompanyDownloadList(params: Object) {
//   return request({
//     url: '/person/get-company-download-list',
//     method: 'get',
//     params
//   })
// }

/**
 * 人才详情页面
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getResumeInfo(params: Object) {
  return request({
    url: '/person/get-resume-info',
    method: 'get',
    params
  })
}

/**
 * 获取单位下载记录
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getCompanyDownloadList(params: Object) {
  return request({
    url: '/person/get-company-download-list',
    params
  })
}

/**
 * 获取内部下载记录
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getInsideDownloadList(params: Object) {
  return request({
    url: '/person/get-inside-download-list',
    params
  })
}

/**
 * 获取附件简历列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getResumeAttachmentList(params: Object) {
  return request({
    url: '/person/get-resume-attachment-list',
    method: 'get',
    params
  })
}

/**
 * 修改附件简历状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeResumeAttachmentStatus(params: Object) {
  return request({
    url: '/person/change-resume-attachment-status',
    method: 'post',
    data: params
  })
}

/**
 * 修改简历开放状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeResumeShowStatus(id: String) {
  return request({
    url: '/person/change-show-status',
    method: 'post',
    data: { memberId: id }
  })
}

/**
 * 修改简历匿名状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeResumeAnonymousStatus(id: String) {
  return request({
    url: '/person/change-anonymous-status',
    method: 'post',
    data: { memberId: id }
  })
}

/**
 * 修改简历代投状态
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function changeResumeProxyDeliverStatus(id: String) {
  return request({
    url: '/person/change-proxy-deliver-status',
    method: 'post',
    data: { memberId: id }
  })
}

/**
 * 简历下载
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function resumeDownload(data: Object) {
  // return request({
  //   url: '/resume/download',
  //   method: 'post',
  //   data
  // })
  axios.post('/api/resume/download', qs.stringify(data)).then((res) => {
    console.log('🚀 ~ file: person.ts ~ line 251 ~ axios.post ~ res', res)
    if (res.status === 200) {
      // 利用a标签自定义下载文件名
      const link = document.createElement('a')
      // 创建Blob对象，设置文件类型
      const blob = new Blob([res.data], {
        type: 'application/pdf' // MIME类型
      })
      link.href = URL.createObjectURL(blob) // 创建URL
      link.setAttribute('download', `${123}.pdf`) // 设置下载文件名称
      link.click() // 下载文件
      document.body.appendChild(link)
      URL.revokeObjectURL(link.href) // 释放内存
    }
  })
}

/**
 * 获取附件简历
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getPersonResumeAttachmentList(params: Object) {
  return request({
    url: '/person/get-person-resume-attachment-list',
    method: 'get',
    params
  })
}

/**
 * 人才库收藏记录
 * @returns 返回接口数据
 */
export function getResumeLibraryCollectList(params: Object) {
  return request({
    url: '/resume-library/collect-list',
    method: 'get',
    params
  })
}

/**
 * 人才库下载记录
 * @returns 返回接口数据
 */
export function getResumeLibraryDownloadList(params: Object) {
  return request({
    url: '/resume-library/download-list',
    method: 'get',
    params
  })
}

/* 识别附件简历
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function resumeAttachmentIdentify(params: Object) {
  return request({
    url: '/person/resume-attachment-identify',
    method: 'get',
    params
  })
}

/* 添加人才标签
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function resumeAddTag(params: Object) {
  return request({
    url: '/resume/add-tag',
    method: 'post',
    data: params
  })
}

/* 给人才贴标签
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function resumeEditTag(params: Object) {
  return request({
    url: '/resume/edit-tag',
    method: 'post',
    data: params
  })
}

/* 获取全部人才标签列表
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getResumeTagList(params: Object) {
  return request({
    url: '/config/get-resume-tag-list',
    method: 'get',
    params
  })
}

/**
 * 获取人才列表v2
 * @param params 要传的参数值
 * @returns 返回接口数据
 */
export function getResumeV2(params: Object) {
  return request({
    url: '/resume/search-v2',
    method: 'get',
    params
  })
}

// 人才批量贴标
export function batchAddTag(params: Object) {
  return request({
    url: '/resume/batch-add-resume-tag',
    method: 'post',
    data: params
  })
}
