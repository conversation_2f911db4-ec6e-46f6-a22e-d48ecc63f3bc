<template>
  <el-form ref="form" label-width="100px" :model="formData">
    <div class="flex">
      <el-form-item class="span-4" label="人才检索" prop="userKeyword">
        <el-input
          @keyup.enter="handleSearch"
          v-model="formData.userKeyword"
          placeholder="输入人才姓名/ID/用户名"
        ></el-input>
      </el-form-item>
      <el-form-item class="span-4" label="附件检索" prop="attachmentKeyword">
        <el-input
          @keyup.enter="handleSearch"
          v-model="formData.attachmentKeyword"
          placeholder="请填写附件名称或编号"
        ></el-input>
      </el-form-item>
      <el-form-item class="span-4" label="上传时间" prop="createTime">
        <DatePickerRange
          v-model:start="formData.createTimeFrom"
          v-model:end="formData.createTimeTo"
        />
      </el-form-item>
      <el-form-item class="span-4" label="最近使用" prop="lastApplyJobTime">
        <DatePickerRange
          v-model:start="formData.lastApplyJobTimeFrom"
          v-model:end="formData.lastApplyJobTimeTo"
        />
      </el-form-item>
      <!-- <el-form-item class="span-4" label="最高学历" prop="educationId">
        <Education v-model="formData.educationId" placeholder="不限" />
      </el-form-item> -->

      <el-form-item class="span-4" label-width="10px">
        <div class="nowrap">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button plain @click="handleResetField">重置</el-button>
          <!-- <el-link :underline="false" class="ml-10" @click="showMore = !showMore">展开</el-link> -->
        </div>
      </el-form-item>
    </div>

    <!-- <div v-show="showMore">
      <div class="flex">
        <el-form-item class="span-4" label="学科专业" prop="majorId">
          <MajorCategory v-model="formData.majorId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="所在地区">
          <Region v-model:area="formData.areaId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="工作年限" prop="workYears">
          <WorkExperience v-model="formData.workYears" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="求职状态" prop="workStatus">
          <JobStatus v-model="formData.workStatus" placeholder="不限" />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="户籍/国籍" prop="householdRegisterId">
          <nativePlace v-model="formData.householdRegisterId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="上传时间">
          <DatePickerRange
            v-model:start="formData.startUploadTime"
            v-model:end="formData.endUploadTime"
          />
        </el-form-item>
        <el-form-item class="span-4" label="最近登录时间">
          <DatePickerRange
            v-model:start="formData.startLastLoginTime"
            v-model:end="formData.endLastLoginTime"
          />
        </el-form-item>
        <el-form-item class="span-4" label="最近更新时间">
          <DatePickerRange
            v-model:start="formData.startLastUpdateTime"
            v-model:end="formData.endLastUpdateTime"
          />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="求职意向" prop="jobCategory">
          <Trade v-model="formData.jobCategory" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="意向城市">
          <Region v-model:area="formData.cityId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="工作性质" prop="natureType">
          <WorkNature v-model="formData.natureType" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="薪资要求" prop="wageId">
          <Wage v-model="formData.wageId" placeholder="不限" />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="到岗时间" prop="arriveDateType">
          <ArriveDate v-model="formData.arriveDateType" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="是否开通代投" prop="isProxyDeliver">
          <HelpSend v-model="formData.isProxyDeliver" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="海外经历" prop="isAbroad">
          <AbroadExperience v-model="formData.isAbroad" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="985/211" prop="isProjectSchool">
          <GoodHigeSchool v-model="formData.isProjectSchool" placeholder="不限" />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-5" label="博士后经历" prop="isPostdoc">
          <Postdoc v-model="formData.isPostdoc" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-5" label="年龄" prop="ageId">
          <Age v-model="formData.ageId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-5" label="职称" prop="titleId">
          <LevelTitle v-model="formData.titleId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-5" label="性别" prop="sex">
          <Gender v-model="formData.sex" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-5" label="注册来源" prop="sourceType">
          <MemberSourceType v-model="formData.sourceType" placeholder="不限" />
        </el-form-item>
      </div>
    </div> -->
  </el-form>
</template>

<script lang="ts">
import { reactive, toRaw, toRefs, ref } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
// import MajorCategory from '/@select/majorCategory.vue'
// import Education from '/@select/education.vue'
// import Region from '/@select/region.vue'
// import DatePickerRange from '/@/components/base/datePickerRange.vue'
// import AbroadExperience from '/@select/abroadExperience.vue'
// import WorkExperience from '/@select/workExperience.vue'
// import Age from '/@select/age.vue'
// import Gender from '/@select/gender.vue'
// import JobStatus from '/@select/personStatus.vue'
// import NativePlace from '/@select/nativePlace.vue'
// import Trade from '/@select/trade.vue'
// import WorkNature from '/@select/workNature.vue'
// import Wage from '/@select/wage.vue'
// import HelpSend from '/@select/helpSend.vue'
// import GoodHigeSchool from '/@select/projectSchool.vue'
// import Postdoc from '/@select/postdoc.vue'
// import LevelTitle from '/@select/levelTitle.vue'
// import MemberSourceType from '/@select/memberSourceType.vue'
// import ArriveDate from '/@select/arriveDate.vue'

export default {
  name: 'jobQuery',
  components: {
    DatePickerRange
    //   MajorCategory,
    //   Education,
    //   Region,
    //   DatePickerRange,
    //   AbroadExperience,
    //   WorkExperience,
    //   Age,
    //   Gender,
    //   JobStatus,
    //   NativePlace,
    //   Trade,
    //   WorkNature,
    //   Wage,
    //   HelpSend,
    //   GoodHigeSchool,
    //   LevelTitle,
    //   Postdoc,
    //   MemberSourceType,
    //   ArriveDate
  },
  emits: ['search', 'download', 'reset'],
  setup(props, { emit }) {
    const state = reactive({
      region: '',
      loading: false,
      showMore: false,
      formData: {
        // 用户名、用户id或者姓名
        userKeyword: '',
        // 附件名称
        attachmentKeyword: '',
        // 上传时间
        createTimeFrom: '',
        createTimeTo: '',
        // 最近使用
        lastApplyJobTimeFrom: '',
        lastApplyJobTimeTo: '',
        // 最高学历
        // educationId: '',
        // // 学科专业
        // majorId: '',
        // // 所在地区
        // areaId: '',
        // // 毕业院校
        // shcoolName: '',
        // // 工作年限
        // workYears: '',
        // // 求职状态
        // workStatus: '',
        // // 户籍国籍
        // householdRegisterId: '',
        // // 创建时间
        // startUploadTime: '',
        // endUploadTime: '',
        // // 最近登录时间
        // startLastLoginTime: '',
        // endLastLoginTime: '',
        // // 最近更新时间
        // startLastUpdateTime: '',
        // endLastUpdateTime: '',
        // // 意向职能
        // jobCategory: '',
        // // 意向城市id
        // cityId: '',
        // // 工作性质
        // natureType: '',
        // // 薪资要求
        // wageId: '',
        // // 到岗时间
        // arriveDateType: '',
        // // 是否代投
        // isProxyDeliver: '',
        // // 海外经历
        // isAbroad: '',
        // // 211
        // isProjectSchool: '',
        // // 是否有博士后经历
        // isPostdoc: '',
        // // 年龄
        // ageId: '',
        // // 职称
        // titleId: '',
        // // 性别
        // sex: '',
        // // 1倒序）
        // sortUploadTime: '',
        // // 1倒序）
        // sortLastLoginTime: '',
        // // 注册来源
        // sourceType: '',
        // 条数
        pageSize: '',
        // 页码
        page: ''
      },
      resetFormData: {}
    })

    // 重置
    const form = ref()

    const handleResetField = () => {
      form.value.resetFields()
      state.formData.createTimeFrom = ''
      state.formData.createTimeTo = ''
      state.formData.lastApplyJobTimeFrom = ''
      state.formData.lastApplyJobTimeTo = ''
      emit('reset', toRaw(state.formData))
    }

    const handleSearch = () => {
      emit('search', toRaw(state.formData))
    }

    return {
      handleSearch,
      handleResetField,
      form,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}
</style>
