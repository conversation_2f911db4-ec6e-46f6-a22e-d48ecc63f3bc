<template>
  <!--
    人才详情弹窗组件
    功能：展示单个人才的完整详细信息，包括简历、求职意向、教育经历、工作经验等
    特点：
    - 支持多人才分页浏览（上一页/下一页）
    - 标签管理功能（贴标签）
    - 账号状态管理（启用/禁用）
    - 附件简历预览和下载
    - 求职日程查看（日历形式）
    - 筛选条件显示
  -->
  <div class="person-detail-dialog">
    <!-- 主弹窗 -->
    <el-dialog
      v-model="visible"
      width="90%"
      top="5vh"
      :close-on-click-modal="false"
      :destroy-on-close="false"
      @close="handleClose"
    >
      <!-- 自定义头部 -->
      <template #header>
        <div class="dialog-header">
          <!-- 标题区域：显示人才详情和分页信息 -->
          <div class="header-title">
            <span>人才详情</span>
            <span class="page-info" v-if="totalCount > 0">
              ({{ currentIndex + 1 }} / {{ totalCount }})
            </span>
          </div>
          <!-- 操作按钮区域 -->
          <div class="header-actions">
            <!-- 功能操作按钮组 -->
            <div class="action-buttons">
              <!-- 贴标签：为当前人才添加/修改标签 -->
              <el-button type="warning" size="small" @click="handleTag"> 贴标签 </el-button>
              <!-- 查看会员信息：跳转到会员详情页面 -->
              <el-button type="primary" size="small" @click="handleViewVipInfo">
                会员信息
              </el-button>
              <!-- 求职设置 -->
              <div class="setting-inline">
                <span class="setting-label">简历隐藏:</span>
                <el-switch
                  :before-change="handleChangeResumeShowStatus"
                  v-model="resumeSettingInfo.isHideResume"
                  active-value="1"
                  inactive-value="2"
                  :disabled="!hasPermission('editResumeOpen')"
                  size="small"
                />
                <span class="setting-label">代投:</span>
                <el-switch
                  :before-change="handleChangeResumeProxyDeliverStatus"
                  v-model="resumeSettingInfo.isProxyDeliver"
                  active-value="1"
                  inactive-value="2"
                  :disabled="!hasPermission('editResumeOpen')"
                  size="small"
                />
                <span class="setting-label">匿名:</span>
                <el-switch
                  :before-change="handleChangeResumeAnonymousStatus"
                  v-model="resumeSettingInfo.isAnonymous"
                  active-value="1"
                  inactive-value="2"
                  :disabled="!hasPermission('editResumeOpen')"
                  size="small"
                />
              </div>
            </div>
            <!-- 分页导航按钮组 -->
            <div class="pagination-buttons">
              <!-- 上一页：当前不是第一页时可用 -->
              <el-button
                type="primary"
                :disabled="currentIndex <= 0"
                @click="goToPrevious"
                size="small"
              >
                上一页
              </el-button>
              <!-- 下一页：当前不是最后一页时可用 -->
              <el-button
                type="primary"
                :disabled="currentIndex >= totalCount - 1"
                @click="goToNext"
                size="small"
              >
                下一页
              </el-button>
            </div>
          </div>
        </div>
      </template>
      <div v-loading="loading" class="dialog-content">
        <!--
          筛选条件显示区域
          功能：显示当前人才列表的筛选条件，便于用户了解当前查看的人才范围
          交互：支持展开/收起显示
        -->
        <div class="filter-conditions" v-if="hasFilterConditions">
          <div class="filter-header">
            <span class="filter-title">当前筛选条件</span>
            <el-button type="text" size="small" @click="toggleFilterDisplay">
              {{ showFilterConditions ? '收起' : '展开' }}
            </el-button>
          </div>
          <div v-show="showFilterConditions" class="filter-content">
            <div class="filter-tags">
              <el-tag
                v-for="(condition, index) in activeFilterConditions"
                :key="index"
                class="filter-tag"
              >
                {{ condition.label }}：{{ condition.value }}
              </el-tag>
            </div>
          </div>
        </div>

        <!--
          人才头部信息区域
          功能：展示人才的核心基本信息和详细信息
          包含：姓名、性别、年龄、联系方式、婚姻状况、政治面貌等详细信息
        -->
        <div class="person-header" v-if="userInfo.name">
          <div class="header-info">
            <div class="main-info">
              <!-- 姓名标题 -->
              <h3 class="person-name">{{ userInfo.name }}</h3>

              <!-- 基本信息行1：性别、年龄、联系方式 -->
              <div class="basic-info" v-if="!isCancel">
                <span class="info-item">{{ userInfo.genderTxt }}，{{ userInfo.age }}岁</span>
                <span
                  class="info-item contact-item"
                  v-if="userInfo.fullMobile"
                  @click="toggleMobileDisplay"
                >
                  <el-icon class="contact-icon"><Phone /></el-icon>
                  {{ displayMobile }}
                  <el-icon class="toggle-icon"><View /></el-icon>
                </span>
                <span
                  class="info-item contact-item"
                  v-if="userInfo.email"
                  @click="toggleEmailDisplay"
                >
                  <el-icon class="contact-icon"><Message /></el-icon>
                  {{ displayEmail }}
                  <el-icon class="toggle-icon"><View /></el-icon>
                </span>
              </div>

              <!-- 详细信息行2：婚姻、政治面貌、民族、职称等 -->
              <div class="detail-info" v-if="hasDetailInfo">
                <span class="info-item" v-if="userInfo.marriageTxt">
                  婚姻状况：{{ userInfo.marriageTxt }}
                </span>
                <span class="info-item" v-if="userInfo.politicalStatusName">
                  政治面貌：{{ userInfo.politicalStatusName }}
                </span>
                <span class="info-item" v-if="userInfo.nationTxt">
                  民族：{{ userInfo.nationTxt }}
                </span>
                <span class="info-item" v-if="userInfo.titleName">
                  职称：{{ userInfo.titleName }}
                </span>
              </div>

              <!-- 详细信息行3：户籍、籍贯、学历、院校等 -->
              <div class="detail-info" v-if="hasEducationInfo">
                <span class="info-item" v-if="userInfo.householdRegisterText">
                  户籍/国籍：{{ userInfo.householdRegisterText }}
                </span>
                <span class="info-item" v-if="userInfo.nativePlaceAreaTxt">
                  籍贯：{{ userInfo.nativePlaceAreaTxt }}
                </span>
                <span class="info-item" v-if="userInfo.educationName">
                  最高学历：{{ userInfo.educationName }}
                </span>
                <span class="info-item" v-if="userInfo.schoolName">
                  毕业院校：{{ userInfo.schoolName }}
                </span>
              </div>

              <!-- 最新动态：应聘记录和时间信息 -->
              <div class="update-info">
                <span v-if="lastApplyJobName">最近应聘职位：{{ lastApplyJobName }}</span>
                <span v-if="lastApplyJobTime">最近投递时间：{{ lastApplyJobTime }}</span>
                <span v-if="lastUpdateResumeTime">最近更新简历：{{ lastUpdateResumeTime }}</span>
              </div>
            </div>

            <!-- 简历完善度圆环 -->
            <div class="resume-completion" v-if="resumePercent">
              <div class="completion-circle">
                <svg width="60" height="60" viewBox="0 0 60 60">
                  <circle
                    cx="30"
                    cy="30"
                    r="25"
                    stroke="rgba(255,255,255,0.3)"
                    stroke-width="4"
                    fill="none"
                  />
                  <circle
                    cx="30"
                    cy="30"
                    r="25"
                    stroke="#ffffff"
                    stroke-width="4"
                    fill="none"
                    :stroke-dasharray="157.08"
                    :stroke-dashoffset="157.08 - (157.08 * resumePercent) / 100"
                    stroke-linecap="round"
                    transform="rotate(-90 30 30)"
                  />
                </svg>
                <div class="completion-text">
                  <span class="percentage">{{ resumePercent }}%</span>
                  <span class="label">简历完善度</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="detail-content" :class="{ 'no-sidebar': !subscribeInfo.subscribeType }">
          <div class="content-main">
            <!--
              Tab页面结构
              功能：将人才信息按类别分组展示，提高信息可读性
              包含：在线简历、附件简历、单位下载记录、内部下载记录
            -->
            <el-tabs v-model="activeName" class="detail-tabs" type="border-card">
              <!-- 在线简历Tab：展示人才的在线简历信息 -->
              <el-tab-pane label="在线简历" name="onlineResume" v-if="!isCancel">
                <!-- 侧边锚点导航 -->
                <div class="sidebar-navigation" v-if="hasAnyResumeContent">
                  <div class="nav-title">内容导航</div>
                  <div class="nav-items">
                    <div
                      v-if="
                        userInfo.workStatusName ||
                        userInfo.arriveDateTypeName ||
                        userInfo.workExperience
                      "
                      class="nav-item"
                      :class="{ active: activeAnchor === 'job-info' }"
                      @click="scrollToAnchor('job-info')"
                    >
                      <span class="nav-dot"></span>
                      <span class="nav-text">求职信息</span>
                    </div>
                    <div
                      v-if="userInfo.advantage"
                      class="nav-item"
                      :class="{ active: activeAnchor === 'advantage' }"
                      @click="scrollToAnchor('advantage')"
                    >
                      <span class="nav-dot"></span>
                      <span class="nav-text">个人优势</span>
                    </div>
                    <div
                      v-if="intentionList.length > 0"
                      class="nav-item"
                      :class="{ active: activeAnchor === 'intention' }"
                      @click="scrollToAnchor('intention')"
                    >
                      <span class="nav-dot"></span>
                      <span class="nav-text">求职意向</span>
                    </div>
                    <div
                      v-if="educationList.length > 0"
                      class="nav-item"
                      :class="{ active: activeAnchor === 'education' }"
                      @click="scrollToAnchor('education')"
                    >
                      <span class="nav-dot"></span>
                      <span class="nav-text">教育经历</span>
                    </div>
                    <div
                      v-if="researchDirection"
                      class="nav-item"
                      :class="{ active: activeAnchor === 'research' }"
                      @click="scrollToAnchor('research')"
                    >
                      <span class="nav-dot"></span>
                      <span class="nav-text">研究方向</span>
                    </div>
                    <div
                      v-if="workList.length > 0"
                      class="nav-item"
                      :class="{ active: activeAnchor === 'work' }"
                      @click="scrollToAnchor('work')"
                    >
                      <span class="nav-dot"></span>
                      <span class="nav-text">工作经历</span>
                    </div>
                    <div
                      v-if="projectList.length > 0"
                      class="nav-item"
                      :class="{ active: activeAnchor === 'project' }"
                      @click="scrollToAnchor('project')"
                    >
                      <span class="nav-dot"></span>
                      <span class="nav-text">项目经历</span>
                    </div>
                    <div
                      v-if="pageList.length > 0 || patentList.length > 0 || bookList.length > 0"
                      class="nav-item"
                      :class="{ active: activeAnchor === 'academic' }"
                      @click="scrollToAnchor('academic')"
                    >
                      <span class="nav-dot"></span>
                      <span class="nav-text">学术成果</span>
                    </div>
                    <div
                      v-if="rewardList.length > 0 || otherRewardList.length > 0"
                      class="nav-item"
                      :class="{ active: activeAnchor === 'awards' }"
                      @click="scrollToAnchor('awards')"
                    >
                      <span class="nav-dot"></span>
                      <span class="nav-text">荣誉奖励</span>
                    </div>
                    <div
                      v-if="
                        skillList.length > 0 ||
                        certificateList.length > 0 ||
                        otherSkillList.length > 0
                      "
                      class="nav-item"
                      :class="{ active: activeAnchor === 'skills' }"
                      @click="scrollToAnchor('skills')"
                    >
                      <span class="nav-dot"></span>
                      <span class="nav-text">技能特长</span>
                    </div>
                  </div>
                </div>

                <!-- 简历内容区域 -->
                <div class="modern-resume">
                  <!-- 求职信息卡片 -->
                  <div id="job-info" class="resume-card job-info-card">
                    <div class="card-header">
                      <div class="card-title">
                        <span>求职信息</span>
                      </div>
                    </div>
                    <div class="card-content">
                      <div class="info-row">
                        <div class="info-item" v-if="userInfo.workStatusName">
                          <div class="item-content">
                            <span class="item-label">求职状态</span>
                            <span
                              class="item-value status-tag"
                              :class="getStatusClass(userInfo.workStatusName)"
                            >
                              {{ userInfo.workStatusName }}
                            </span>
                          </div>
                        </div>
                        <div class="info-item" v-if="userInfo.arriveDateTypeName">
                          <div class="item-content">
                            <span class="item-label">到岗时间</span>
                            <span class="item-value">{{ userInfo.arriveDateTypeName }}</span>
                          </div>
                        </div>
                        <div class="info-item" v-if="userInfo.workExperience">
                          <div class="item-content">
                            <span class="item-label">工作经验</span>
                            <span class="item-value">{{ userInfo.workExperience }}</span>
                          </div>
                        </div>
                      </div>
                      <div class="info-row">
                        <div class="info-item" v-if="userInfo.educationName">
                          <div class="item-content">
                            <span class="item-label">最高学历</span>
                            <span class="item-value education-tag">{{
                              userInfo.educationName
                            }}</span>
                          </div>
                        </div>
                        <div class="info-item" v-if="userInfo.schoolName">
                          <div class="item-content">
                            <span class="item-label">毕业院校</span>
                            <span class="item-value">{{ userInfo.schoolName }}</span>
                          </div>
                        </div>
                        <div class="info-item" v-if="userInfo.identityExperience">
                          <div class="item-content">
                            <span class="item-label">身份经验</span>
                            <span class="item-value">{{ userInfo.identityExperience }}</span>
                          </div>
                        </div>
                      </div>
                      <div
                        class="info-row"
                        v-if="userInfo.lastLoginTime || userInfo.lastActiveTime"
                      >
                        <div class="info-item" v-if="userInfo.lastLoginTime">
                          <div class="item-content">
                            <span class="item-label">最近登录</span>
                            <span class="item-value time-value">{{ userInfo.lastLoginTime }}</span>
                          </div>
                        </div>
                        <div class="info-item" v-if="userInfo.lastActiveTime">
                          <div class="item-content">
                            <span class="item-label">最近活跃</span>
                            <span class="item-value time-value">{{ userInfo.lastActiveTime }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 个人优势卡片 -->
                  <div id="advantage" class="resume-card advantage-card" v-if="userInfo.advantage">
                    <div class="card-header">
                      <div class="card-title">
                        <span>个人优势</span>
                      </div>
                    </div>
                    <div class="card-content">
                      <div class="advantage-text">
                        {{ userInfo.advantage }}
                      </div>
                    </div>
                  </div>

                  <!-- 求职意向卡片 -->
                  <div
                    id="intention"
                    class="resume-card intention-card"
                    v-if="intentionList.length > 0"
                  >
                    <div class="card-header">
                      <div class="card-title">
                        <span>求职意向</span>
                      </div>
                      <div class="count-badge">{{ intentionList.length }} 个</div>
                    </div>
                    <div class="card-content">
                      <div class="intention-list">
                        <div
                          v-for="(intention, index) in intentionList"
                          :key="index"
                          class="intention-item"
                        >
                          <div class="intention-header">
                            <span class="intention-index">{{ index + 1 }}</span>
                            <span class="intention-title">{{ intention.jobCategoryName }}</span>
                          </div>
                          <div class="intention-details">
                            <div class="detail-item">
                              <span>{{ intention.wageName }}</span>
                            </div>
                            <div class="detail-item">
                              <span>{{ intention.areaName }}</span>
                            </div>
                            <div class="detail-item">
                              <span>{{ intention.natureName }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 教育经历卡片 -->
                  <div
                    id="education"
                    class="resume-card education-card"
                    v-if="educationList.length > 0"
                  >
                    <div class="card-header">
                      <div class="card-title">
                        <span>教育经历</span>
                      </div>
                      <div class="count-badge">{{ educationList.length }} 个</div>
                    </div>
                    <div class="card-content">
                      <div class="education-timeline">
                        <div
                          v-for="(edu, index) in educationList"
                          :key="index"
                          class="timeline-item"
                        >
                          <div class="timeline-dot"></div>
                          <div class="timeline-content">
                            <div class="edu-header">
                              <span class="edu-school">
                                {{ edu.school }}
                                <el-tag v-if="edu.isRecruitment == 1" type="success" size="small"
                                  >统招</el-tag
                                >
                              </span>
                              <span class="edu-time"
                                >{{ edu.studyBeginDate }} - {{ edu.studyEndDate }}</span
                              >
                            </div>
                            <div class="edu-details">
                              <div class="edu-major">
                                {{ edu.majorName }} · {{ edu.educationName }}
                              </div>
                              <div class="edu-extra" v-if="edu.college || edu.mentor">
                                <span v-if="edu.college">{{ edu.college }}</span>
                                <span v-if="edu.mentor">导师: {{ edu.mentor }}</span>
                              </div>
                              <div
                                class="edu-tags"
                                v-if="edu.isOverseasStudy == 1 || edu.isProjectSchool == 1"
                              >
                                <el-tag v-if="edu.isOverseasStudy == 1" type="success" size="small"
                                  >海外</el-tag
                                >
                                <el-tag v-if="edu.isProjectSchool == 1" type="warning" size="small"
                                  >985/211</el-tag
                                >
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 研究方向卡片 -->
                  <div id="research" class="resume-card research-card" v-if="researchDirection">
                    <div class="card-header">
                      <div class="card-title">
                        <span>研究方向</span>
                      </div>
                    </div>
                    <div class="card-content">
                      <div class="research-text">
                        {{ researchDirection }}
                      </div>
                    </div>
                  </div>

                  <!-- 工作经历卡片 -->
                  <div id="work" class="resume-card work-card" v-if="workList.length > 0">
                    <div class="card-header">
                      <div class="card-title">
                        <el-icon class="title-icon"><Briefcase /></el-icon>
                        <span>工作/实习/研究经历</span>
                      </div>
                      <div class="count-badge">{{ workList.length }} 个</div>
                    </div>
                    <div class="card-content">
                      <div class="work-timeline">
                        <div v-for="(work, index) in workList" :key="index" class="timeline-item">
                          <div class="timeline-dot work-dot"></div>
                          <div class="timeline-content">
                            <div class="work-header">
                              <span class="work-company">
                                {{ work.company }}
                                <el-tag v-if="work.isOverseas == 1" type="success" size="small"
                                  >海外经历</el-tag
                                >
                                <el-tag v-if="work.isPostdoc == 1" type="warning" size="small"
                                  >博士后经历</el-tag
                                >
                                <el-tag v-if="work.isPractice == 1" type="info" size="small"
                                  >实习</el-tag
                                >
                              </span>
                              <span class="work-time"
                                >{{ work.jobBeginDate }} - {{ work.jobEndDate || '至今' }}</span
                              >
                            </div>
                            <div class="work-details">
                              <div class="work-position">{{ work.jobName }}</div>
                              <div class="work-department" v-if="work.department">
                                {{ work.department }}
                              </div>
                              <div class="work-description" v-if="work.jobContent">
                                {{ work.jobContent }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 项目经历卡片 -->
                  <div id="project" class="resume-card project-card" v-if="projectList.length > 0">
                    <div class="card-header">
                      <div class="card-title">
                        <el-icon class="title-icon"><Briefcase /></el-icon>
                        <span>项目经历</span>
                      </div>
                      <div class="count-badge">{{ projectList.length }} 个</div>
                    </div>
                    <div class="card-content">
                      <div class="project-timeline">
                        <div
                          v-for="(project, index) in projectList"
                          :key="index"
                          class="timeline-item"
                        >
                          <div class="timeline-dot project-dot"></div>
                          <div class="timeline-content">
                            <div class="project-header">
                              <span class="project-name">
                                {{ project.name }}
                                <el-tag v-if="project.isClose == 1" type="success" size="small"
                                  >已结项</el-tag
                                >
                                <el-tag v-else type="warning" size="small">进行中</el-tag>
                              </span>
                              <span class="project-time"
                                >{{ project.beginDate }} - {{ project.endDate || '至今' }}</span
                              >
                            </div>
                            <div class="project-details">
                              <div class="project-role">{{ project.role }}</div>
                              <div class="project-company" v-if="project.company">
                                {{ project.company }}
                              </div>
                              <div class="project-category" v-if="project.categoryName">
                                {{ project.categoryName }}
                              </div>
                              <div class="project-description" v-if="project.description">
                                {{ project.description }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 学术成果卡片 -->
                  <div
                    id="academic"
                    class="resume-card academic-card"
                    v-if="pageList.length > 0 || patentList.length > 0 || bookList.length > 0"
                  >
                    <div class="card-header">
                      <div class="card-title">
                        <span>学术成果</span>
                      </div>
                      <div class="count-badge">
                        {{ pageList.length + patentList.length + bookList.length }} 个
                      </div>
                    </div>
                    <div class="card-content">
                      <!-- 学术论文 -->
                      <div v-if="pageList.length > 0" class="academic-section">
                        <div class="sub-section-title">
                          <el-icon class="sub-icon"><Document /></el-icon>
                          <span>学术论文</span>
                          <el-tag type="primary" size="small">{{ pageList.length }}</el-tag>
                        </div>
                        <div class="academic-grid">
                          <div
                            v-for="(paper, index) in pageList"
                            :key="index"
                            class="academic-item paper-item"
                          >
                            <div class="academic-header">
                              <div class="academic-title">{{ paper.title }}</div>
                              <div class="academic-date">{{ paper.publishDate }}</div>
                            </div>
                            <div class="academic-details">
                              <div class="detail-row">
                                <span class="detail-label">期刊号:</span>
                                <span class="detail-value">{{ paper.serialNumber }}</span>
                              </div>
                              <div class="detail-row">
                                <span class="detail-label">收录情况:</span>
                                <span class="detail-value">{{ paper.recordSituation }}</span>
                              </div>
                              <div class="detail-row">
                                <span class="detail-label">作者位置:</span>
                                <span class="detail-value">{{ paper.positionText }}</span>
                              </div>
                              <div class="detail-row" v-if="paper.impactFactor">
                                <span class="detail-label">影响因子:</span>
                                <span class="detail-value impact-factor">{{
                                  paper.impactFactor
                                }}</span>
                              </div>
                            </div>
                            <div class="academic-description" v-if="paper.description">
                              {{ paper.description }}
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 学术专利 -->
                      <div v-if="patentList.length > 0" class="academic-section">
                        <div class="sub-section-title">
                          <el-icon class="sub-icon"><Star /></el-icon>
                          <span>学术专利</span>
                          <el-tag type="success" size="small">{{ patentList.length }}</el-tag>
                        </div>
                        <div class="academic-grid">
                          <div
                            v-for="(patent, index) in patentList"
                            :key="index"
                            class="academic-item patent-item"
                          >
                            <div class="academic-header">
                              <div class="academic-title">{{ patent.name }}</div>
                              <div class="academic-date">{{ patent.authorizationDate }}</div>
                            </div>
                            <div class="academic-details">
                              <div class="detail-row">
                                <span class="detail-label">专利号:</span>
                                <span class="detail-value">{{ patent.number }}</span>
                              </div>
                              <div class="detail-row">
                                <span class="detail-label">发明人位置:</span>
                                <span class="detail-value">{{ patent.positionText }}</span>
                              </div>
                              <div class="detail-row">
                                <span class="detail-label">完成状态:</span>
                                <span class="detail-value">{{ patent.finishStatus }}</span>
                              </div>
                            </div>
                            <div class="academic-description" v-if="patent.description">
                              {{ patent.description }}
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 学术专著 -->
                      <div v-if="bookList.length > 0" class="academic-section">
                        <div class="sub-section-title">
                          <el-icon class="sub-icon"><Document /></el-icon>
                          <span>学术专著</span>
                          <el-tag type="warning" size="small">{{ bookList.length }}</el-tag>
                        </div>
                        <div class="academic-grid">
                          <div
                            v-for="(book, index) in bookList"
                            :key="index"
                            class="academic-item book-item"
                          >
                            <div class="academic-header">
                              <div class="academic-title">{{ book.name }}</div>
                              <div class="academic-date">{{ book.publishDate }}</div>
                            </div>
                            <div class="academic-details">
                              <div class="detail-row" v-if="book.words">
                                <span class="detail-label">字数:</span>
                                <span class="detail-value">{{ book.words }}</span>
                              </div>
                              <div class="detail-row" v-if="book.publishAmount">
                                <span class="detail-label">发行量:</span>
                                <span class="detail-value">{{ book.publishAmount }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 荣誉奖励卡片 -->
                  <div
                    id="awards"
                    class="resume-card awards-card"
                    v-if="rewardList.length > 0 || otherRewardList.length > 0"
                  >
                    <div class="card-header">
                      <div class="card-title">
                        <span>荣誉奖励</span>
                      </div>
                      <div class="count-badge">
                        {{ rewardList.length + otherRewardList.length }} 个
                      </div>
                    </div>
                    <div class="card-content">
                      <!-- 学术奖励 -->
                      <div v-if="rewardList.length > 0" class="awards-section">
                        <div class="sub-section-title">
                          <span>学术奖励</span>
                          <el-tag type="success" size="small">{{ rewardList.length }}</el-tag>
                        </div>
                        <div class="awards-grid">
                          <div
                            v-for="(reward, index) in rewardList"
                            :key="index"
                            class="award-item academic-award"
                          >
                            <div class="award-header">
                              <div class="award-name">{{ reward.name }}</div>
                              <div class="award-date">{{ reward.obtainDate }}</div>
                            </div>
                            <div class="award-details">
                              <div class="detail-row">
                                <span class="detail-label">奖励级别:</span>
                                <span class="detail-value level-badge">{{ reward.level }}</span>
                              </div>
                              <div class="detail-row">
                                <span class="detail-label">获奖角色:</span>
                                <span class="detail-value">{{ reward.role }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 其他奖励 -->
                      <div v-if="otherRewardList.length > 0" class="awards-section">
                        <div class="sub-section-title">
                          <span>其他奖励</span>
                          <el-tag type="info" size="small">{{ otherRewardList.length }}</el-tag>
                        </div>
                        <div class="awards-grid">
                          <div
                            v-for="(reward, index) in otherRewardList"
                            :key="index"
                            class="award-item other-award"
                          >
                            <div class="award-header">
                              <div class="award-name">{{ reward.name }}</div>
                              <div class="award-date">{{ reward.obtainDate }}</div>
                            </div>
                            <div class="award-details">
                              <div class="detail-row">
                                <span class="detail-label">奖励级别:</span>
                                <span class="detail-value level-badge">{{ reward.level }}</span>
                              </div>
                              <div class="detail-row">
                                <span class="detail-label">获奖角色:</span>
                                <span class="detail-value">{{ reward.role }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 技能特长卡片 -->
                  <div
                    id="skills"
                    class="resume-card skills-card"
                    v-if="
                      skillList.length > 0 ||
                      certificateList.length > 0 ||
                      otherSkillList.length > 0
                    "
                  >
                    <div class="card-header">
                      <div class="card-title">
                        <span>技能特长</span>
                      </div>
                      <div class="count-badge">
                        {{ skillList.length + certificateList.length + otherSkillList.length }} 个
                      </div>
                    </div>
                    <div class="card-content">
                      <!-- 技能/语言 -->
                      <div v-if="skillList.length > 0" class="skills-section">
                        <div class="sub-section-title">
                          <span>技能/语言</span>
                          <el-tag type="primary" size="small">{{ skillList.length }}</el-tag>
                        </div>
                        <div class="skills-grid">
                          <div
                            v-for="(skill, index) in skillList"
                            :key="index"
                            class="skill-item language-skill"
                          >
                            <div class="skill-name">{{ skill.skillName }}</div>
                            <div class="skill-level">
                              <span class="level-text">{{ skill.degreeTypeName }}</span>
                              <div class="level-indicator">
                                <div
                                  class="level-bar"
                                  :class="getSkillLevelClass(skill.degreeTypeName)"
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 资质证书 -->
                      <div v-if="certificateList.length > 0" class="skills-section">
                        <div class="sub-section-title">
                          <span>资质证书</span>
                          <el-tag type="success" size="small">{{ certificateList.length }}</el-tag>
                        </div>
                        <div class="certificates-grid">
                          <div
                            v-for="(cert, index) in certificateList"
                            :key="index"
                            class="certificate-item"
                          >
                            <div class="certificate-header">
                              <div class="certificate-name">{{ cert.certificateName }}</div>
                              <div class="certificate-date">{{ cert.obtainDate }}</div>
                            </div>
                            <div class="certificate-details" v-if="cert.score">
                              <span class="detail-label">成绩/分数:</span>
                              <span class="detail-value score-badge">{{ cert.score }}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 其他技能 -->
                      <div v-if="otherSkillList.length > 0" class="skills-section">
                        <div class="sub-section-title">
                          <span>其他技能</span>
                          <el-tag type="info" size="small">{{ otherSkillList.length }}</el-tag>
                        </div>
                        <div class="other-skills-grid">
                          <div
                            v-for="(skill, index) in otherSkillList"
                            :key="index"
                            class="other-skill-item"
                          >
                            <div class="skill-header">
                              <div class="skill-name">{{ skill.name }}</div>
                              <div class="skill-level">{{ skill.degreeTypeName }}</div>
                            </div>
                            <div class="skill-description" v-if="skill.description">
                              {{ skill.description }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 简历内容区域结束 -->
                </div>
              </el-tab-pane>

              <!-- 附件简历 -->
              <el-tab-pane label="附件简历" name="attachment" v-if="!isCancel">
                <el-table :data="attachmentList" style="width: 100%">
                  <el-table-column prop="addTime" label="上传时间" width="180" />
                  <el-table-column prop="fileName" label="简历名称" width="180" />
                  <el-table-column
                    align="center"
                    header-align="center"
                    label="操作"
                    show-overflow-tooltip
                  >
                    <template #default="{ row }">
                      <el-button type="text" size="small" @click="handleDownloadAttachment(row)">
                        下载
                      </el-button>
                      <el-button type="text" size="small" @click="handlePreviewAttachment(row)">
                        预览
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>

              <!-- 单位下载记录 -->
              <el-tab-pane label="单位下载记录" name="companyDown">
                <CompanyDownload :id="memberId" />
              </el-tab-pane>

              <!-- 内部下载记录 -->
              <el-tab-pane label="内部下载记录" name="insideDown">
                <InsideDownload :id="memberId" />
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 右侧操作面板 -->
          <div class="content-sidebar">
            <!-- 职位订阅 -->
            <div class="subscribe-panel" v-if="subscribeInfo.subscribeType">
              <div class="setting-title">职位订阅</div>
              <div class="subscribe-content">
                <div class="subscribe-item">
                  <span class="subscribe-label">意向职位：</span>
                  <span class="subscribe-value">{{ subscribeInfo.jobCategoryText }}</span>
                </div>
                <div class="subscribe-item">
                  <span class="subscribe-label">意向城市：</span>
                  <span class="subscribe-value">{{ subscribeInfo.areaText }}</span>
                </div>
                <div class="subscribe-item">
                  <span class="subscribe-label">学历要求：</span>
                  <span class="subscribe-value">{{ subscribeInfo.educationText }}</span>
                </div>
                <div class="subscribe-item">
                  <span class="subscribe-label">推送渠道：</span>
                  <span class="subscribe-value">{{ subscribeInfo.subscribeType }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 标签弹窗 -->
    <TagDialog
      ref="tagDialogRef"
      :data="tagIds"
      :resumeId="userInfo.resumeId"
      @confirm="handleTagConfirm"
      @resetTagList="handleResetTagList"
    />

    <!-- 附件预览弹窗 -->
    <el-dialog
      v-model="previewOption.visible"
      title="附件预览"
      width="80%"
      top="5vh"
      destroy-on-close
    >
      <ResumeAttachmentPreview
        v-if="previewOption.visible"
        v-model:token="previewOption.token"
        v-model:resumeId="previewOption.resumeId"
        @close="previewOption.visible = false"
      />
    </el-dialog>
  </div>
</template>

<script lang="ts">
/**
 * 人才详情弹窗组件
 *
 * 功能概述：
 * 1. 展示单个人才的完整详细信息
 * 2. 支持多人才分页浏览
 * 3. 提供人才管理功能（标签、状态管理）
 * 4. 展示求职日程和附件信息
 *
 * 主要数据结构：
 * - userInfo: 用户基本信息对象
 * - educationList: 教育经历数组
 * - workList: 工作经历数组
 * - projectList: 项目经历数组
 * - skillList: 技能列表
 * - pageList/patentList/bookList: 学术成果
 * - rewardList/otherRewardList: 荣誉奖励
 *
 * 使用场景：
 * 在人才管理页面中点击某个人才时弹出此详情弹窗
 */

import {
  defineComponent,
  reactive,
  toRefs,
  computed,
  ref,
  onMounted,
  onUnmounted,
  nextTick
} from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Phone,
  Message,
  View,
  User,
  Briefcase,
  Document,
  Star,
  Calendar,
  Location,
  Clock,
  Edit,
  Search
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  getResumeInfo, // 获取人才简历详情API
  getPersonResumeAttachmentList, // 获取附件简历列表API
  changeResumeShowStatus, // 修改简历显示状态API
  changeResumeAnonymousStatus, // 修改匿名状态API
  changeResumeProxyDeliverStatus // 修改代投状态API
} from '/@/api/person'
import { lockAccount, unlockAccount } from '/@/api/member' // 账号状态管理API
import { useStore } from '/@/store/index'
import TagDialog from './tagDialog.vue' // 标签管理弹窗
import ResumeAttachmentPreview from '/@/components/resumeAttachmentPreview/index.vue' // 简历预览组件
import CompanyDownload from './companyDownload.vue' // 单位下载记录组件
import InsideDownload from './insideDownload.vue' // 内部下载记录组件

export default defineComponent({
  name: 'personDetailDialog',
  components: {
    TagDialog,
    ResumeAttachmentPreview,
    CompanyDownload,
    InsideDownload,
    Phone,
    Message,
    View,
    User,
    Briefcase,
    Document,
    Star,
    Calendar,
    Location,
    Clock,
    Edit,
    Search
  },
  emits: ['refresh'], // 向父组件发送刷新事件

  /**
   * 组件设置
   * @param _ - setup上下文（未使用）
   * @param emit - 事件发射器，用于向父组件通信
   */
  setup(_, { emit }) {
    const store = useStore()
    const router = useRouter()
    const tagDialogRef = ref() // 标签弹窗引用

    // 响应式状态对象
    const state = reactive({
      // 弹窗控制
      visible: false, // 弹窗显示状态
      loading: false, // 加载状态
      isCancel: false,

      // 分页相关
      memberId: '', // 当前人才ID
      currentIndex: 0, // 当前人才在列表中的索引
      totalCount: 0, // 人才总数量
      personList: [] as any[], // 人才列表数据

      // 简历信息
      resumePercent: 0, // 简历完整度百分比
      activeName: 'onlineResume', // 当前激活的Tab

      // 用户基本信息
      userInfo: {} as any, // 用户详细信息对象
      lastApplyJobName: '', // 最近应聘职位名称
      lastApplyJobTime: '', // 最近投递时间
      lastUpdateResumeTime: '', // 最近更新简历时间

      // 各类详细信息数组
      intentionList: [] as any[], // 求职意向列表
      educationList: [] as any[], // 教育经历列表
      workList: [] as any[], // 工作经历列表
      projectList: [] as any[], // 项目经历列表
      skillList: [] as any[], // 技能列表
      certificateList: [] as any[], // 证书列表
      otherSkillList: [] as any[], // 其他技能列表

      // 学术成果
      pageList: [] as any[], // 论文列表
      patentList: [] as any[], // 专利列表
      bookList: [] as any[], // 专著列表

      // 荣誉奖励
      rewardList: [] as any[], // 学术奖励列表
      otherRewardList: [] as any[], // 其他奖励列表

      // 其他信息
      researchDirection: '', // 研究方向

      // 筛选条件相关
      filterConditions: {} as any, // 原始筛选条件对象
      showFilterConditions: false, // 是否显示筛选条件

      // 锚点导航相关
      activeAnchor: '', // 当前激活的锚点
      activeFilterConditions: [] as any[], // 处理后的筛选条件显示列表

      // 求职设置信息
      resumeSettingInfo: {
        shieldCompanyAmount: 0, // 屏蔽公司数量
        isHideResume: '2', // 简历隐藏状态（1隐藏，2显示）
        isAnonymous: '2', // 匿名投递状态（1匿名，2实名）
        isProxyDeliver: '2' // 代投状态（1开通，2关闭）
      },

      // 附件简历列表
      attachmentList: [] as any[],

      // 标签相关
      tagIds: [] as any[], // 当前人才的标签ID列表

      // 预览相关
      previewOption: {
        visible: false, // 预览弹窗显示状态
        token: '', // 预览令牌
        resumeId: '' // 简历ID
      },

      // 职位订阅信息
      subscribeInfo: {
        areaText: '', // 意向地区文本
        educationText: '', // 学历要求文本
        jobCategoryText: '', // 职位类别文本
        subscribeType: '' // 订阅类型
      },

      // 脱敏状态控制
      showFullMobile: false, // 是否显示完整手机号
      showFullEmail: false // 是否显示完整邮箱
    })

    /**
     * 打开人才详情弹窗
     *
     * @param memberId - 人才会员ID
     * @param personList - 人才列表（用于分页浏览）
     * @param currentIndex - 当前人才在列表中的索引
     * @param filterConditions - 当前筛选条件（用于显示）
     */
    const open = async (
      memberId: string,
      personList: any[] = [],
      currentIndex: number = 0,
      filterConditions: any = {}
    ) => {
      state.visible = true
      state.memberId = memberId
      state.personList = personList
      state.currentIndex = currentIndex
      state.totalCount = personList.length
      state.filterConditions = filterConditions
      state.showFilterConditions = false

      // 重置脱敏状态
      state.showFullMobile = false
      state.showFullEmail = false

      // 处理筛选条件，转换为可显示的格式
      processFilterConditions()

      // 加载人才详细信息
      await loadPersonDetail()

      // 初始化滚动监听器
      addScrollListener()
    }

    /**
     * 加载人才详细信息
     * 功能：从后端API获取人才的完整详细信息
     * 包括：基本信息、教育经历、工作经验、项目经历等
     */
    const loadPersonDetail = async () => {
      if (!state.memberId) return

      state.loading = true
      try {
        // 调用API获取人才详情
        const response = await getResumeInfo({ memberId: state.memberId })

        // 设置基本信息
        state.userInfo = response.userInfo || {}
        state.isCancel = response.userInfo.memberStatus == 10
        state.lastApplyJobName = response.lastApplyJobName || ''
        state.lastApplyJobTime = response.lastApplyJobTime || ''
        state.lastUpdateResumeTime = response.lastUpdateResumeTime || ''
        state.resumePercent = response.resumePercent || 0

        // 设置各类详细信息
        state.intentionList = response.intentionList || []
        state.educationList = response.educationList || []
        state.workList = response.workList || []
        state.projectList = response.projectList || []
        state.skillList = response.skillList || []
        state.certificateList = response.certificateList || []
        state.otherSkillList = response.otherSkillList || []

        // 设置学术成果
        state.pageList = response.pageList || []
        state.patentList = response.patentList || []
        state.bookList = response.bookList || []

        // 设置荣誉奖励
        state.rewardList = response.rewardList || []
        state.otherRewardList = response.otherRewardList || []

        // 设置其他信息
        state.researchDirection = response.researchDirection || ''

        // 设置求职设置信息
        state.resumeSettingInfo = response.resumeSettingInfo || {
          shieldCompanyAmount: 0,
          isHideResume: '2',
          isAnonymous: '2',
          isProxyDeliver: '2'
        }

        // 设置标签信息
        state.tagIds = response.userInfo?.resumeTagId || []

        // 设置职位订阅信息
        state.subscribeInfo = response.subscribeInfo || {
          areaText: '',
          educationText: '',
          jobCategoryText: '',
          subscribeType: ''
        }

        // 加载附件简历
        await loadAttachmentList()
      } catch (error) {
        console.error('加载人才详情失败:', error)
      } finally {
        state.loading = false
      }
    }

    /**
     * 加载附件简历列表
     * 功能：获取当前人才上传的所有附件简历文件
     * 处理：为每个附件添加下载URL
     */
    const loadAttachmentList = async () => {
      try {
        const response = await getPersonResumeAttachmentList({ memberId: state.memberId })
        state.attachmentList = response.map((item: any) => ({
          ...item,
          url: `/person/resume-attachment-download?resumeId=${item.resumeId}&token=${item.token}`
        }))
      } catch (error) {
        console.error('加载附件简历失败:', error)
      }
    }

    /**
     * 切换到上一页人才
     * 功能：在人才列表中向前导航，加载上一个人才的详细信息
     */
    const goToPrevious = async () => {
      if (state.currentIndex > 0) {
        state.currentIndex--
        const previousPerson = state.personList[state.currentIndex]
        if (previousPerson && previousPerson.memberId) {
          state.memberId = previousPerson.memberId
          await loadPersonDetail()
        }
      }
    }

    /**
     * 切换到下一页人才
     * 功能：在人才列表中向后导航，加载下一个人才的详细信息
     */
    const goToNext = async () => {
      if (state.currentIndex < state.totalCount - 1) {
        state.currentIndex++
        const nextPerson = state.personList[state.currentIndex]
        if (nextPerson && nextPerson.memberId) {
          state.memberId = nextPerson.memberId
          await loadPersonDetail()
        }
      }
    }

    /**
     * 获取选项的中文显示值
     * 功能：根据选项值从选项列表中获取对应的中文显示文本
     * @param optionList - 选项列表
     * @param value - 选项值
     * @returns 中文显示文本
     */
    const getOptionText = (optionList: any[], value: any) => {
      if (!optionList || !Array.isArray(optionList)) return value
      const option = optionList.find((item) => item.k == value)
      return option ? option.v : value
    }

    /**
     * 处理筛选条件
     * 功能：将复杂的筛选条件对象转换为可读的标签格式，便于用户理解当前筛选条件
     *
     * 处理逻辑：
     * 1. 遍历所有筛选条件
     * 2. 根据条件类型进行不同的处理（映射、翻译、格式化）
     * 3. 生成适合显示的标签列表
     */
    const processFilterConditions = () => {
      const conditions: any[] = []
      const filterData = state.filterConditions
      const { selectList } = store.state.selectList

      // 定义筛选条件的中文标签映射
      const filterLabels = {
        userKeyword: '人才检索',
        mobile: '手机号',
        mobileCode: '手机号段',
        educationId: '最高学历',
        identityType: '身份',
        workYears: '工作年限',
        graduateBeginDate: '毕业开始时间',
        graduateEndDate: '毕业结束时间',
        workStatus: '求职状态',
        householdRegisterId: '户籍/国籍',
        areaId: '现居住地',
        startCreateTime: '注册开始时间',
        endCreateTime: '注册结束时间',
        startLastLoginTime: '最近登录开始时间',
        endLastLoginTime: '最近登录结束时间',
        startLastUpdateTime: '最近更新开始时间',
        endLastUpdateTime: '最近更新结束时间',
        jobCategory: '求职意向',
        cityId: '意向城市',
        natureType: '工作性质',
        wageId: '薪资要求',
        arriveDateType: '到岗时间',
        isProxyDeliver: '是否开通代投',
        isAbroad: '海外经历',
        isProjectSchool: '985/211',
        isPostdoc: '博士后经历',
        textCombination: '组合搜索',
        researchDirection: '研究方向',
        academicAchievement: '学术成果'
      }

      // 定义特殊值的映射
      const specialValueMaps = {
        identityType: {
          '-1': '-',
          '1': '职场人',
          '2': '应届生/在校生'
        },
        isProxyDeliver: {
          '1': '是',
          '0': '否'
        },
        isAbroad: {
          '1': '是',
          '0': '否'
        },
        isProjectSchool: {
          '1': '是',
          '0': '否'
        },
        isPostdoc: {
          '1': '是',
          '0': '否'
        }
      }

      // 处理各种筛选条件
      Object.keys(filterData).forEach((key) => {
        const value = filterData[key]
        if (value && value !== '' && value !== null && value !== undefined) {
          // 跳过空数组
          if (Array.isArray(value) && value.length === 0) return

          const label = filterLabels[key] || key
          let displayValue = value

          // 处理特殊值映射
          if (specialValueMaps[key] && specialValueMaps[key][value]) {
            displayValue = specialValueMaps[key][value]
          }
          // 处理从store中获取的选项值
          else if (key === 'educationId') {
            displayValue = getOptionText(selectList.educationList, value)
          } else if (key === 'workYears') {
            displayValue = getOptionText(selectList.experienceList, value)
          } else if (key === 'workStatus') {
            displayValue = getOptionText(selectList.personStatusList, value)
          } else if (key === 'householdRegisterId') {
            displayValue = getOptionText(selectList.nativePlaceList, value)
          } else if (key === 'natureType') {
            displayValue = getOptionText(selectList.wordNatureList, value)
          } else if (key === 'wageId') {
            displayValue = getOptionText(selectList.wageList, value)
          } else if (key === 'arriveDateType') {
            displayValue = getOptionText(selectList.arriveDateList, value)
          }
          // 处理数组类型的值
          else if (Array.isArray(value)) {
            if (key === 'areaId' || key === 'cityId') {
              // 地区数组需要特殊处理，这里简化显示
              displayValue = `已选择${value.length}个地区`
            } else {
              displayValue = value.join(', ')
            }
          }
          // 处理时间范围
          else if ((key.includes('Date') || key.includes('Time')) && typeof value === 'object') {
            if (value.start && value.end) {
              displayValue = `${value.start} 至 ${value.end}`
            }
          }

          conditions.push({
            label,
            value: displayValue
          })
        }
      })

      state.activeFilterConditions = conditions
    }

    /**
     * 切换筛选条件显示状态
     * 功能：控制筛选条件区域的展开/收起
     */
    const toggleFilterDisplay = () => {
      state.showFilterConditions = !state.showFilterConditions
    }

    /**
     * 计算是否有详细个人信息
     */
    const hasDetailInfo = computed(() => {
      return !!(
        state.userInfo.marriageTxt ||
        state.userInfo.politicalStatusName ||
        state.userInfo.nationTxt ||
        state.userInfo.titleName
      )
    })

    /**
     * 计算是否有教育相关信息
     */
    const hasEducationInfo = computed(() => {
      return !!(
        state.userInfo.householdRegisterText ||
        state.userInfo.nativePlaceAreaTxt ||
        state.userInfo.educationName ||
        state.userInfo.schoolName
      )
    })

    /**
     * 计算是否有筛选条件
     * 功能：基于当前筛选条件的数量判断是否显示筛选条件区域
     */
    const hasFilterConditions = computed(() => {
      return state.activeFilterConditions.length > 0
    })

    /**
     * 权限检查函数
     * 功能：检查当前用户是否具有指定权限
     * @param permission - 权限标识符
     * @returns 是否具有该权限
     */
    /**
     * 权限检查函数（占位实现）
     * 功能：检查当前用户是否具有指定权限
     * @param _permission - 权限标识符（未使用，预留接口）
     * @returns 是否具有该权限（当前始终返回true）
     */
    const hasPermission = (_permission: string) => {
      // 简化权限检查，实际项目中应该根据具体的权限系统实现
      return true
    }

    // ===== 脱敏处理函数 =====

    /**
     * 脱敏手机号
     * 功能：将手机号中间4位替换为星号
     * @param mobile - 完整手机号
     * @returns 脱敏后的手机号
     */
    const maskMobile = (mobile: string) => {
      if (!mobile || mobile.length < 11) return mobile
      return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }

    /**
     * 脱敏邮箱
     * 功能：将邮箱用户名部分用星号替换
     * @param email - 完整邮箱
     * @returns 脱敏后的邮箱
     */
    const maskEmail = (email: string) => {
      if (!email || !email.includes('@')) return email
      const [username, domain] = email.split('@')
      if (username.length <= 2) return email
      const maskedUsername = `${username.charAt(0)}***${username.slice(-1)}`
      return `${maskedUsername}@${domain}`
    }

    /**
     * 计算显示的手机号
     */
    const displayMobile = computed(() => {
      if (!state.userInfo.fullMobile) return ''
      return state.showFullMobile
        ? state.userInfo.fullMobile
        : maskMobile(state.userInfo.fullMobile)
    })

    /**
     * 计算显示的邮箱
     */
    const displayEmail = computed(() => {
      if (!state.userInfo.email) return ''
      return state.showFullEmail ? state.userInfo.email : maskEmail(state.userInfo.email)
    })

    /**
     * 切换手机号显示状态
     */
    const toggleMobileDisplay = () => {
      state.showFullMobile = !state.showFullMobile
    }

    /**
     * 切换邮箱显示状态
     */
    const toggleEmailDisplay = () => {
      state.showFullEmail = !state.showFullEmail
    }

    // ===== 操作函数区域 =====

    /**
     * 下载简历
     * 功能：触发简历下载，跳转到下载URL
     */
    const handleDownload = () => {
      if (!state.userInfo.resumeId) {
        return
      }
      window.location.href = `/resume/download?resumeId=${state.userInfo.resumeId}`
    }

    /**
     * 打印简历
     * 功能：触发浏览器打印功能
     */
    const handlePrint = () => {
      // 这里可以实现打印功能
      window.print()
    }

    /**
     * 打开标签管理弹窗
     * 功能：为当前人才打开标签设置弹窗
     */
    const handleTag = () => {
      if (!state.userInfo.resumeId) {
        return
      }
      tagDialogRef.value?.open()
    }

    /**
     * 查看人才操作日志
     * 功能：跳转到人才操作日志页面
     */
    const handleViewLog = () => {
      if (!state.memberId) {
        return
      }
      router.push({
        path: '/member/logList',
        query: { id: state.memberId, isShow: 1 }
      })
    }

    /**
     * 切换人才账号状态
     * 功能：在启用和禁用状态之间切换
     * 交互：显示确认对话框，用户确认后执行状态变更
     */
    const handleChangeStatus = () => {
      const txt = state.userInfo.status === '1' ? '你确定禁用该人才' : '你确定启用该人才'
      ElMessageBox.confirm(txt, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            if (state.userInfo.status === '1') {
              await lockAccount(state.memberId)
            } else {
              await unlockAccount(state.memberId)
            }

            await loadPersonDetail()
            emit('refresh')
          } catch (error) {}
        })
        .catch(() => {})
    }

    /**
     * 查看会员信息
     * 功能：跳转到会员详情页面
     */
    const handleViewVipInfo = () => {
      if (!state.userInfo.resumeId) {
        return
      }
      router.push({
        path: '/member/vipInfo',
        query: { id: state.userInfo.resumeId, isShow: 1 }
      })
    }

    /**
     * 关闭弹窗
     * 功能：关闭人才详情弹窗，不重新加载数据
     */
    const handleClose = () => {
      state.visible = false
      // 不重新加载数据，只是关闭弹窗
    }

    // ===== 求职设置相关操作 =====

    /**
     * 修改简历显示状态
     * 功能：切换简历的隐藏/显示状态
     */
    const handleChangeResumeShowStatus = async () => {
      try {
        await changeResumeShowStatus(state.memberId)

        return Promise.resolve(true)
      } catch (error) {
        return Promise.reject(false)
      }
    }

    /**
     * 修改匿名投递状态
     * 功能：切换匿名投递的开启/关闭状态
     */
    const handleChangeResumeAnonymousStatus = async () => {
      try {
        await changeResumeAnonymousStatus(state.memberId)

        return Promise.resolve(true)
      } catch (error) {
        return Promise.reject(false)
      }
    }

    /**
     * 修改代投状态
     * 功能：切换简历代投的开启/关闭状态
     */
    const handleChangeResumeProxyDeliverStatus = async () => {
      try {
        await changeResumeProxyDeliverStatus(state.memberId)

        return Promise.resolve(true)
      } catch (error) {
        return Promise.reject(false)
      }
    }

    // ===== 快捷操作函数 =====

    /**
     * 查看站内应聘记录
     * 功能：跳转到站内应聘详情页面
     */
    const handleViewOnSiteApply = () => {
      router.push(`/person/jobApplyDetail/${state.memberId}?tag=1`)
    }

    /**
     * 查看站外应聘记录
     * 功能：跳转到站外应聘详情页面
     */
    const handleViewOffSiteApply = () => {
      router.push(`/person/jobApplyDetail/${state.memberId}?tag=2`)
    }

    /**
     * 查看面试记录
     * 功能：跳转到面试记录详情页面
     */
    const handleViewInterview = () => {
      router.push(`/person/jobApplyDetail/${state.memberId}?tag=3`)
    }

    /**
     * 查看求职日程
     * 功能：跳转到求职日程页面（待实现）
     */
    const handleViewSchedule = () => {
      ElMessage.info('求职日程功能开发中')
    }

    // ===== 附件操作函数 =====

    /**
     * 下载附件简历
     * 功能：在新窗口中打开附件简历的下载链接
     * @param attachment - 附件信息对象
     */
    const handleDownloadAttachment = (attachment: any) => {
      if (attachment.url) {
        window.open(attachment.url, '_blank')
      }
    }

    /**
     * 预览附件简历
     * 功能：打开附件简历预览弹窗
     * @param attachment - 附件信息对象
     */
    const handlePreviewAttachment = (attachment: any) => {
      state.previewOption.visible = true
      state.previewOption.resumeId = attachment.resumeId
      state.previewOption.token = attachment.token
    }

    // ===== 标签操作函数 =====

    /**
     * 标签设置确认回调
     * 功能：标签设置成功后重新加载人才信息
     */
    const handleTagConfirm = () => {
      loadPersonDetail()
    }

    /**
     * 重置标签列表
     * 功能：向父组件发送刷新事件，更新标签列表
     */
    const handleResetTagList = () => {
      emit('refresh')
    }

    /**
     * 获取求职状态对应的样式类
     */
    const getStatusClass = (status: string) => {
      const statusMap = {
        在职: 'status-working',
        求职中: 'status-seeking',
        离职: 'status-available',
        暂不求职: 'status-unavailable'
      }
      return statusMap[status] || 'status-default'
    }

    /**
     * 获取技能等级对应的样式类
     */
    const getSkillLevelClass = (level: string) => {
      const levelMap = {
        精通: 'level-expert',
        熟练: 'level-proficient',
        一般: 'level-intermediate',
        了解: 'level-basic'
      }
      return levelMap[level] || 'level-basic'
    }

    /**
     * 检查是否有任何简历内容
     */
    const hasAnyResumeContent = computed(() => {
      return (
        state.userInfo.workStatusName ||
        state.userInfo.advantage ||
        state.intentionList.length > 0 ||
        state.educationList.length > 0 ||
        state.researchDirection ||
        state.workList.length > 0 ||
        state.projectList.length > 0 ||
        state.pageList.length > 0 ||
        state.patentList.length > 0 ||
        state.bookList.length > 0 ||
        state.rewardList.length > 0 ||
        state.otherRewardList.length > 0 ||
        state.skillList.length > 0 ||
        state.certificateList.length > 0 ||
        state.otherSkillList.length > 0
      )
    })

    /**
     * 滚动到指定锚点
     */
    const scrollToAnchor = (anchorId: string) => {
      state.activeAnchor = anchorId
      const element = document.getElementById(anchorId)
      if (element) {
        // 获取对话框内容区域
        const dialogBody = document.querySelector('.el-dialog__body')
        if (dialogBody) {
          // 计算元素相对于对话框的位置
          const elementRect = element.getBoundingClientRect()
          const dialogRect = dialogBody.getBoundingClientRect()
          const scrollTop = elementRect.top - dialogRect.top + dialogBody.scrollTop - 100 // 100px 偏移量

          dialogBody.scrollTo({
            top: Math.max(0, scrollTop),
            behavior: 'smooth'
          })
        }
      }
    }

    /**
     * 滚动监听器，用于高亮当前显示的节点
     */
    const handleScroll = () => {
      const anchors = [
        'job-info',
        'advantage',
        'intention',
        'education',
        'research',
        'work',
        'project',
        'academic',
        'awards',
        'skills'
      ]
      const dialogBody = document.querySelector('.el-dialog__body')

      if (!dialogBody) return

      const { scrollTop } = dialogBody
      let currentAnchor = ''

      // 找到当前在视口中的节点
      for (let i = 0; i < anchors.length; i++) {
        const anchorId = anchors[i]
        const element = document.getElementById(anchorId)
        if (element) {
          const rect = element.getBoundingClientRect()
          const dialogRect = dialogBody.getBoundingClientRect()
          const elementTop = rect.top - dialogRect.top + scrollTop

          // 如果元素在视口上方200px内，则认为是当前节点
          if (elementTop <= scrollTop + 200) {
            currentAnchor = anchorId
          } else {
            break
          }
        }
      }

      // 如果没有找到合适的节点，使用第一个可见的节点
      if (!currentAnchor && anchors.length > 0) {
        currentAnchor = anchors[0]
      }

      if (currentAnchor && currentAnchor !== state.activeAnchor) {
        state.activeAnchor = currentAnchor
      }
    }

    /**
     * 组件挂载后添加滚动监听
     */
    onMounted(() => {
      // 由于对话框是动态显示的，我们在 open 方法中添加监听器
    })

    /**
     * 组件卸载时移除滚动监听
     */
    onUnmounted(() => {
      const dialogBody = document.querySelector('.el-dialog__body')
      if (dialogBody) {
        dialogBody.removeEventListener('scroll', handleScroll)
      }
    })

    /**
     * 添加滚动监听器
     */
    const addScrollListener = () => {
      nextTick(() => {
        const dialogBody = document.querySelector('.el-dialog__body')
        if (dialogBody) {
          // 移除旧的监听器（防止重复添加）
          dialogBody.removeEventListener('scroll', handleScroll)
          // 添加新的监听器
          dialogBody.addEventListener('scroll', handleScroll, { passive: true })
          // 初始化高亮状态
          handleScroll()
        }
      })
    }

    // ===== 返回暴露的方法和属性 =====
    return {
      ...toRefs(state),
      tagDialogRef,
      open,
      loadPersonDetail,
      goToPrevious,
      goToNext,
      handleClose,
      processFilterConditions,
      toggleFilterDisplay,
      hasDetailInfo,
      hasEducationInfo,
      hasFilterConditions,
      hasPermission,
      displayMobile,
      displayEmail,
      toggleMobileDisplay,
      toggleEmailDisplay,
      getStatusClass,
      getSkillLevelClass,
      hasAnyResumeContent,
      scrollToAnchor,
      addScrollListener,
      handleDownload,
      handlePrint,
      handleTag,
      handleViewLog,
      handleChangeStatus,
      handleViewVipInfo,
      handleChangeResumeShowStatus,
      handleChangeResumeAnonymousStatus,
      handleChangeResumeProxyDeliverStatus,
      handleViewOnSiteApply,
      handleViewOffSiteApply,
      handleViewInterview,
      handleViewSchedule,
      handleDownloadAttachment,
      handlePreviewAttachment,
      handleTagConfirm,
      handleResetTagList
    }
  }
})
</script>

<style lang="scss" scoped>
.person-detail-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;

    .el-dialog__header {
      padding: 20px 24px 16px;
    }

    .el-dialog__body {
      padding: 0;
      max-height: 80vh;
      overflow-y: auto;
    }
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;

    .page-info {
      font-size: 14px;
      color: #909399;
      margin-left: 8px;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 16px;

    .action-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      align-items: center;

      .el-button {
        margin: 0;
      }

      .setting-inline {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-left: 16px;
        padding-left: 16px;
        border-left: 1px solid #dcdfe6;

        .setting-label {
          font-size: 12px;
          color: #606266;
          white-space: nowrap;
        }

        .el-switch {
          margin: 0;
        }
      }
    }

    .pagination-buttons {
      display: flex;
      gap: 8px;

      .el-button {
        margin: 0;
      }
    }
  }
}

.dialog-content {
  .filter-conditions {
    padding: 16px 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #ebeef5;

    .filter-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .filter-title {
        font-size: 14px;
        font-weight: 500;
        color: #606266;
      }
    }

    .filter-content {
      .filter-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .filter-tag {
          margin: 0;
          background: #e1f3d8;
          border-color: #67c23a;
          color: #67c23a;
          font-size: 12px;
        }
      }
    }
  }

  .person-header {
    padding: 20px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;

    .header-info {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .main-info {
        flex: 1;
      }

      .person-name {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
      }

      .basic-info {
        margin-bottom: 8px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;

        .info-item {
          font-size: 14px;
          opacity: 0.9;

          &.contact-item {
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);

            .contact-icon {
              font-size: 12px;
            }

            .toggle-icon {
              font-size: 10px;
              opacity: 0.7;
            }
          }
        }
      }

      .detail-info {
        margin-bottom: 8px;
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .info-item {
          font-size: 13px;
          opacity: 0.85;
          white-space: nowrap;
        }
      }

      .update-info {
        font-size: 13px;
        opacity: 0.8;

        span {
          margin-right: 16px;
        }
      }
    }

    // 简历完善度圆环样式
    .resume-completion {
      flex-shrink: 0;

      .completion-circle {
        position: relative;
        width: 60px;
        height: 60px;

        svg {
          transform: rotate(-90deg);

          circle {
            transition: stroke-dashoffset 0.3s ease;
          }
        }

        .completion-text {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;

          .percentage {
            display: block;
            font-size: 14px;
            font-weight: 600;
            line-height: 1;
          }

          .label {
            display: block;
            font-size: 10px;
            opacity: 0.8;
            margin-top: 2px;
            line-height: 1;
          }
        }
      }
    }
  }

  .detail-content {
    display: flex;
    gap: 24px;
    padding: 24px;

    .content-main {
      flex: 1;
      min-width: 0;
    }

    .content-sidebar {
      width: 220px;
      flex-shrink: 0;

      // 如果没有订阅信息，隐藏侧边栏
      &:empty {
        display: none;
      }
    }
  }

  // Tab页面样式
  .detail-tabs {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;

    :deep(.el-tabs__header) {
      margin: 0;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;
    }

    :deep(.el-tabs__nav-wrap) {
      padding: 0;
    }

    :deep(.el-tabs__nav) {
      border: none;
    }

    :deep(.el-tabs__item) {
      padding: 0 20px;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      color: #606266;
      border-right: 1px solid #dcdfe6;
      border-bottom: none;
      background-color: #f5f7fa;

      &.is-active {
        color: #409eff;
        background-color: #fff;
        border-bottom: 1px solid #fff;
        position: relative;
        z-index: 1;
      }

      &:last-child {
        border-right: none;
      }
    }

    :deep(.el-tabs__content) {
      padding: 20px;
      background-color: #fff;
    }

    :deep(.el-tab-pane) {
      min-height: 400px;
    }
  }

  // 侧边锚点导航栏样式 - 简洁实用
  .sidebar-navigation {
    position: fixed;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 999;
    background: rgba(255, 255, 255, 0.92);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    max-height: 65vh;
    overflow-y: auto;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.96);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .nav-title {
      font-size: 10px;
      font-weight: 500;
      color: #999;
      margin-bottom: 6px;
      text-align: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      padding-bottom: 4px;
    }

    .nav-items {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .nav-item {
        display: flex;
        align-items: center;
        padding: 4px 6px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.15s ease;

        .nav-dot {
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background: #ddd;
          margin-right: 6px;
          flex-shrink: 0;
          transition: all 0.15s ease;
        }

        .nav-text {
          font-size: 10px;
          color: #888;
          line-height: 1.2;
          white-space: nowrap;
        }

        &:hover {
          background: rgba(64, 158, 255, 0.08);

          .nav-dot {
            background: #409eff;
          }

          .nav-text {
            color: #409eff;
          }
        }

        &.active {
          background: rgba(64, 158, 255, 0.12);

          .nav-dot {
            background: #409eff;
            width: 6px;
            height: 6px;
          }

          .nav-text {
            color: #409eff;
            font-weight: 500;
          }
        }
      }
    }

    // 隐藏滚动条
    &::-webkit-scrollbar {
      width: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 1px;
    }

    // 在小屏幕上隐藏
    @media (max-width: 1200px) {
      display: none;
    }
  }

  // 简历内容区域样式
  .modern-resume {
    position: relative;
  }

  // 现代化简历样式
  .modern-resume {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 0;

    .resume-card {
      background: #ffffff;
      border-radius: 12px;
      border: 1px solid #e8eaed;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      overflow: hidden;

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 24px 16px;
        border-bottom: 1px solid #f0f2f5;
        background: linear-gradient(135deg, #f8f9fb 0%, #ffffff 100%);

        .card-title {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 16px;
          font-weight: 600;
          color: #1f2329;

          .title-icon {
            font-size: 18px;
            color: #409eff;
          }
        }

        .completion-badge {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 12px;
          background: linear-gradient(135deg, #f7ba2a 0%, #ea9518 100%);
          color: white;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 500;

          .el-icon {
            font-size: 12px;
          }
        }

        .count-badge {
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 24px;
          height: 24px;
          padding: 0 8px;
          background: #409eff;
          color: white;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }
      }

      .card-content {
        padding: 20px 24px 24px;
      }
    }

    // 求职信息卡片
    .job-info-card {
      .info-row {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          flex: 1;
          min-width: 200px;

          .item-icon {
            font-size: 16px;
            color: #409eff;
            margin-top: 2px;
            flex-shrink: 0;

            &.activity-icon {
              color: #67c23a;
            }
          }

          .item-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
            flex: 1;

            .item-label {
              font-size: 12px;
              color: #86909c;
              font-weight: 500;
            }

            .item-value {
              font-size: 14px;
              color: #1f2329;
              font-weight: 500;

              &.status-tag {
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 12px;
                display: inline-block;
                width: fit-content;

                &.status-working {
                  background: #e7f8ff;
                  color: #0066cc;
                }

                &.status-seeking {
                  background: #e8f8f0;
                  color: #00a36c;
                }

                &.status-available {
                  background: #fff3e0;
                  color: #ff8f00;
                }

                &.status-unavailable {
                  background: #f5f5f6;
                  color: #86909c;
                }

                &.status-default {
                  background: #f0f2f5;
                  color: #4e5969;
                }
              }

              &.education-tag {
                padding: 2px 8px;
                background: #f2f3ff;
                color: #6366f1;
                border-radius: 12px;
                font-size: 12px;
                display: inline-block;
                width: fit-content;
              }

              &.time-value {
                color: #67c23a;
                font-size: 13px;
              }
            }
          }
        }
      }
    }

    // 个人优势卡片
    .advantage-card {
      .advantage-text {
        font-size: 14px;
        line-height: 1.6;
        color: #4e5969;
        background: #f8f9fb;
        padding: 16px;
        border-radius: 8px;
        border-left: 4px solid #f7ba2a;
      }
    }

    // 求职意向卡片
    .intention-card {
      .intention-list {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .intention-item {
          background: #f8f9fb;
          border-radius: 8px;
          padding: 16px;
          border-left: 4px solid #409eff;

          .intention-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;

            .intention-index {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;
              background: #409eff;
              color: white;
              border-radius: 50%;
              font-size: 12px;
              font-weight: 600;
            }

            .intention-title {
              font-size: 16px;
              font-weight: 600;
              color: #1f2329;
            }
          }

          .intention-details {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;

            .detail-item {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 13px;
              color: #4e5969;

              .el-icon {
                font-size: 14px;
                color: #86909c;
              }
            }
          }
        }
      }
    }

    // 教育经历和工作经历时间线
    .education-timeline,
    .work-timeline {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 12px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, #409eff, #a0cfff);
      }

      .timeline-item {
        position: relative;
        padding-left: 40px;
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .timeline-dot {
          position: absolute;
          left: 6px;
          top: 8px;
          width: 12px;
          height: 12px;
          background: #409eff;
          border: 3px solid #ffffff;
          border-radius: 50%;
          box-shadow: 0 0 0 2px #e3f2fd;

          &.work-dot {
            background: #67c23a;
            box-shadow: 0 0 0 2px #f0f9ff;
          }
        }

        .timeline-content {
          background: #ffffff;
          border: 1px solid #e8eaed;
          border-radius: 8px;
          padding: 16px;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

          .edu-header,
          .work-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;

            .edu-school,
            .work-company {
              font-size: 16px;
              font-weight: 600;
              color: #1f2329;
              display: flex;
              align-items: center;
              gap: 8px;
              flex-wrap: wrap;
            }

            .edu-time,
            .work-time {
              font-size: 12px;
              color: #86909c;
              font-weight: 500;
              white-space: nowrap;
            }
          }

          .edu-details,
          .work-details {
            .edu-major,
            .work-position {
              font-size: 14px;
              font-weight: 500;
              color: #4e5969;
              margin-bottom: 8px;
            }

            .edu-extra,
            .work-department {
              font-size: 13px;
              color: #86909c;
              margin-bottom: 8px;

              span {
                margin-right: 16px;
              }
            }

            .edu-tags {
              display: flex;
              gap: 8px;
              margin-top: 8px;
            }

            .work-description {
              font-size: 13px;
              line-height: 1.5;
              color: #4e5969;
              background: #f8f9fb;
              padding: 12px;
              border-radius: 6px;
              margin-top: 8px;
            }
          }
        }
      }
    }

    // 研究方向卡片
    .research-card {
      .research-text {
        font-size: 14px;
        line-height: 1.6;
        color: #4e5969;
        background: #f8f9fb;
        padding: 16px;
        border-radius: 8px;
        border-left: 4px solid #67c23a;
      }
    }

    // 项目经历卡片样式
    .project-card {
      .project-timeline {
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 12px;
          top: 0;
          bottom: 0;
          width: 2px;
          background: linear-gradient(to bottom, #f39c12, #e67e22);
        }

        .timeline-item {
          position: relative;
          padding-left: 40px;
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .timeline-dot {
            position: absolute;
            left: 6px;
            top: 8px;
            width: 12px;
            height: 12px;
            background: #f39c12;
            border: 3px solid #ffffff;
            border-radius: 50%;
            box-shadow: 0 0 0 2px #fef9e7;

            &.project-dot {
              background: #f39c12;
              box-shadow: 0 0 0 2px #fef9e7;
            }
          }

          .timeline-content {
            background: #ffffff;
            border: 1px solid #e8eaed;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
          }

          .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;

            .project-name {
              font-size: 16px;
              font-weight: 600;
              color: #1f2329;
              display: flex;
              align-items: center;
              gap: 8px;
              flex-wrap: wrap;
            }

            .project-time {
              font-size: 12px;
              color: #86909c;
              font-weight: 500;
              white-space: nowrap;
            }
          }

          .project-details {
            .project-role {
              font-size: 14px;
              font-weight: 500;
              color: #4e5969;
              margin-bottom: 6px;
            }

            .project-company,
            .project-category {
              font-size: 13px;
              color: #86909c;
              margin-bottom: 6px;
            }

            .project-description {
              font-size: 13px;
              line-height: 1.5;
              color: #4e5969;
              background: #f8f9fb;
              padding: 12px;
              border-radius: 6px;
              margin-top: 8px;
              border-left: 4px solid #f39c12;
            }
          }
        }
      }
    }

    // 学术成果卡片样式
    .academic-card {
      .academic-section {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .sub-section-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 1px solid #e8eaed;

          .sub-icon {
            font-size: 16px;
            color: #409eff;
          }

          span {
            font-size: 15px;
            font-weight: 600;
            color: #1f2329;
          }
        }

        .academic-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 16px;

          .academic-item {
            background: #f8f9fb;
            border-radius: 8px;
            padding: 16px;
            border-left: 4px solid #409eff;

            &.paper-item {
              border-left-color: #409eff;
            }

            &.patent-item {
              border-left-color: #67c23a;
            }

            &.book-item {
              border-left-color: #e6a23c;
            }

            .academic-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 12px;

              .academic-title {
                font-size: 15px;
                font-weight: 600;
                color: #1f2329;
                line-height: 1.4;
                flex: 1;
                margin-right: 12px;
              }

              .academic-date {
                font-size: 12px;
                color: #86909c;
                white-space: nowrap;
                background: #ffffff;
                padding: 2px 8px;
                border-radius: 4px;
              }
            }

            .academic-details {
              .detail-row {
                display: flex;
                margin-bottom: 6px;

                .detail-label {
                  min-width: 80px;
                  font-size: 12px;
                  color: #86909c;
                  font-weight: 500;
                }

                .detail-value {
                  font-size: 13px;
                  color: #4e5969;
                  flex: 1;

                  &.impact-factor {
                    font-weight: 600;
                    color: #f56c6c;
                  }
                }
              }
            }

            .academic-description {
              margin-top: 12px;
              padding: 12px;
              background: #ffffff;
              border-radius: 6px;
              font-size: 13px;
              line-height: 1.5;
              color: #4e5969;
            }
          }
        }
      }
    }

    // 荣誉奖励卡片样式
    .awards-card {
      .awards-section {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .sub-section-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 1px solid #e8eaed;

          .sub-icon {
            font-size: 16px;
            color: #f7ba2a;
          }

          span {
            font-size: 15px;
            font-weight: 600;
            color: #1f2329;
          }
        }

        .awards-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 16px;

          .award-item {
            background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #f7ba2a;
            position: relative;
            transition: all 0.3s ease;

            &::before {
              content: '🏆';
              position: absolute;
              top: -8px;
              left: -8px;
              width: 24px;
              height: 24px;
              background: #f7ba2a;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
            }

            &.academic-award {
              border-color: #67c23a;
              background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);

              &::before {
                background: #67c23a;
                content: '🎓';
              }
            }

            .award-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 12px;

              .award-name {
                font-size: 15px;
                font-weight: 600;
                color: #1f2329;
                line-height: 1.4;
                flex: 1;
                margin-right: 12px;
              }

              .award-date {
                font-size: 12px;
                color: #86909c;
                white-space: nowrap;
                background: rgba(255, 255, 255, 0.8);
                padding: 2px 8px;
                border-radius: 4px;
              }
            }

            .award-details {
              .detail-row {
                display: flex;
                margin-bottom: 6px;

                .detail-label {
                  min-width: 70px;
                  font-size: 12px;
                  color: #86909c;
                  font-weight: 500;
                }

                .detail-value {
                  font-size: 13px;
                  color: #4e5969;
                  flex: 1;

                  &.level-badge {
                    padding: 2px 8px;
                    background: #e8f4fd;
                    color: #409eff;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;
                    display: inline-block;
                    width: fit-content;
                  }
                }
              }
            }
          }
        }
      }
    }

    // 技能特长卡片样式
    .skills-card {
      .skills-section {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .sub-section-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 1px solid #e8eaed;

          .sub-icon {
            font-size: 16px;
            color: #409eff;
          }

          span {
            font-size: 15px;
            font-weight: 600;
            color: #1f2329;
          }
        }

        .skills-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;

          .skill-item {
            background: #f8f9fb;
            border-radius: 8px;
            padding: 16px;
            border-left: 4px solid #409eff;

            .skill-name {
              font-size: 14px;
              font-weight: 600;
              color: #1f2329;
              margin-bottom: 8px;
            }

            .skill-level {
              display: flex;
              align-items: center;
              gap: 8px;

              .level-text {
                font-size: 12px;
                color: #86909c;
                font-weight: 500;
              }

              .level-indicator {
                flex: 1;
                height: 4px;
                background: #e8eaed;
                border-radius: 2px;
                overflow: hidden;

                .level-bar {
                  height: 100%;
                  border-radius: 2px;
                  transition: width 0.3s ease;

                  &.level-expert {
                    width: 100%;
                    background: linear-gradient(to right, #67c23a, #95d475);
                  }

                  &.level-proficient {
                    width: 75%;
                    background: linear-gradient(to right, #409eff, #79bbff);
                  }

                  &.level-intermediate {
                    width: 50%;
                    background: linear-gradient(to right, #e6a23c, #f2c94c);
                  }

                  &.level-basic {
                    width: 25%;
                    background: linear-gradient(to right, #f56c6c, #f78989);
                  }
                }
              }
            }
          }
        }

        .certificates-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 16px;

          .certificate-item {
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #67c23a;
            position: relative;
            transition: all 0.3s ease;

            &::before {
              content: '📜';
              position: absolute;
              top: -8px;
              right: -8px;
              width: 24px;
              height: 24px;
              background: #67c23a;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
            }

            .certificate-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 12px;

              .certificate-name {
                font-size: 15px;
                font-weight: 600;
                color: #1f2329;
                line-height: 1.4;
                flex: 1;
                margin-right: 12px;
              }

              .certificate-date {
                font-size: 12px;
                color: #86909c;
                white-space: nowrap;
                background: rgba(255, 255, 255, 0.8);
                padding: 2px 8px;
                border-radius: 4px;
              }
            }

            .certificate-details {
              display: flex;
              align-items: center;
              gap: 8px;

              .detail-label {
                font-size: 12px;
                color: #86909c;
                font-weight: 500;
              }

              .detail-value {
                font-size: 13px;
                color: #4e5969;

                &.score-badge {
                  padding: 2px 8px;
                  background: #e8f5e8;
                  color: #67c23a;
                  border-radius: 12px;
                  font-size: 12px;
                  font-weight: 600;
                }
              }
            }
          }
        }

        .other-skills-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 16px;

          .other-skill-item {
            background: #f8f9fb;
            border-radius: 8px;
            padding: 16px;
            border-left: 4px solid #86909c;

            .skill-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              .skill-name {
                font-size: 15px;
                font-weight: 600;
                color: #1f2329;
              }

              .skill-level {
                font-size: 12px;
                color: #86909c;
                background: #ffffff;
                padding: 2px 8px;
                border-radius: 4px;
                font-weight: 500;
              }
            }

            .skill-description {
              font-size: 13px;
              line-height: 1.5;
              color: #4e5969;
              background: #ffffff;
              padding: 12px;
              border-radius: 6px;
            }
          }
        }
      }
    }
  }

  .section-title {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    border-left: 4px solid #409eff;
    padding-left: 12px;
  }

  .sub-title {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 500;
    color: #606266;
  }

  .info-section {
    margin-bottom: 24px;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px 24px;
    margin-bottom: 16px;
  }

  .info-item {
    display: flex;
    align-items: center;

    label {
      min-width: 100px;
      font-weight: 500;
      color: #606266;
      margin-right: 8px;
      flex-shrink: 0;
    }

    span {
      color: #303133;
      flex: 1;
      word-break: break-all;
    }
  }

  .intention-item,
  .education-item,
  .work-item {
    padding: 20px;
    margin-bottom: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #409eff;

    .info-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 12px 20px;
    }
  }

  .work-description,
  .advantage-content,
  .research-content {
    margin-top: 8px;
    padding: 12px;
    background: white;
    border-radius: 4px;
    line-height: 1.6;
    white-space: pre-wrap;
  }

  .advantage-content,
  .research-content {
    background: #f8f9fa;
    border-left: 4px solid #67c23a;
  }

  .skill-group,
  .academic-group,
  .reward-group {
    margin-bottom: 24px;

    .skill-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .skill-tag {
        margin: 0;
      }
    }

    .skill-item,
    .certificate-item,
    .other-skill-item,
    .academic-item,
    .reward-item {
      padding: 16px;
      margin-bottom: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      border-left: 3px solid #67c23a;

      .info-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 8px 16px;
      }
    }
  }

  .academic-description,
  .skill-description {
    margin-top: 8px;
    padding: 12px;
    background: white;
    border-radius: 4px;
    line-height: 1.6;
    white-space: pre-wrap;
  }

  .academic-description {
    border-left: 4px solid #e6a23c;
  }

  .skill-description {
    border-left: 4px solid #67c23a;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dialog-content {
    .info-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .intention-item,
    .education-item,
    .work-item {
      .info-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      }
    }

    .academic-item,
    .reward-item {
      .info-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      }
    }
  }
}

@media (max-width: 768px) {
  .person-detail-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 0 auto;
    }
  }

  .dialog-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;

    .header-actions {
      align-self: flex-end;
    }
  }

  .dialog-content {
    .detail-content {
      padding: 16px;
    }

    .info-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .info-item {
      label {
        min-width: 80px;
      }
    }
  }
}

@media (max-width: 480px) {
  .dialog-content {
    .detail-content {
      padding: 12px;
      flex-direction: column;

      .content-sidebar {
        width: 100%;
        margin-top: 16px;
      }
    }

    .intention-item,
    .education-item,
    .work-item {
      padding: 12px;
    }
  }
}

// 侧边栏样式
.content-sidebar {
  .setting-panel,
  .operation-panel,
  .attachment-panel,
  .subscribe-panel {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid #ebeef5;

    .setting-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #409eff;
    }
  }

  .setting-panel {
    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .setting-label {
        font-size: 14px;
        color: #606266;
      }
    }

    .setting-info {
      font-size: 13px;
      color: #909399;
      margin-top: 8px;
    }
  }

  .operation-panel {
    .operation-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }

  .attachment-panel {
    .attachment-list {
      .attachment-item {
        padding: 8px;
        background: white;
        border-radius: 4px;
        margin-bottom: 8px;
        border: 1px solid #e4e7ed;

        .attachment-name {
          font-size: 14px;
          color: #303133;
          margin-bottom: 4px;
          word-break: break-all;
        }

        .attachment-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .subscribe-panel {
    .subscribe-content {
      .subscribe-item {
        display: flex;
        margin-bottom: 8px;

        .subscribe-label {
          min-width: 80px;
          font-size: 14px;
          color: #606266;
          font-weight: 500;
        }

        .subscribe-value {
          flex: 1;
          font-size: 14px;
          color: #303133;
          word-break: break-all;
        }
      }
    }
  }
}
</style>
