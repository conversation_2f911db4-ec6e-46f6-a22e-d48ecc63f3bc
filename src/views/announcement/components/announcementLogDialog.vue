<template>
  <el-dialog title="职位操作日志" v-model="visible" top="13vh" width="980px" @close="handleClose">
    <el-form :model="formData" ref="form" size="small" :inline="true">
      <el-form-item prop="handlerName">
        <el-input
          v-model="formData.handlerName"
          placeholder="操作用户"
          filterable
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="addTimeStart">
        <DatePickerRange
          v-model:start="formData.addTimeStart"
          v-model:end="formData.addTimeEnd"
          size="small"
          placeholder="操作时间"
        />
      </el-form-item>
      <el-form-item prop="handleType">
        <AnnouncementTypeList v-model="formData.handleType" />
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" @click="getList">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border size="small" v-loading="loading">
      <el-table-column
        prop="handlerName"
        align="center"
        header-align="center"
        label="操作用户"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="addTime"
        align="center"
        header-align="center"
        label="操作时间"
        min-width="130px"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="ip"
        align="center"
        header-align="center"
        label="操作IP"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="handleTypeTitle"
        align="center"
        header-align="center"
        label="操作类型"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="handleBefore"
        align="center"
        header-align="center"
        label="操作前"
        min-width="200px"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <p v-for="(v, k) in row.handleBefore" :key="k">{{ k }}:{{ v }}</p>
        </template>
      </el-table-column>
      <el-table-column
        prop="handleAfter"
        align="center"
        min-width="200px"
        header-align="center"
        label="操作后"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <p v-for="(v, k) in row.handleAfter" :key="k">{{ k }}:{{ v }}</p>
        </template></el-table-column
      >
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>
    <Pagination
      v-if="pagination.total > 0"
      :total="pagination.total"
      class="mt-15"
      @change="handlePaginationChange"
      align="right"
    />
  </el-dialog>
</template>

<script lang="ts">
import { toRefs, reactive, ref } from 'vue'

import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Pagination from '/@/components/base/paging.vue'

import { getAnnouncementHandleLog } from '/@/api/cooperaAnnouncement'
import AnnouncementTypeList from '/@/components/base/select/announcementTypeList.vue'

export default {
  name: 'announcementLogDialog',
  components: { DatePickerRange, Pagination, AnnouncementTypeList },
  props: {
    data: {}
  },
  emits: ['confirm'],
  setup() {
    const form = ref()
    const state = reactive({
      visible: false,
      loading: false,
      formData: {
        id: '',
        handlerName: '',
        addTimeStart: '',
        addTimeEnd: '',
        handleType: '',
        limit: 20,
        page: 1
      },
      pagination: {
        total: 0
      },
      list: []
    })

    const getList = () => {
      state.loading = true
      getAnnouncementHandleLog(state.formData).then((resp: any) => {
        state.loading = false
        state.list = resp.list.map((item: any) => {
          return {
            ...item,
            handleAfter: item.handleAfter[0],
            handleBefore: item.handleBefore[0]
          }
        })
        state.pagination.total = resp.page.count
      })
    }

    const open = (id: string) => {
      state.visible = true
      state.formData.id = id
      getList()
    }
    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.limit = data.limit
      getList()
    }

    const handleClose = () => {
      form.value?.resetFields()
    }

    return {
      form,
      handleClose,
      open,
      getList,
      handlePaginationChange,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss"></style>
