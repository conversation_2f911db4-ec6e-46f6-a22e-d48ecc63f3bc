<template>
  <div class="main">
    <el-card>
      <el-form label-width="90px" ref="announcementForm" :model="form">
        <el-row>
          <el-col :span="4">
            <el-form-item label="公告检索" prop="announcementTitleNum">
              <el-input
                placeholder="请填写公告标题或编号"
                v-model="form.announcementTitleNum"
                clearable
                @keyup.enter="getList"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="职位检索" prop="name">
              <el-input
                placeholder="请填写职位名称或编号"
                v-model="form.name"
                clearable
                @keyup.enter="getList"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="单位检索" prop="companyName">
              <el-input
                placeholder="请填写单位名称或编号"
                v-model="form.companyName"
                clearable
                @keyup.enter="getList"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="职位类型" prop="jobCategoryId">
              <JobCategory v-model="form.jobCategoryId" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="学历要求" prop="educationType">
              <Education v-model="form.educationType" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <div class="nowrap ml-15">
              <el-button type="primary" size="small" @click="getList">搜索</el-button>
              <el-button size="small" @click="reset">重置</el-button>
              <el-button size="small" @click="downloadExcel">下载</el-button>
              <el-button type="primary" link size="small" @click="showMore = !showMore"
                >查看更多</el-button
              >
            </div>
          </el-col>
        </el-row>
        <div v-show="showMore">
          <el-row>
            <el-col :span="4">
              <el-form-item label="工作城市" prop="city">
                <Region v-model="form.city" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="招聘状态" prop="status">
                <el-select v-model="form.status" placeholder="不限" filterable clearable>
                  <el-option
                    v-for="item in statusRecruitList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="需求专业" prop="majorId">
                <MajorCategory v-model="form.majorId" :multiple="false" :deep="2" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="初始发布时间" prop="firstReleaseTimeStart" label-width="100px">
                <DatePickerRange
                  v-model:start="form.firstReleaseTimeStart"
                  v-model:end="form.firstReleaseTimeEnd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="刷新时间" prop="realRefreshTimeStart">
                <DatePickerRange
                  v-model:start="form.realRefreshTimeStart"
                  v-model:end="form.realRefreshTimeEnd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="发布时间" prop="refreshTimeStart">
                <DatePickerRange
                  v-model:start="form.refreshTimeStart"
                  v-model:end="form.refreshTimeEnd"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="用人部门" prop="department">
                <el-input
                  placeholder="请填写用人部门"
                  v-model="form.department"
                  clearable
                  @keyup.enter="getList"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="工作性质" prop="natureType">
                <WorkNature v-model="form.natureType" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="海外经历" prop="abroadType">
                <AbroadExperience ispageSize v-model="form.abroadType" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="工作经验" prop="experienceType">
                <WorkExperience v-model="form.experienceType" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="职称要求" prop="titleType">
                <LevelTitle v-model="form.titleType" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="年龄要求" prop="ageType">
                <Age v-model="form.ageType" @keyup.enter="getList" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="政治面貌" prop="politicalType">
                <Political v-model="form.politicalType" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="审核状态" prop="auditStatus">
                <Audit v-model="form.auditStatus" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="创建人" prop="creator">
                <el-input
                  placeholder="请填写创建人账号"
                  v-model="form.creator"
                  clearable
                  @keyup.enter="getList"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="所属栏目" prop="homeColumnId">
                <Colunm v-model="form.homeColumnId" :columnList="columnList" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="是否小程序" prop="isMiniapp">
                <IsMiniapp v-model="form.isMiniapp" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="公告属性" prop="notOverseasAttribute">
                <el-select
                  v-model="form.notOverseasAttribute"
                  placeholder="不限"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in notOverseasAttributeList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="高才海外相关属性" prop="overseasAttribute">
                <el-select v-model="form.overseasAttribute" placeholder="不限" clearable filterable>
                  <el-option
                    v-for="item in overseasAttributeList"
                    :key="item.k"
                    :label="item.v"
                    :value="item.k"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      &nbsp;&nbsp;
      <el-switch
        v-model="isSimple"
        size="large"
        active-text="简版(只支持部分搜索条件和显示结果)"
        inactive-text="全功能"
      />
      <div class="jc-between amount">
        <div>
          共计:
          <span class="danger">{{ pagination.total }}</span>
          则公告；招聘职位：<span class="danger">{{ amount.allJob }}</span
          >；面试邀约:
          <span class="danger">{{ amount.interview }}</span>
          次；点击次数:
          <span class="danger">{{ amount.click }}</span
          >次； 平台投递:
          <span class="danger">{{ amount.platformNum }}</span>
          次； 邮箱投递:
          <span class="danger">{{ amount.emailNum }}</span>
          次； 网址投递:
          <span class="danger">{{ amount.linkNum }}</span>
          次；
        </div>
        <el-link :underline="false" type="primary" size="small" @click="handleOpenCustomColumn"
          >选择列</el-link
        >
      </div>
      <el-table
        :data="announcementList"
        border
        @selection-change="handleAnnounceChange"
        v-loading="loading"
        ref="announcementTable"
        @sort-change="sortAnnouncementData"
      >
        <el-table-column type="selection" width="40" />
        <template v-for="(item, index) in customAnnouncement">
          <el-table-column
            v-if="item.select && item.k === 1"
            :key="index"
            :label="item.v"
            prop="announcementUid"
            align="center"
          />

          <el-table-column
            v-if="item.select && item.k === 2"
            :key="index"
            :label="item.v"
            prop="title"
            align="center"
            width="300"
          >
            <template #default="{ row }">
              <router-link
                class="bg-primary td-none"
                :to="`/cms/announcementDetail/${row.aid}/${row.status}`"
                >{{ row.title }}</router-link
              >
              <p class="tip danger">
                {{ row.tip }}
              </p>
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 3"
            :key="index"
            :label="item.v"
            prop="fullName"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-button type="primary" link @click="toCompanyDetail(row.companyId)">
                {{ row.fullName }}
              </el-button>
            </template>
          </el-table-column>

          <el-table-column
            min-width="110px"
            v-if="item.select && item.k === 18"
            :key="index"
            :label="item.v"
            prop="sortHomeSort"
            align="center"
            sortable="custom"
          >
            <template #header>
              <el-tooltip
                class="box-item"
                effect="dark"
                content="此排序仅对单位主页中的公告排序生效"
                placement="top"
              >
                <i class="fa-question-circle-o fa"></i>
              </el-tooltip>
              排序
            </template>
            <template #default="{ row }">
              <div class="sort ai-center">
                {{ row.homeSort }}
                <img
                  @click="handleChangeSort(row)"
                  class="sort-edit"
                  src="/src/assets/icons/edit.svg"
                  alt=""
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 4"
            :key="index"
            :label="item.v"
            prop="sortRecruitCount"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              {{ row.amount }}
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 5"
            :key="index"
            :label="item.v"
            prop="sortRecruitJobCount"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              <router-link
                :to="{ path: '/job/query', query: { id: row.announcementUid } }"
                class="bg-primary td-none"
                v-if="row.jobNum !== '0'"
                >{{ row.jobNum }}</router-link
              >
              <div v-else class="bg-primary">{{ row.jobNum }}</div>
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 6"
            :key="index"
            :label="item.v"
            prop="sortOnlineJobCount"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              <router-link
                :to="{ path: '/job/query', query: { id: row.announcementUid } }"
                class="bg-primary td-none"
                v-if="row.onlineCount !== '0'"
                >{{ row.onlineCount }}</router-link
              >
              <div v-else class="bg-primary">{{ row.onlineCount }}</div>
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 7"
            :key="index"
            :label="item.v"
            prop="sortPlatformNum"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              <router-link
                :to="{ path: '/announcement/business', query: { id: row.aid } }"
                class="bg-primary td-none"
                v-if="row.platformNum !== '0'"
                >{{ row.platformNum }}</router-link
              >
              <div v-else class="bg-primary">
                {{ row.platformNum }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 8"
            :key="index"
            :label="item.v"
            prop="sortEmailNum"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              <router-link
                :to="{ path: '/announcement/business', query: { id: row.aid } }"
                class="bg-primary td-none"
                v-if="row.emailNum !== '0'"
                >{{ row.emailNum }}</router-link
              >
              <div v-else class="bg-primary">
                {{ row.emailNum }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 9"
            :key="index"
            :label="item.v"
            prop="sortJobInterviewNum"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              <router-link
                v-if="row.jobInterviewNum !== 0"
                class="bg-primary td-none"
                :to="{ path: '/announcement/business', query: { id: row.aid, active: '3' } }"
              >
                {{ row.jobInterviewNum }}
              </router-link>
              <div v-else class="bg-primary">
                {{ row.jobInterviewNum }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 10"
            :key="index"
            :label="item.v"
            prop="recruitStatusTxt"
            align="center"
          />

          <el-table-column
            v-if="item.select && item.k === 11"
            :key="index"
            :label="item.v"
            prop="sortRefreshTime"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              {{ row.refreshTime }}
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 12"
            :key="index"
            :label="item.v"
            prop="sortRealRefreshTime"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              {{ row.realRefreshTime }}
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 13"
            :key="index"
            :label="item.v"
            prop="opration"
            align="center"
            width="180px"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button type="primary" size="small">
                <router-link
                  class="td-none color-white"
                  :to="{ path: '/announcement/business', query: { id: row.aid } }"
                  >业务</router-link
                >
              </el-button>
              <el-popover placement="left" :width="20" trigger="click">
                <template #reference>
                  <el-button plain size="small">...</el-button>
                </template>
                <div class="column btns">
                  <el-button
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    :disabled="disabledOnlineButton(row)"
                    @click="refreshAnnounce(row.aid)"
                    >刷新</el-button
                  >

                  <el-button
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    @click="announceEdit(row.aid)"
                    :disabled="editButton(row)"
                    >编辑</el-button
                  >

                  <el-button
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    :disabled="!row.canDel"
                    @click="announceDel(row.aid)"
                    >删除</el-button
                  >

                  <el-button class="w100 mx-0 my-5" plain size="small" @click="handelLog(row.aid)"
                    >日志</el-button
                  >

                  <el-button
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    :disabled="isShowButton(row)"
                    @click="announcementOffline({ id: row.aid, actionType: '1' })"
                    >再发布</el-button
                  >

                  <el-button
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    :disabled="disabledOnlineButton(row)"
                    @click="announcementOffline({ id: row.aid, actionType: '2' })"
                    >下线</el-button
                  >
                  <!-- isShow 1:显示，2:隐藏 -->
                  <el-button
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    :disabled="row.status !== '3' && row.isShow === '1'"
                    @click="showAnnouncement({ id: row.aid, actionType: '1' })"
                    >显示</el-button
                  >
                  <el-button
                    class="w100 mx-0 my-5"
                    plain
                    size="small"
                    :disabled="row.status !== '3' && row.isShow === '2'"
                    @click="showAnnouncement({ id: row.aid, actionType: '2' })"
                    >隐藏</el-button
                  >
                  <el-popover placement="left" :width="20" trigger="click">
                    <template #reference>
                      <el-button
                        @click="getAttributeData(row.articleId)"
                        class="w100 mx-0 my-5"
                        plain
                        size="small"
                        >...</el-button
                      >
                    </template>
                    <div class="column btns">
                      <el-button
                        class="w100 mx-0 my-5 ml-0"
                        plain
                        size="small"
                        v-for="item in attributeData"
                        :key="item.type"
                        @click="refreshSortTime(item.type, item.articleId)"
                        >{{ item.typeTxt }}</el-button
                      >
                    </div>
                  </el-popover>
                </div>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 14"
            :key="index"
            :label="item.v"
            prop="sortClickCount"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              {{ row.click }}
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 15"
            :key="index"
            :label="item.v"
            prop="auditStatusTxt"
            align="center"
          />

          <el-table-column
            v-if="item.select && item.k === 16"
            :key="index"
            :label="item.v"
            prop="isArticleTxt"
            align="center"
          />

          <el-table-column
            v-if="item.select && item.k === 17"
            :key="index"
            :label="item.v"
            prop="creatorName"
            align="center"
          />

          <el-table-column
            v-if="item.select && item.k === 18"
            :key="index"
            :label="item.v"
            prop="sortFirstReleaseTime"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              {{ row.firstReleaseTime }}
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 20"
            :key="index"
            :label="item.v"
            prop="majorTxt"
            align="center"
          />

          <el-table-column
            v-if="item.select && item.k === 21"
            :key="index"
            :label="item.v"
            prop="isMiniappTxt"
            align="center"
          >
            <template #default="{ row }">
              <!-- <el-button type="primary">{{ row.isMiniappTxt }}</el-button> -->
              <isMiniappChange
                :value="row.isMiniapp"
                type="announcement"
                :id="row.aid"
              ></isMiniappChange>
            </template>
          </el-table-column>

          <el-table-column
            v-if="item.select && item.k === 19"
            :key="index"
            :label="item.v"
            prop="sortLinkNum"
            align="center"
            sortable="custom"
          >
            <template #default="{ row }">
              <router-link
                :to="{ path: '/announcement/business', query: { id: row.aid, active: 2 } }"
                class="bg-primary td-none"
                v-if="row.linkNum !== '0'"
                >{{ row.linkNum }}</router-link
              >
              <span class="bg-primary" v-else>
                {{ row.linkNum }}
              </span>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <div class="mt-15 jc-between">
        <div class="ai-center">
          <el-checkbox
            v-model="checkAll"
            label="全选"
            class="mr-10"
            @change="announcementChange"
            :indeterminate="isIndeterminate"
          ></el-checkbox>
          <el-select
            size="small"
            v-model="batchValue"
            placeholder="批量操作"
            :disabled="!announceSelection.length"
            @change="announceSelectBatch"
            clearable
            filterable
          >
            <el-option v-for="item in batchOptions" :key="item.k" :label="item.v" :value="item.k" />
          </el-select>
        </div>
      </div>
      <Paging class="mt-15" @change="handlePaginationChange" :total="pagination.total" />
    </el-card>
    <CustomColumnDialog ref="customColumnDialog" v-model:data="customAnnouncement" />
    <AnnouncementLogDialog ref="logDialog" />
    <AnnouncementEdit ref="announcementEdit" :list="attributeDocument" />
    <AnnouncementBatchCopy ref="announcementBatchCopy" />
    <SortChangeDialog ref="sortChangeDialog" />
  </div>
</template>

<script lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { defineComponent, reactive, ref, toRefs, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import AnnouncementEdit from '../cms/announcement/component/announcementEdit.vue'
import {
  announcementBatchOnline,
  announcementBatchRefresh,
  announcementDelete,
  announcementIsShow,
  announcementLine,
  announcementRefresh,
  announcementRefreshSortTime,
  getAnnouncementAttributeData,
  getAnnouncementList,
  getAnnounceSearchParams,
  batchHidden,
  batchShow,
  getSimpleAnnouncementList
} from '/@/api/announcement'
import { getTableStagingField } from '/@/api/config'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import AbroadExperience from '/@/components/base/select/abroadExperience.vue'
import Age from '/@/components/base/select/age.vue'
import Audit from '/@/components/base/select/audit.vue'
import Education from '/@/components/base/select/education.vue'
import JobCategory from '/@/components/base/select/jobCategory.vue'
import LevelTitle from '/@/components/base/select/levelTitle.vue'
import MajorCategory from '/@/components/base/select/majorCategory.vue'
import Political from '/@/components/base/select/political.vue'
import Region from '/@/components/base/select/region.vue'
import WorkExperience from '/@/components/base/select/workExperience.vue'
import WorkNature from '/@/components/base/select/workNature.vue'
import CustomColumnDialog from '/@/components/business/customColumnDialog.vue'
import AnnouncementBatchCopy from '/@/components/dialog/announcementBatchCopy.vue'
import AnnouncementLogDialog from '/@/views/announcement/components/announcementLogDialog.vue'
import Colunm from '/@/components/base/colunm.vue'
import SortChangeDialog from '/src/components/business/sortChangeDialog.vue'
import IsMiniapp from '/@/components/base/select/isMiniapp.vue'
import isMiniappChange from '/@/components/base/select/isMiniappChange.vue'

export default defineComponent({
  name: 'announcementList',

  components: {
    AnnouncementLogDialog,
    JobCategory,
    Education,
    Region,
    MajorCategory,
    DatePickerRange,
    WorkNature,
    AbroadExperience,
    WorkExperience,
    LevelTitle,
    Age,
    Political,
    Audit,
    CustomColumnDialog,
    Paging,
    AnnouncementEdit,
    AnnouncementBatchCopy,
    Colunm,
    SortChangeDialog,
    IsMiniapp,
    isMiniappChange
  },

  setup() {
    const state = <any>reactive({
      showMore: false,
      form: {
        isCooperation: 1,
        announcementTitleNum: '',
        name: '',
        companyName: '',
        jobCategoryId: '',
        educationType: '',
        city: [],
        status: '',
        majorId: '',
        firstReleaseTimeStart: '',
        firstReleaseTimeEnd: '',
        releaseTimeStart: '',
        releaseTimeEnd: '',
        department: '',
        natureType: '',
        abroadType: '',
        experienceType: '',
        titleType: '',
        ageType: '',
        politicalType: '',
        auditStatus: '',
        creator: '',
        overseasAttribute: '',
        notOverseasAttribute: '',
        isMiniapp: ''
      },
      isSimple: true,
      announcementList: [],
      amount: {},
      pagination: {
        total: 0,
        pageSize: 20,
        page: 1
      },
      overseasAttributeList: <any>[],
      notOverseasAttributeList: <any>[],
      batchOptions: [
        { k: 1, v: '再发布文档' },
        { k: 2, v: '下线文档' },
        { k: 3, v: '刷新文档' },
        { k: 4, v: '编辑属性' },
        { k: 5, v: '复制文档' },
        { k: 6, v: '移动文档' },
        { k: 7, v: '显示文档' },
        { k: 8, v: '隐藏文档' }
      ],
      announceSelection: [],
      checkAll: false,
      batchValue: null,
      loading: false,
      announceIds: '',
      attributeDocument: [],
      attributeData: [],
      columnList: [],
      statusRecruitList: []
    })
    const customAnnouncement = ref([
      {
        k: 1,
        v: '公告ID',
        name: 'announcementUid',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '公告标题',
        name: 'title',
        select: true,
        default: true
      },
      {
        k: 3,
        v: '所属单位',
        name: 'fullName',
        select: true,
        default: true
      },
      {
        k: 18,
        v: '排序',
        name: 'sort',
        select: true,
        default: false
      },
      {
        k: 4,
        v: '招聘人数',
        name: 'amount',
        select: true,
        default: true
      },
      {
        k: 5,
        v: '招聘职位',
        name: 'jobNum',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '在线职位',
        name: 'onlineCount',
        select: true,
        default: true
      },
      {
        k: 7,
        v: '平台投递',
        name: 'platformNum',
        select: true,
        default: true
      },
      {
        k: 8,
        v: '邮箱投递',
        name: 'emailNum',
        select: true,
        default: true
      },
      {
        k: 9,
        v: '面试邀约',
        name: 'jobInterviewNum',
        select: true,
        default: true
      },
      {
        k: 10,
        v: '招聘状态',
        name: 'recruitStatusTxt',
        select: true,
        default: true
      },
      {
        k: 11,
        v: '发布时间',
        name: 'refreshTime',
        select: true,
        default: true
      },
      {
        k: 12,
        v: '刷新时间',
        name: 'realRefreshTime',
        select: true,
        default: true
      },
      {
        k: 13,
        v: '操作',
        name: 'opration',
        select: true,
        default: true
      },
      {
        k: 14,
        v: '点击量',
        name: 'click',
        select: true,
        default: false
      },
      {
        k: 15,
        v: '审核状态',
        name: 'auditStatusTxt',
        select: true,
        default: false
      },
      {
        k: 16,
        v: '发布模式',
        name: 'isArticleTxt',
        select: true,
        default: false
      },
      {
        k: 17,
        v: '创建人',
        name: 'creatorName',
        select: true,
        default: false
      },
      {
        k: 18,
        v: '初次发布时间',
        name: 'firstReleaseTime',
        select: true,
        default: false
      },
      {
        k: 19,
        v: '网址投递',
        name: 'linkNum',
        select: false,
        default: false
      },
      {
        k: 20,
        v: '学科专业',
        name: 'majorTxt',
        select: false,
        default: false
      },
      {
        k: 21,
        v: '是否小程序',
        name: 'isMiniappTxt',
        select: false,
        default: false
      }
    ])
    const customColumnDialog = ref()
    const announcementTable = ref()
    const announcementForm = ref()
    const logDialog = ref()
    const announcementAudit = ref()
    const announcementEdit = ref()
    const announcementBatchCopy = ref()
    const router = useRouter()
    const isIndeterminate = ref(false)
    const sortChangeDialog = ref()

    const getList = async () => {
      state.loading = true
      let rs = {}
      if (state.isSimple) {
        rs = await getSimpleAnnouncementList(state.form)
        rs.amount = {}
      } else {
        rs = await getAnnouncementList(state.form)
      }
      state.announcementList = rs.list
      state.amount = rs.amount
      state.pagination.total = rs.pages.total
      state.loading = false
    }
    // getList()
    onMounted(async () => {
      ElMessage({
        message: '由于数据过多,默认不显示数据,请自行搜索',
        type: 'warning',
        duration: 5000
      })
      const res = await getTableStagingField('announcementQueryCooperativeUnitYes')
      const { columnList, statusRecruitList, overseasAttributeList, notOverseasAttributeList } =
        await getAnnounceSearchParams()
      state.columnList = columnList
      state.statusRecruitList = statusRecruitList
      state.overseasAttributeList = overseasAttributeList
      state.notOverseasAttributeList = notOverseasAttributeList
      if (!res.value) return
      const value = res.value.split(',')
      customAnnouncement.value = customAnnouncement.value.map((item: any) => {
        return {
          ...item,
          select: value.includes(item.name)
        }
      })
    })
    const handleOpenCustomColumn = () => {
      customColumnDialog.value.open('announcementQueryCooperativeUnitYes')
    }
    interface params {
      page: number
      limit: number
    }
    const handlePaginationChange = (data: params) => {
      state.form.page = data.page
      state.form.pageSize = data.limit
      getList()
    }
    const handleAnnounceChange = (data: any) => {
      state.announceSelection = data
      state.announceIds = data.map((item: any) => item.aid).join()
      if (data.length === state.announcementList.length) {
        state.checkAll = true
        isIndeterminate.value = false
      } else {
        state.checkAll = false
        isIndeterminate.value = data.length > 0
      }
    }
    const announcementChange = () => {
      announcementTable.value.toggleAllSelection()
    }
    // 排序
    const sortAnnouncementData = ({ prop, order }) => {
      Reflect.deleteProperty(state.form, 'sortRecruitCount')
      Reflect.deleteProperty(state.form, 'sortRecruitJobCount')
      Reflect.deleteProperty(state.form, 'sortOnlineJobCount')
      Reflect.deleteProperty(state.form, 'sortOuterTotal')
      Reflect.deleteProperty(state.form, 'sortOutsideTotal')
      Reflect.deleteProperty(state.form, 'sortJobInterviewNum')
      Reflect.deleteProperty(state.form, 'sortReleaseTime')
      Reflect.deleteProperty(state.form, 'sortRefreshTime')
      Reflect.deleteProperty(state.form, 'sortClickCount')
      Reflect.deleteProperty(state.form, 'sortAddTime')
      Reflect.deleteProperty(state.form, 'sortLinkNum')

      if (order === 'ascending') {
        // 正序
        state.form[prop] = 2
      } else if (order === 'descending') {
        state.form[prop] = 1
      }
      getList()
    }

    const reset = () => {
      announcementForm.value.resetFields()
      getList()
    }
    const refreshAnnounce = (id: string) => {
      ElMessageBox.confirm('确定要刷新该公告吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await announcementRefresh({ id })
        getList()
      })
    }

    interface announceOffline {
      id: number
      actionType: string
    }

    const announcementOffline = async (val: announceOffline) => {
      if (val.actionType === '2') {
        ElMessageBox.prompt('请填写下线原因', '提示', {
          inputPattern: /\S+/,
          inputErrorMessage: '请填写下线原因'
        }).then(async ({ value }) => {
          await announcementLine({ ...val, reason: value })
          getList()
        })
      } else {
        await announcementLine(val)
        getList()
      }
    }
    const showAnnouncement = async (val: announceOffline) => {
      await announcementIsShow(val)
      getList()
    }
    const handelLog = (id: string) => {
      logDialog.value.open(id)
    }

    const changeHideState = (changeState, type) => {
      ElMessageBox.confirm(`确定要${type === 1 ? '显示' : '隐藏'}文档吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { content } = await changeState({ ids: state.announceIds })
        if (content) {
          ElMessageBox.alert(content, '提示', {
            dangerouslyUseHTMLString: true,
            showConfirmButton: false
          })
        }
        getList()
      })
    }
    const announceSelectBatch = async (val: number) => {
      state.batchValue = null
      if (val === 7) {
        changeHideState(batchShow, 1)
      }

      if (val === 8) {
        changeHideState(batchHidden, 2)
      }

      if (val === 3) {
        ElMessageBox.confirm('确定要刷新该公告吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            await announcementBatchRefresh({ ids: state.announceIds })
            getList()
          })
          .catch(() => {})
      }
      if (val === 1) {
        await announcementBatchOnline({ ids: state.announceIds, actionType: val })
        getList()
      }
      if (val === 2) {
        ElMessageBox.prompt('请填写下线原因', '提示', {
          inputPattern: /\S+/,
          inputErrorMessage: '请填写下线原因'
        })
          .then(async ({ value }) => {
            if (!value) {
              ElMessage.warning('请填写下线原因')
              return
            }
            await announcementBatchOnline({
              ids: state.announceIds,
              actionType: val,
              reason: value
            })
            getList()
          })
          .catch(() => {})
      }
      if (val === 4) {
        const { attributeDocument } = await getAnnounceSearchParams()
        state.attributeDocument = attributeDocument
        announcementEdit.value.open(state.announceIds)
      }
      if (val === 5 || val === 6) {
        announcementBatchCopy.value.openBatchCopy(val, state.announceIds)
      }
    }
    const announceEdit = (id: string) => {
      router.push({ path: `/cms/announcementEdit/${id}` })
    }

    const toCompanyDetail = (id: string) => {
      router.push({
        path: '/company/details',
        query: { id }
      })
    }

    const downloadExcel = async () => {
      // 出loading
      state.loading = true
      getAnnouncementList({ ...state.form, export: 1 })
        .then((res) => {
          window.location.href = res.excelUrl
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    // 获取文档属性
    const getAttributeData = async (articleId: string) => {
      state.attributeData = await getAnnouncementAttributeData({ articleId })
    }

    // 刷新文档属性
    const refreshSortTime = async (attributeId: string, articleId: string) => {
      await announcementRefreshSortTime({ attributeId, articleId })
    }

    const announceDel = async (id: string) => {
      await announcementDelete({ id })
      getList()
    }

    /*
    编辑按钮
    有审核通过历史和没有审核通过历史均可操作
    在线状态 status=== '1'
    审核拒绝auditStatus==='-1'
    审核通过auditStatus=== '1'
    */
    const editButton = (data: any) => {
      const { firstReleaseTime, status, auditStatus } = data
      if (firstReleaseTime) {
        if (status === '1') {
          if (/^(1|-1|3)$/.test(auditStatus)) {
            return false
          }
        }
      } else if (/^(1|-1|3)$/.test(auditStatus)) {
        return false
      }
      return true
    }
    /*
    刷新、下线
    */
    const disabledOnlineButton = (data: any) => {
      const { status } = data
      if (status === '1') {
        return false
      }
      return true
    }
    /*
    isShow 1:显示，2:隐藏
    显示和隐藏不受上下线约束，单独做判断
     */

    /*
      再发布、显示
      再发布 =>下线 status=== '2'
    */
    const isShowButton = (data: any) => {
      const { status } = data
      if (status === '2') {
        return false
      }
      return true
    }

    // 排序
    const handleChangeSort = (row) => {
      const { aid: id, homeSort: sort } = row
      const callback = (newSort) => {
        row.homeSort = newSort
      }
      sortChangeDialog.value.open({ id, sort }, callback)
    }

    return {
      ...toRefs(state),
      customAnnouncement,
      handleOpenCustomColumn,
      customColumnDialog,
      handlePaginationChange,
      handleAnnounceChange,
      announcementTable,
      announcementChange,
      sortAnnouncementData,
      announcementForm,
      reset,
      refreshAnnounce,
      announcementOffline,
      showAnnouncement,
      handelLog,
      logDialog,
      announceSelectBatch,
      announcementAudit,
      announcementEdit,
      announcementBatchCopy,
      announceEdit,
      getList,
      downloadExcel,
      toCompanyDetail,
      getAttributeData,
      refreshSortTime,
      announceDel,
      editButton,
      disabledOnlineButton,
      isShowButton,
      handleChangeSort,
      sortChangeDialog,
      isIndeterminate
    } as any
  }
})
</script>

<style lang="scss" scoped>
.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
}
.column {
  .my-5 {
    margin-left: auto;
  }
}
.tip {
  font-size: 12px;
}
.danger {
  color: #d9041a;
  font-weight: bold;
}
.sort {
  display: flex;
  align-items: center;
  justify-content: center;
  .sort-edit {
    width: 20px;
    opacity: 0.6;
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
