<template>
  <el-dialog
    title="限制详情"
    v-model="dialogVisible"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="detail-content">
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="detailData.status == 1 ? 'success' : 'danger'">
              {{ detailData.status == 1 ? '启用' : '禁用' }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="限制ID">
            {{ detailData.id }}
          </el-descriptions-item>
          <el-descriptions-item label="限制名称">
            {{ detailData.name }}
          </el-descriptions-item>
          <el-descriptions-item label="单位信息">
            {{ detailData.companyInfo }}
          </el-descriptions-item>
          <el-descriptions-item label="限制类型">
            {{ detailData.limitTypeText }}
          </el-descriptions-item>

          <!-- 次数限制相关字段 -->
          <template v-if="detailData.limitType === 'count'">
            <el-descriptions-item label="时间限制(天)">
              {{ detailData.timeLimit }}
            </el-descriptions-item>
            <el-descriptions-item label="次数限制">
              {{ detailData.countLimit }}
            </el-descriptions-item>
            <el-descriptions-item label="限制描述" :span="2">
              {{ detailData.timeLimit }}天内最多投递{{ detailData.countLimit }}次
            </el-descriptions-item>
          </template>

          <!-- 条件限制相关字段 -->
          <template v-if="detailData.limitType === 'condition'">
            <el-descriptions-item label="条件字段">
              {{ getConditionFieldText(detailData.conditionField) }}
            </el-descriptions-item>
            <el-descriptions-item label="条件值">
              {{ detailData.conditionValue }}
            </el-descriptions-item>
            <el-descriptions-item label="限制描述" :span="2">
              {{ getConditionFieldText(detailData.conditionField) }}:
              {{ detailData.conditionValue }}
            </el-descriptions-item>
          </template>

          <el-descriptions-item label="错误提示信息" :span="2">
            <div class="error-message">
              {{ detailData.errorMessage }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ detailData.remark || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ detailData.createdByName }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ detailData.addTime }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 限制规则说明 -->
      <el-card class="detail-card">
        <template #header>
          <span>限制规则说明</span>
        </template>

        <div class="rule-description">
          <div v-if="detailData.limitType === 'count'" class="rule-item">
            <h4>次数限制规则：</h4>
            <ul>
              <li>
                在{{ detailData.timeLimit }}天内，同一用户最多可以向该单位投递{{
                  detailData.countLimit
                }}次简历
              </li>
              <li>超出限制时，系统将显示错误提示：{{ detailData.errorMessage }}</li>
              <li>时间窗口为滚动窗口，从用户第一次投递开始计算</li>
            </ul>
          </div>

          <div v-if="detailData.limitType === 'condition'" class="rule-item">
            <h4>条件限制规则：</h4>
            <ul>
              <li>
                当用户的{{ getConditionFieldText(detailData.conditionField) }}为"{{
                  detailData.conditionValue
                }}"时，禁止投递
              </li>
              <li>不满足条件时，系统将显示错误提示：{{ detailData.errorMessage }}</li>
              <li>条件检查在每次投递时进行</li>
            </ul>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

interface Props {
  visible: boolean
  detailData?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  detailData: () => ({})
})

const emit = defineEmits<Emits>()

// 弹窗显示状态
const dialogVisible = ref(false)

// 获取条件字段文本
const getConditionFieldText = (field: string) => {
  const fieldMap = {
    isAbroad: '海外经历',
    educationLevel: '学历水平',
    workExperience: '工作经验'
  }
  return fieldMap[field] || field
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
  },
  { immediate: true }
)

// 监听弹窗关闭
watch(dialogVisible, (val) => {
  if (!val) {
    emit('update:visible', false)
  }
})
</script>

<style scoped lang="scss">
.detail-content {
  .detail-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.error-message {
  color: #f56c6c;
  font-weight: 500;
  padding: 8px 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
}

.rule-description {
  .rule-item {
    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 16px;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.6;
        color: #606266;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
