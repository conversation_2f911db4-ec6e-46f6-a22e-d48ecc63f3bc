<template>
  <el-dialog
    title="缓存管理"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="cache-manage-content">
      <!-- 缓存统计 -->
      <el-card class="cache-stats-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>缓存统计</span>
            <el-button type="primary" size="small" @click="refreshStats" :loading="statsLoading">
              刷新
            </el-button>
          </div>
        </template>
        <div v-if="cacheStats" class="stats-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="stat-item">
                <span class="label">总缓存键数:</span>
                <span class="value">{{ cacheStats.totalKeys }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="stat-item">
                <span class="label">缓存大小:</span>
                <span class="value">{{ formatSize(cacheStats.cacheSize) }}</span>
              </div>
            </el-col>
          </el-row>

          <el-divider />

          <div class="type-stats">
            <h4>按类型分布</h4>
            <el-row :gutter="20">
              <el-col :span="8" v-for="(count, type) in cacheStats.byType" :key="type">
                <div class="stat-item">
                  <span class="label">{{ getTypeLabel(type) }}:</span>
                  <span class="value">{{ count }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="platform-stats">
            <h4>按平台分布</h4>
            <el-row :gutter="20">
              <el-col :span="6" v-for="(count, platform) in cacheStats.byPlatform" :key="platform">
                <div class="stat-item">
                  <span class="label">{{ platform }}:</span>
                  <span class="value">{{ count }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div v-else class="no-stats">
          <el-empty description="暂无缓存统计数据" />
        </div>
      </el-card>

      <!-- 缓存操作 -->
      <el-card class="cache-operations-card" shadow="never">
        <template #header>
          <span>缓存操作</span>
        </template>
        <div class="operations-content">
          <el-form :model="operationForm" label-width="100px">
            <el-form-item label="操作类型">
              <el-radio-group v-model="operationForm.type">
                <el-radio label="clear">清除缓存</el-radio>
                <el-radio label="warmup">预热缓存</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="配置类型" v-if="operationForm.type">
              <el-select v-model="operationForm.configType" placeholder="全部类型" clearable>
                <el-option label="职位信息" value="job" />
                <el-option label="公告信息" value="announcement" />
                <el-option label="单位信息" value="company" />
              </el-select>
            </el-form-item>

            <el-form-item label="目标ID" v-if="operationForm.type">
              <el-input
                v-model="operationForm.targetId"
                placeholder="可选，指定特定目标ID"
                type="number"
              />
            </el-form-item>

            <el-form-item label="平台类型" v-if="operationForm.type">
              <el-select v-model="operationForm.platform" placeholder="全平台" clearable>
                <el-option label="全平台" value="ALL" />
                <el-option label="PC端" value="PC" />
                <el-option label="H5端" value="H5" />
                <el-option label="小程序" value="MINI" />
              </el-select>
            </el-form-item>

            <el-form-item v-if="operationForm.type === 'warmup'" label="目标ID列表">
              <el-input
                v-model="operationForm.targetIds"
                placeholder="可选，多个ID用逗号分隔"
                type="textarea"
                :rows="2"
              />
              <div class="form-tip">多个目标ID请用逗号分隔，如：1214,1215,1216</div>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          @click="executeOperation"
          :loading="operationLoading"
          :disabled="!operationForm.type"
        >
          执行操作
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCacheStats, clearCache, warmupCache } from '/@/api/specialNeed'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

const emit = defineEmits<Emits>()

// 弹窗显示状态
const dialogVisible = ref(false)

// 缓存统计数据
const cacheStats = ref<any>(null)
const statsLoading = ref(false)

// 操作表单
const operationForm = reactive({
  type: '',
  configType: '',
  targetId: '',
  platform: '',
  targetIds: ''
})

const operationLoading = ref(false)

// 获取类型标签
const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    job: '职位',
    announcement: '公告',
    company: '单位'
  }
  return labels[type] || type
}

// 格式化文件大小
const formatSize = (bytes: number) => {
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(2)} MB`
}

// 刷新统计数据
const refreshStats = async () => {
  statsLoading.value = true
  try {
    cacheStats.value = await getCacheStats()
  } catch (error) {
    console.error('获取缓存统计失败:', error)
    ElMessage.error('获取缓存统计失败')
  } finally {
    statsLoading.value = false
  }
}

// 执行缓存操作
const executeOperation = async () => {
  try {
    const { type, configType, targetId, platform, targetIds } = operationForm

    if (type === 'clear') {
      await ElMessageBox.confirm(
        `确定要清除${configType ? getTypeLabel(configType) : '所有'}缓存吗？`,
        '确认清除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      operationLoading.value = true
      const params: any = {}
      if (configType) params.type = configType
      if (targetId) params.targetId = Number(targetId)
      if (platform) params.platform = platform

      await clearCache(params)
      ElMessage.success('缓存清除成功')
    } else if (type === 'warmup') {
      await ElMessageBox.confirm(
        `确定要预热${configType ? getTypeLabel(configType) : '所有'}缓存吗？这可能需要一些时间。`,
        '确认预热',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )

      operationLoading.value = true
      const params: any = {}
      if (configType) params.type = configType
      if (platform) params.platform = platform
      if (targetIds) {
        params.targetIds = targetIds
          .split(',')
          .map((id) => Number(id.trim()))
          .filter((id) => !isNaN(id))
      }

      await warmupCache(params)
      ElMessage.success('缓存预热成功')
    }

    // 刷新统计数据
    await refreshStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('缓存操作失败:', error)
      ElMessage.error('缓存操作失败')
    }
  } finally {
    operationLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val) {
      refreshStats()
    }
  },
  { immediate: true }
)

// 监听弹窗关闭
watch(dialogVisible, (val) => {
  if (!val) {
    emit('update:visible', false)
  }
})
</script>

<style scoped lang="scss">
.cache-manage-content {
  .cache-stats-card,
  .cache-operations-card {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .stats-content {
    .stat-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .label {
        color: #666;
      }

      .value {
        font-weight: bold;
        color: #409eff;
      }
    }

    .type-stats,
    .platform-stats {
      margin-top: 16px;

      h4 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 14px;
      }
    }
  }

  .no-stats {
    text-align: center;
    padding: 20px 0;
  }

  .operations-content {
    .form-tip {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
