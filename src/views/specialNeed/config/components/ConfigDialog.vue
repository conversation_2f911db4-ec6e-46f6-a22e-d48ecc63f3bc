<template>
  <el-dialog
    :title="isEdit ? '编辑配置' : '新增配置'"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="配置名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入配置名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="配置类型" prop="type">
            <el-select
              v-model="formData.type"
              placeholder="请选择配置类型"
              @change="handleTypeChange"
              style="width: 100%"
            >
              <el-option label="职位信息" value="job" />
              <el-option label="公告信息" value="announcement" />
              <el-option label="单位信息" value="company" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="目标ID" prop="targetId">
            <el-input v-model="formData.targetId" placeholder="请输入目标ID" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字段名称" prop="fieldName">
            <el-select
              v-model="formData.fieldName"
              placeholder="请选择字段名称"
              style="width: 100%"
              filterable
              :loading="fieldOptionsLoading"
            >
              <el-option
                v-for="(label, value) in fieldOptions"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="字段值" prop="fieldValue">
        <el-input
          v-model="formData.fieldValue"
          type="textarea"
          :rows="4"
          placeholder="请输入字段值"
          maxlength="5000"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="适用平台" prop="platform">
            <el-select v-model="formData.platform" placeholder="请选择适用平台" style="width: 100%">
              <el-option label="全平台" value="ALL" />
              <el-option label="PC端" value="PC" />
              <el-option label="H5端" value="H5" />
              <el-option label="小程序" value="MINI" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="生效开始时间">
            <el-date-picker
              v-model="formData.startTime"
              type="datetime"
              placeholder="选择开始时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效结束时间">
            <el-date-picker
              v-model="formData.endTime"
              type="datetime"
              placeholder="选择结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { getConfigDetail, createConfig, updateConfig, getFieldOptions } from '/@/api/specialNeed'

interface Props {
  visible: boolean
  editId?: number | string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editId: undefined
})

const emit = defineEmits<Emits>()

// 弹窗显示状态
const dialogVisible = ref(false)

// 是否编辑模式
const isEdit = ref(false)

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  name: '',
  type: '',
  targetId: '',
  fieldName: '',
  fieldValue: '',
  platform: 'ALL',
  status: 1,
  startTime: '',
  endTime: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { max: 100, message: '配置名称不能超过100个字符', trigger: 'blur' }
  ],
  type: [{ required: true, message: '请选择配置类型', trigger: 'change' }],
  targetId: [
    { required: true, message: '请输入目标ID', trigger: 'blur' },
    { pattern: /^\d+$/, message: '目标ID必须是数字', trigger: 'blur' }
  ],
  fieldName: [{ required: true, message: '请选择字段名称', trigger: 'change' }],
  fieldValue: [
    { required: true, message: '请输入字段值', trigger: 'blur' },
    { max: 5000, message: '字段值不能超过5000个字符', trigger: 'blur' }
  ],
  platform: [{ required: true, message: '请选择适用平台', trigger: 'change' }]
}

// 状态
const loading = ref(false)
const saveLoading = ref(false)
const fieldOptionsLoading = ref(false)
const fieldOptions = ref({})

// 获取字段选项
const loadFieldOptions = async (type: string) => {
  if (!type) return

  fieldOptionsLoading.value = true
  try {
    const res = await getFieldOptions({ type })
    fieldOptions.value = res || {}
  } catch (error) {
    console.error('获取字段选项失败:', error)
    fieldOptions.value = {}
  } finally {
    fieldOptionsLoading.value = false
  }
}

// 类型变化
const handleTypeChange = (type: string) => {
  formData.fieldName = ''
  loadFieldOptions(type)
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: '',
    targetId: '',
    fieldName: '',
    fieldValue: '',
    platform: 'ALL',
    status: 1,
    startTime: '',
    endTime: '',
    remark: ''
  })
  fieldOptions.value = {}
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 获取详情（编辑模式）
const getDetail = async () => {
  if (!isEdit.value || !props.editId) return

  loading.value = true
  try {
    const res = await getConfigDetail({ id: props.editId })

    // 填充表单数据
    Object.keys(formData).forEach((key) => {
      if (res[key] !== undefined) {
        formData[key] = res[key]
      }
    })

    // 加载字段选项
    if (formData.type) {
      await loadFieldOptions(formData.type)
    }
  } catch (error) {
    console.error('获取配置详情失败:', error)
    ElMessage.error('获取配置详情失败')
  } finally {
    loading.value = false
  }
}

// 保存
const handleSave = async () => {
  try {
    await formRef.value.validate()

    saveLoading.value = true

    const data = {
      ...formData
    }

    if (isEdit.value) {
      await updateConfig({ ...data, id: props.editId })
    } else {
      await createConfig(data)
    }

    emit('success')
    handleClose()
  } catch (error) {
    if (error !== false) {
      // 表单验证失败时error为false
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saveLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val) {
      isEdit.value = !!props.editId
      if (isEdit.value) {
        getDetail()
      } else {
        resetForm()
      }
    }
  },
  { immediate: true }
)

// 监听弹窗关闭
watch(dialogVisible, (val) => {
  if (!val) {
    emit('update:visible', false)
  }
})
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}
</style>
