<template>
  <div class="main">
    <div class="main-form">
      <el-form
        :model="formData"
        :class="['form', { 'form-disabled': formDisabled }]"
        label-width="90px"
        :rules="rules"
        ref="form"
      >
        <el-form-item label="所属单位" prop="companyName">
          <el-input
            v-model="formData.companyName"
            :disabled="!canEditCompanyName || formDisabled"
            placeholder="请输入单位名称"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="账号类型">
          <div class="account">
            <div>{{ accountInfo.companyMemberTypeText }}</div>
            <div v-if="!isMainAccount">如需变更子账号权限，请前往【账号查询】列表中设置</div>
          </div>
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" :disabled="!canEditAccount || formDisabled" />
        </el-form-item>
        <el-form-item label="姓名" prop="contact">
          <el-input v-model="formData.contact" :disabled="!canEditAccount || formDisabled" />
        </el-form-item>
        <el-form-item label="所在部门" prop="department">
          <el-input v-model="formData.department" :disabled="!canEditAccount || formDisabled" />
        </el-form-item>

        <div v-if="!isMainAccount">
          <el-form-item label="绑定邮箱" prop="email" :rules="email">
            <el-input v-model="formData.email" :disabled="!canEditAccount || formDisabled" />
          </el-form-item>

          <el-form-item label="绑定手机号">
            <el-input v-model="formData.mobile" :disabled="!canEditAccount || formDisabled" />
          </el-form-item>
        </div>

        <div class="senior" v-else>
          <el-form-item label="绑定信息" prop="mobile" :rules="mobile">
            <div class="flex">
              <el-checkbox
                class="mr-10"
                label="绑定手机号"
                v-model="checkMobile"
                true-label="1"
                false-label="2"
                :disabled="!canEditAccount || formDisabled"
              ></el-checkbox>
              <el-input
                placeholder="请输入手机号"
                :disabled="mobileDisabled || !canEditAccount || formDisabled"
                v-model="formData.mobile"
              ></el-input>
            </div>
          </el-form-item>

          <el-form-item prop="email" :rules="email">
            <div class="flex">
              <el-checkbox
                class="mr-10"
                label="绑定邮箱"
                v-model="checkEmail"
                true-label="1"
                false-label="2"
                :disabled="!canEditAccount || formDisabled"
              ></el-checkbox>
              <el-input
                v-model="formData.email"
                :disabled="emailDisabled || !canEditAccount || formDisabled"
                placeholder="请输入邮箱"
              ></el-input>
            </div>
          </el-form-item>
          <div class="error" v-if="showValidate">请选择绑定信息</div>
        </div>

        <el-form-item label="绑定微信">{{ accountInfo.wxBindText }} </el-form-item>
      </el-form>
      <div class="upload">
        <el-upload
          class="avatar-uploader"
          :show-file-list="false"
          action="/company-account/upload-avatar"
          :on-success="handleSuccess"
          v-if="accountInfo.avatarShow === ''"
        >
          <div class="avatar-uploader-icon el-icon-plus"></div>
        </el-upload>
        <div class="avatar" v-else>
          <img :src="accountInfo.avatarShow" alt="" />
        </div>
        <div class="text">个人头像</div>
      </div>
    </div>

    <el-button type="primary" :disabled="formDisabled" @click="submit">
      {{ formDisabled ? '该单位当前待审核，不可修改信息' : '确定' }}
    </el-button>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, ref, toRefs } from 'vue'
import { useRoute } from 'vue-router'
import { getAccountSetting, editAccount } from '/@/api/account'
import { verifyEmail, verifyPhone } from '/@/utils/toolsValidate'
import { ElMessage } from 'element-plus'

export default defineComponent({
  name: 'accountSetting',

  components: {},

  setup() {
    const route = useRoute()
    const id = route.params.id as string

    const state = reactive({
      formData: {
        id: computed(() => route.params.id),
        companyName: '',
        username: '',
        contact: '',
        department: '',
        email: '',
        mobile: '',
        avatar: ''
      },
      checkMobile: '',
      checkEmail: '',
      accountInfo: {},
      formDisabled: false,
      emailDisabled: computed(() => state.checkEmail !== '1'),
      mobileDisabled: computed(() => state.checkMobile !== '1'),
      isMainAccount: computed(() => state.accountInfo.companyMemberType === 0),
      showValidate: computed(() => state.checkMobile === '2' && state.checkEmail === '2'),
      canEditAccount: computed(() => state.accountInfo.canEditAccount),
      canEditCompanyName: computed(() => state.accountInfo.canEditCompanyName),
      isInAuditStatus: computed(() => [7, 8, 9].includes(state.accountInfo.companyAuditStatus))
    })

    const form = ref()

    const rules = ref({
      companyName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
      username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
      contact: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      department: [{ required: true, message: '请输入所在部门', trigger: 'blur' }]
    })

    const validateEmail = (rule, value, callback) => {
      const { email } = state.formData
      const { checkEmail } = state

      if (checkEmail === '1' && !email) {
        callback(new Error('请输入邮箱'))
      } else if (checkEmail === '1' && !verifyEmail(email)) {
        callback(new Error('请输入正确的邮箱'))
      } else {
        callback()
      }
    }

    const validateMobile = (rule, value, callback) => {
      const { mobile } = state.formData
      const { checkMobile } = state

      if (checkMobile === '1' && !mobile) {
        callback(new Error('请输入手机号'))
      } else if (checkMobile === '1' && !verifyPhone(mobile)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }

    const email = ref({ required: true, validator: validateEmail, trigger: ['change', 'blur'] })
    const mobile = ref({ required: true, validator: validateMobile, trigger: ['change', 'blur'] })

    const getDetail = async () => {
      try {
        state.accountInfo = await getAccountSetting({ id })

        // 根据审核状态控制表单状态
        if (state.isInAuditStatus) {
          state.formDisabled = true
        }

        // 填充表单数据
        Object.keys(state.formData).forEach((key) => {
          if (state.accountInfo[key]) {
            state.formData[key] = state.accountInfo[key]
          }
        })

        // 设置单位名称
        state.formData.companyName = state.accountInfo.companyName || ''

        state.checkMobile = state.accountInfo.mobile ? '1' : '2'
        state.checkEmail = state.accountInfo.email ? '1' : '2'
      } catch (error) {
        console.error('获取账号详情失败:', error)
      }
    }

    const handleSuccess = (res: any) => {
      const { url, fullUrl } = res.data
      state.accountInfo.avatarShow = fullUrl
      state.formData.avatar = url
    }

    const submit = async () => {
      // 1. 检查是否为待审核状态
      if (state.isInAuditStatus) {
        return
      }

      // 2. 表单验证
      try {
        await form.value.validate()
      } catch (error) {
        return
      }

      if (state.showValidate) {
        return
      }

      // 3. 提交数据
      try {
        await editAccount(state.formData)
      } catch (error) {
        // handleSubmitError(error)
      }
    }

    getDetail()

    return { ...toRefs(state), form, rules, handleSuccess, submit, email, mobile } as any
  }
})
</script>

<style lang="scss" scoped>
.main {
  background-color: white;
  padding: 20px;
  text-align: center;

  .main-form {
    display: flex;

    .upload {
      margin-left: 100px;
    }

    .text {
      width: 100%;
      text-align: center;
      margin-top: 10px;
    }

    .avatar-uploader {
      width: 150px;
      height: 150px;
      border: 1px dashed #ccc;

      :deep(.el-upload) {
        width: 100%;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        cursor: pointer;
      }
    }

    .avatar {
      width: 150px;
      height: 150px;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
  .senior {
    position: relative;
  }
  .error {
    position: absolute;
    color: #fe676a;
    font-size: 12px;
    bottom: -15px;
    left: 20px;
  }

  .account {
    text-align: left;
  }

  .form {
    width: 600px;
  }

  .form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }

  // 禁用状态样式
  .form-disabled {
    .el-input__inner {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
    }

    .el-input.is-disabled .el-input__inner {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
      cursor: not-allowed;
    }

    .el-checkbox.is-disabled {
      .el-checkbox__label {
        color: #c0c4cc;
      }
    }
  }

  // 单位名称字段特殊样式
  .el-form-item {
    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
      line-height: 1.4;
    }
  }
}
</style>
