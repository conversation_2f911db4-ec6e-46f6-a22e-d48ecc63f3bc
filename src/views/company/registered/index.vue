<template>
  <el-card>
    <BaseTable @search="handleSearch" @downloadFile="handleDownload" @reset="resetForm"></BaseTable>
    <div class="unitCount">
      共计：<span class="danger">{{ pagination.total }}</span
      >所单位
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      align="center"
      @sort-change="handleSortable"
    >
      <el-table-column prop="fullName" label="单位名称" width="100" align="center" />
      <el-table-column prop="username" label="账号" align="center" />
      <el-table-column prop="contact" label="联系人" align="center" />
      <el-table-column prop="department" label="所在部门" align="center" />
      <el-table-column prop="mMobile" label="注册手机号" align="center" />
      <el-table-column prop="mEmail" label="注册邮箱" align="center" />
      <el-table-column prop="cMobile" label="联系电话" align="center" />
      <el-table-column prop="cEmail" label="联系邮箱" align="center" />
      <el-table-column prop="telephone" label="联系邮箱" align="center" />
      <el-table-column prop="natureTxt" label="单位性质" align="center" />
      <el-table-column prop="typeTxt" label="单位类型" align="center" />
      <el-table-column prop="industryTxt" label="所属行业" align="center" />
      <el-table-column
        prop="sortAddTime"
        label="注册时间"
        sortable="custom"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <span>{{ row.addTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="所在地" align="center" />
    </el-table>
    <div class="paging">
      <Paging :total="pagination.total" @change="change"></Paging>
    </div>
  </el-card>
</template>
<script lang="ts">
import { onMounted, reactive, toRefs } from 'vue'
import BaseTable from '/@/views/company/components/baseTable.vue'
import Paging from '/@/components/base/paging.vue'
import { registeredSearch } from '/@/api/unitManage'

export default {
  name: 'companyRegisteredQuery',
  components: { BaseTable, Paging },
  setup() {
    const state = <any>reactive({
      tableData: [],
      form: {
        sortAddTime: 1,
        industryId: '',
        area: ''
      },
      // 分页信息
      pagination: {
        total: 0,
        limit: '',
        page: ''
      },
      downloaUrl: '',
      loading: false
    })
    // 拿数据方法
    const getData = async () => {
      state.loading = true
      const {
        form: { industryId, area },
        form
      } = state
      const postData = {
        ...form,
        industryId: industryId.length ? (<any>industryId).join() : '',
        area: area.length ? (<any>area).join() : ''
      }
      const { list, pages } = await registeredSearch(postData)
      state.tableData = list
      state.pagination = pages
      state.loading = false
    }
    // 发请求获取数据(拿到子组件的数据)
    const handleSearch = async (val: any) => {
      const { list, pages } = await registeredSearch(val)
      state.tableData = list
      state.form = val
      state.pagination = pages
    }
    const handleDownload = async (val: any) => {
      const { excelUrl } = await registeredSearch(val)
      state.downloaUrl = excelUrl
      window.location.href = excelUrl
    }
    onMounted(() => {
      getData()
    })
    const resetForm = () => {
      getData()
    }
    // 分页
    const change = (data: any) => {
      state.form.page = data.page
      state.form.pageSize = data.limit
      getData()
    }
    // 排序
    const handleSortable = ({ prop, order }) => {
      Reflect.deleteProperty(state.form, 'sortAddTime')
      if (order === 'ascending') {
        // 正序
        state.form[prop] = 2
      } else if (order === 'descending') {
        state.form[prop] = 1
      }
      getData()
    }
    return { ...toRefs(state), handleSearch, change, handleDownload, resetForm, handleSortable }
  }
}
</script>
<style lang="scss" scoped>
.unitCount {
  margin: 10px 0;
  height: 30px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: #d9041a;
    font-weight: bold;
    margin: 0 5px 0 0;
  }
}
.paging {
  margin-top: 30px;
}
</style>
