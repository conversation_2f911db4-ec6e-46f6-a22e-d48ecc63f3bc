<template>
  <!-- Form -->
  <!-- <el-dialog v-model="dialogFormVisible" title="新增单位" > -->
  <div class="bigbox" v-loading="loading">
    <div class="title">新增单位</div>
    <div v-if="id" class="reason">拒绝原因：{{ form.reason }}</div>
    <!-- 已合作单位 -->
    <el-form :model="form" :rules="rules" ref="formData" v-if="change">
      <el-form-item label="单位类型" prop="isCooperation">
        <el-select
          v-model="form.isCooperation"
          placeholder="请选择合作类型"
          style="width: 720px"
          @change="changeIscooper"
          v-if="!id"
        >
          <el-option
            v-for="item in unitList.companyCooperationList"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          ></el-option>
        </el-select>
        <div v-else>合作单位</div>
      </el-form-item>
      <el-form-item label="单位名称" prop="fullName" style="width: 800px">
        <el-input
          v-model="form.fullName"
          autocomplete="off"
          placeholder="请填写单位全称；若属于二级单位或者课题组，请具体到二级单位或者课题组"
          clearable
        ></el-input>
      </el-form-item>
      <el-row>
        <el-col :span="6">
          <el-form-item label="单位类型" prop="type">
            <el-select
              v-model="form.type"
              clearable
              placeholder="请选择单位类型"
              style="width: 200px"
            >
              <el-option
                v-for="item in unitList.companyTypeList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="单位性质" label-position="right" prop="nature">
            <el-select v-model="form.nature" placeholder="请选择单位性质" style="width: 200px">
              <el-option
                v-for="item in unitList.companyNatureList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="所属行业" prop="industryId" style="width: 800px">
        <Industry v-model="form.industryId"></Industry>
      </el-form-item>
      <el-row>
        <el-form-item label="单位地址" prop="area">
          <Region v-model="form.area"></Region>
        </el-form-item>
        <el-form-item prop="address">
          <el-input
            v-model="form.address"
            placeholder="详细地址"
            style="width: 510px"
            clearable
            class="ml-20"
          />
        </el-form-item>
      </el-row>

      <el-row>
        <el-form-item label="联系人" prop="contact">
          <el-input
            v-model="form.contact"
            autocomplete="off"
            placeholder="请填写联系人姓名"
            class="contact"
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item label="所在部门" prop="department" class="department">
          <el-input
            v-model="form.department"
            autocomplete="off"
            placeholder="请填写所在部门"
            style="width: 200px"
          ></el-input>
        </el-form-item>
      </el-row>
      <el-form-item label="注册方式" prop="mobile" :rules="mobile">
        <div class="flex">
          <el-checkbox
            class="mr-10"
            label="手机号注册"
            v-model="checkMobile"
            true-label="1"
            false-label="2"
            @change="handleWayChange('mobile')"
          ></el-checkbox>

          <el-input
            placeholder="请输入手机号码"
            :disabled="mobileDisabled"
            v-model="form.mobile"
          ></el-input>
        </div>
      </el-form-item>

      <el-form-item label-width="80px" :rules="email" prop="email">
        <div>
          <div class="flex">
            <el-checkbox
              class="mr-10"
              label="邮箱注册"
              v-model="checkEmail"
              true-label="1"
              false-label="2"
              @change="handleWayChange('email')"
            ></el-checkbox>
            <el-input
              :disabled="emailDisabled"
              v-model="form.email"
              placeholder="请输入邮箱地址"
            ></el-input>
          </div>
          <div class="error" v-if="showWayValidate">请选择注册方式</div>
        </div>
      </el-form-item>

      <el-form-item label="联系方式" class="ml-10" prop="telephone" style="width: 790px">
        <el-input
          v-model="form.telephone"
          autocomplete="off"
          placeholder="请填写单位联系方式"
        ></el-input>
      </el-form-item>
      <el-form-item label="所属业务员" prop="adminId">
        <el-select
          v-model="form.adminId"
          filterable
          placeholder="请选择所属业务员"
          style="width: 708px"
        >
          <el-option
            v-for="item in <any>saleMan"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="单位资质证明" prop="licensePath" ref="formLicen">
        <el-upload
          ref="formLicenPath"
          class="upload-demo"
          action="upload/image"
          :on-success="handleSuccess"
          multiple
          :limit="3"
          v-model="form.licensePath"
          :before-upload="beforeLicenseUpload"
        >
          <el-button size="small" type="primary" class="chose-document">选择文件</el-button>
        </el-upload>
        <a v-if="id" :href="form.licensePath" target="_blank" class="preview">预览</a>
      </el-form-item>
      <el-form-item label="经办人身份证明" prop="personInfoPath" ref="formPerson">
        <el-upload
          ref="formPersonPath"
          class="upload-demo"
          action="upload/image"
          multiple
          :limit="3"
          :on-success="handleSuccesses"
          v-model="form.personInfoPath"
          :before-upload="beforeLicenseUpload"
        >
          <el-button size="small" type="primary">选择文件</el-button>
        </el-upload>
        <a v-if="id" :href="form.personInfoPath" target="_blank" class="preview">预览</a>
      </el-form-item>
      <el-form-item label="投递配置" prop="deliveryType">
        <el-select v-model="form.deliveryType">
          <el-option
            v-for="item in unitList.deliveryType"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账号性质" prop="accountNature">
        <el-select v-model="form.accountNature">
          <el-option
            v-for="item in unitList.accountNature"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="单位群组" prop="groupIds">
        <CompanyGroup v-model="form.groupIds" :data="companyGroupList" />
      </el-form-item>
    </el-form>
    <!-- 非合作单位 -->
    <el-form :model="formIsCoope" :rules="isRules" ref="formIsCoopeData" v-else>
      <el-form-item label="单位类型" prop="isCooperation">
        <el-select
          v-model="formIsCoope.isCooperation"
          placeholder="请选择合作类型"
          style="width: 720px"
          @change="changeCooper"
        >
          <el-option
            v-for="item in unitList.companyCooperationList"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          ></el-option>
        </el-select>
        <el-button type="primary" class="uploadBtn" @click="status = !status"
          >{{ status ? '单个' : '批量' }}新增</el-button
        >
      </el-form-item>
      <div v-if="status === false">
        <el-form-item label="单位名称" prop="fullName" style="width: 800px">
          <el-input
            v-model="formIsCoope.fullName"
            autocomplete="off"
            placeholder="请填写单位全称；若属于二级单位或者课题组，请具体到二级单位或者课题组"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="单位简称" prop="shortName">
          <el-input
            style="width: 200px"
            v-model="formIsCoope.shortName"
            maxlength="50"
            placeholder="请输入单位简称，最多50字"
            clearable
          ></el-input>
        </el-form-item>
        <el-row class="region">
          <el-col :span="6">
            <el-form-item label="单位类型" prop="type">
              <el-select
                v-model="formIsCoope.type"
                clearable
                placeholder="请选择单位类型"
                style="width: 200px"
              >
                <el-option
                  v-for="item in unitList.companyTypeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item prop="nature" label="单位性质">
              <el-select
                v-model="formIsCoope.nature"
                placeholder="请选择单位性质"
                class="contact"
                style="width: 200px"
              >
                <el-option
                  v-for="item in unitList.companyNatureList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="所属行业" class="ml-10" prop="industryId" style="width: 720px">
          <Industry v-model="formIsCoope.industryId"></Industry>
        </el-form-item>

        <el-row class="ml-10">
          <el-form-item label="单位地址" prop="area">
            <Region v-model="formIsCoope.area"></Region>
          </el-form-item>
          <el-form-item prop="address" class="ml-10">
            <el-input
              v-model="formIsCoope.address"
              placeholder="详细地址"
              style="width: 520px"
              clearable
            />
          </el-form-item>
        </el-row>

        <el-row class="ml-10">
          <el-form-item label="联系人" prop="contact">
            <el-input
              v-model="formIsCoope.contact"
              autocomplete="off"
              placeholder="请填写联系人姓名"
              class="contact"
              style="width: 300px"
            ></el-input>
          </el-form-item>
          <el-form-item label="所在部门" prop="department" class="ml-30">
            <el-input
              v-model="formIsCoope.department"
              autocomplete="off"
              placeholder="请填写所在部门"
              clearable
              style="width: 322px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-form-item label="联系电话" style="width: 790px" prop="mobile" class="ml-10">
          <el-input v-model="formIsCoope.mobile" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="联系邮箱" style="width: 790px" prop="email" class="ml-10">
          <el-input v-model="formIsCoope.email" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="联系方式" style="width: 790px" prop="telephone" class="ml-10">
          <el-input
            v-model="formIsCoope.telephone"
            autocomplete="off"
            placeholder="请填写单位联系方式"
          ></el-input>
        </el-form-item>

        <el-form-item label="单位群组" prop="groupIds">
          <CompanyGroup v-model="formIsCoope.groupIds" :data="companyGroupList" />
        </el-form-item>
      </div>
      <div v-else>
        <el-form-item label="上传文件" prop="excelPath">
          <el-row>
            <el-upload
              class="upload-demo"
              action="company/upload-excel"
              :limit="1"
              :on-success="excelSuccess"
              :on-remove="removeExcel"
            >
              <el-button size="small" type="primary" class="chose-document">选择文件</el-button>
            </el-upload>
            <p class="upload" @click="downloadFile">下载导入模板</p>
          </el-row>
        </el-form-item>
      </div>
    </el-form>

    <div class="button" v-if="change">
      <span class="dialog-footer">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="submitBtn(form)">提交</el-button>
      </span>
    </div>
    <div class="button" v-else>
      <span class="dialog-footer">
        <el-button @click="resetIsCoope" v-show="!status">重置</el-button>
        <el-button type="primary" @click="isCoopeSubmitBtn(formIsCoope)">提交</el-button>
      </span>
    </div>
  </div>
</template>

<script lang="ts">
import { computed, onMounted, reactive, ref, toRefs, watch, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import Region from '../../../components/base/select/region.vue'
import {
  addCooperationCompany,
  addUnit,
  getAllSale,
  getAuditDetailEdit,
  getUnitList,
  postWaitCooperation,
  auditDetailSave
} from '/@/api/unitManage'
import Industry from '/@/components/base/industry.vue'
import { verifyEmail, verifyPhone } from '/@/utils/toolsValidate'
import { getCompanyGroup } from '/@/api/config'
import CompanyGroup from '/@/components/base/select/companyGroup.vue'

export default {
  name: 'addCompany',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  components: { Region, Industry, CompanyGroup },
  setup(props, { emit }) {
    const state = reactive({
      loading: false,
      // 表单绑定数据
      form: <any>{
        isCooperation: '', // 单位类型
        fullName: '', // 单位名称
        type: '', // 单位性质
        nature: '', // 单位类型
        industryId: [], // 所属行业
        area: [], // 所在地区
        address: '', // 详细地址
        contact: '', // 联系人
        department: '', // 所在部门
        mobile: '',
        email: '', // 邮箱
        telephone: '', // 联系方式
        licensePath: '', // 单位资质认证
        personInfoPath: '', // 经办人身份证明
        adminId: '', // 业务员
        accountNature: '', // 账号性质
        deliveryType: '',
        groupIds: '15'
      },
      formIsCoope: <any>{
        isCooperation: '', // 单位类型
        fullName: '', // 单位名称
        shortName: '', // 单位简称
        nature: '', // 单位类型
        type: '', // 单位性质
        industryId: [], // 所属行业
        area: [], // 所在地区
        address: '', // 详细地址
        contact: '', // 联系人
        department: '', // 所在部门
        mobile: '', // 联系电话
        email: '', // 邮箱
        telephone: '', // 联系方式
        groupIds: '18'
      },
      checkMobile: '1',
      checkEmail: '2',
      emailDisabled: computed(() => state.checkEmail !== '1'),
      mobileDisabled: computed(() => state.checkMobile !== '1'),
      showWayValidate: computed(() => state.checkMobile === '2' && state.checkEmail === '2'),
      // 规则校验数据
      rules: {
        isCooperation: [{ required: true, message: '请选择合作单位', trigger: 'change' }],
        fullName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
        type: [{ required: false, message: '请选择单位类型', trigger: 'change' }],
        nature: [{ required: true, message: '请选择单位性质', trigger: 'change' }],
        industryId: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
        area: [{ required: true, message: '请选择合作单位', trigger: 'change' }],
        address: [{ required: true, message: '请填写详细地址', trigger: 'blur' }],
        contact: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
        department: [{ required: true, message: '请填写所在部门', trigger: 'blur' }],
        adminId: [{ required: true, message: '请选择所属业务员', trigger: 'change' }],
        licensePath: [{ required: true, message: '请上传单位资质证明', trigger: 'success' }],
        personInfoPath: [{ required: true, message: '请上传经办人身份证明', trigger: 'change' }],
        excelPath: [{ required: true, message: '请上传文件', trigger: 'change' }],
        deliveryType: [{ required: true, message: '请选择投递配置', trigger: 'change' }],
        groupIds: [{ required: true, message: '请选择单位群组', trigger: 'change' }]
      },
      // 校验规则2
      isRules: {
        isCooperation: [{ required: true, message: '请选择合作单位', trigger: 'change' }],
        fullName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
        type: [{ required: false, message: '请选择单位类型', trigger: 'change' }],
        nature: [{ required: true, message: '请选择单位性质', trigger: 'change' }],
        groupIds: [{ required: true, message: '请选择单位群组', trigger: 'change' }]
      },
      url: '',
      saleMan: [],
      id: '',
      companyGroupList: []
    })

    const validateEmail = (rule, value, callback) => {
      const { email } = state.form
      const { checkEmail } = state

      if (checkEmail === '1' && !email) {
        callback(new Error('请输入邮箱'))
      } else if (checkEmail === '1' && !verifyEmail(email)) {
        callback(new Error('请输入正确的邮箱'))
      } else {
        callback()
      }
    }

    const validateMobile = (rule, value, callback) => {
      const { mobile } = state.form
      const { checkMobile } = state

      if (checkMobile === '1' && !mobile) {
        callback(new Error('请输入手机号'))
      } else if (checkMobile === '1' && !verifyPhone(mobile)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }

    const email = ref({ required: true, validator: validateEmail, trigger: ['change', 'blur'] })
    const mobile = ref({ required: true, validator: validateMobile, trigger: ['change', 'blur'] })
    // 数据列表
    const unitList = <any>ref({})

    // 批量上传
    const status = ref(false)
    // 控制弹窗的开关
    const dialogFormVisible = computed({
      get() {
        return props.modelValue
      },
      set(val) {
        emit('update:modelValue', val)
      }
    })

    // 参数
    const route = useRoute()
    const { proxy } = getCurrentInstance() as any
    const router = useRouter()
    // 获取后台单位类型等
    onMounted(async () => {
      state.loading = true
      state.companyGroupList = await getCompanyGroup()
      const res = await getUnitList()
      state.saleMan = await getAllSale()
      unitList.value = res
      state.form.isCooperation = '1'
      state.formIsCoope.isCooperation = '0'
      state.loading = false
      watch(
        () => route.params.id,
        async (companyId: any) => {
          if (companyId) {
            state.loading = true
            state.id = companyId
            const { baseInfo } = await getAuditDetailEdit({ companyId })
            state.form = { ...baseInfo }
            state.form.isCooperation = '1'
            state.form.area = baseInfo.area.split(',')
            state.loading = false
          }
        },
        { immediate: true, deep: true }
      )
    })
    // 重置按钮
    const formData = ref()
    const formIsCoopeData = ref()
    const formLicen = ref()
    const formPerson = ref()
    const formLicenPath = ref()
    const formPersonPath = ref()
    const resetForm = () => {
      formData.value.resetFields()
      formLicenPath.value.clearFiles()
      formPersonPath.value.clearFiles()
      setTimeout(() => {
        formData.value.clearValidate()
      }, 0)
      state.checkMobile = '1'
    }
    const resetIsCoope = () => {
      formIsCoopeData.value.resetFields()
      setTimeout(() => {
        formIsCoopeData.value.clearValidate()
      })
    }
    // 提交按钮
    const submitBtn = (val: any) => {
      // 先验证
      formData.value.validate(async (valid: boolean) => {
        if (!state.id) {
          if (valid) {
            await addCooperationCompany(val)
            resetForm()
          } else {
            return false
          }
        } else if (valid) {
          const res = { ...state.form, companyId: route.params.id }
          await auditDetailSave(res)
          proxy.mittBus.emit('closeCurrentViewTag')
          router.replace({ name: 'joinAudit' })
        } else {
          return false
        }
        return valid
      })
    }
    const isCoopeSubmitBtn = async (val: any) => {
      if (status.value === false) {
        formIsCoopeData.value.validate(async (valid: boolean) => {
          if (valid) {
            await addUnit(val)
            resetIsCoope()
          } else {
            return false
          }
          return val
        })
      } else {
        await postWaitCooperation({ filePath: state.url })
        ElMessage.success('操作成功')
        resetIsCoope()
      }
    }
    // 单位资质证明上传成功的钩子函数
    const handleSuccess = (response: any) => {
      state.form.licensePath = response.data.fullUrl
      formLicen.value.clearValidate()
    }
    const handleSuccesses = (response: any) => {
      state.form.personInfoPath = response.data.fullUrl
      formPerson.value.clearValidate()
    }
    const excelSuccess = (res: any) => {
      state.url = res.data.url
    }
    const removeExcel = () => {
      state.url = ''
    }
    const beforeLicenseUpload = (file: any) => {
      const isJPG =
        file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif'
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isJPG) {
        ElMessage.error('上传文件只能是 JPG/PNG/GIF 格式!')
      }
      if (!isLt10M) {
        ElMessage.error('上传文件大小不能超过10M')
      }
      return isJPG && isLt10M
    }
    const change = ref(true)
    // 合作单位
    const changeIscooper = (data: String) => {
      if (data !== '1') {
        change.value = false
        state.formIsCoope.isCooperation = '2'
      }
    }
    const changeCooper = (data: String) => {
      if (data !== '2') {
        change.value = true
        state.form.isCooperation = '1'
      }
    }

    const downloadFile = () => {
      window.location.href =
        '//test.gcjob.ideaboat.cn/uploads/template/company_unit_no_template.xlsx'
    }

    const handleWayChange = (val: any) => {
      if (val === 'mobile' && state.checkMobile === '2') {
        state.form.mobile = ''
      } else {
        state.form.email = ''
      }
    }

    return {
      ...toRefs(state),
      formData,
      resetForm,
      dialogFormVisible,
      submitBtn,
      handleSuccess,
      handleSuccesses,
      unitList,
      status,
      isCoopeSubmitBtn,
      changeIscooper,
      changeCooper,
      change,
      formIsCoopeData,
      downloadFile,
      formLicen,
      formPerson,
      formLicenPath,
      formPersonPath,
      resetIsCoope,
      excelSuccess,
      removeExcel,
      beforeLicenseUpload,
      email,
      mobile,
      handleWayChange
    } as any
  }
}
</script>

<style lang="scss">
.bigbox {
  background-color: #fff;
  border-radius: 10px;
  padding: 10px 20px;
}

.error {
  position: absolute;
  color: #fe676a;
  font-size: 12px;
  top: 22px;
}
.el-overlay-dialog {
  width: 1800px;
  height: 1300px;
}
.uploadBtn {
  margin-left: 20px;
}

.row-bg {
  padding: 10px 0;
}
.customWidth {
  width: 80%;
}
.el-form-item--medium {
  margin-left: 20px;
}

.company-address {
  margin-left: -11px;
}
.contact {
  margin-left: 15px;
}
.department {
  margin-left: 25px;
}

.email {
  margin-left: 20px;
}

.button {
  text-align: center;
  margin-top: 20px;
}
.chose-document {
  margin-left: 15px;
}
.title {
  border-left: 2px solid #196bf9;
  text-indent: 1em;
  margin: 10px 0px 10px 0;
}

.upload {
  cursor: pointer;
  color: #2c93fa;
  margin-left: 15px;
}
.reason {
  padding: 10px 20px;
}
.preview {
  flex: 1 0 auto;
  margin-left: 10px;
  text-decoration: none;
  color: var(--el-color-primary);
}
.el-form-item--medium .el-form-item__content {
  display: flex;
}
</style>
