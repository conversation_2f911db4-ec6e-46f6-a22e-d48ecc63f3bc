<template>
  <div class="main">
    <el-form ref="formVmSearch" :model="formDataSearch" label-width="120px">
      <div class="flex">
        <el-col :span="6">
          <el-form-item label="协议ID:" prop="id">
            <el-input v-model="formDataSearch.id" clearable placeholder="请输入ID" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="协议标题:" prop="name">
            <el-input v-model="formDataSearch.name" clearable placeholder="协议标题进行模糊搜索" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="协议类型:" prop="agreementCategoryId">
            <el-select
              class="select-width-100"
              v-model="formDataSearch.type"
              clearable
              placeholder="请选择协议类型"
            >
              <el-option
                v-for="item in selectData.typeList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="协议状态:" prop="agreementType">
            <el-select
              class="select-width-100"
              v-model="formDataSearch.status"
              clearable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in selectData.statusList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </div>
      <div class="flex">
        <el-col :span="24">
          <div class="nowrap ml-15 btn-search-group">
            <el-button type="primary" @click="getList">搜索</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </el-col>
      </div>
    </el-form>
    <div class="btn-group">
      <el-button type="success" class="el-icon-plus" @click="formSubmitAdd()"> 新增 </el-button>
    </div>
    <el-table
      :data="list"
      border
      v-loading="loading"
      @sort-change="handleSortable"
      ref="ConfigTable"
    >
      <template v-for="(item, index) in listHeader">
        <el-table-column
          v-if="item.select && item.k === 1"
          :key="index"
          :label="item.v"
          prop="id"
          align="center"
          width="70"
        />
        <el-table-column
          v-if="item.select && item.k === 2"
          :key="index"
          :label="item.v"
          prop="name"
          align="center"
          width="260"
          show-overflow-tooltip
        />
        <el-table-column
          v-if="item.select && item.k === 3"
          :key="index"
          :label="item.v"
          prop="typeText"
          align="center"
          width="150"
        />
        <el-table-column
          v-if="item.select && item.k === 4"
          :key="index"
          :label="item.v"
          prop="statusText"
          width="130"
          align="center"
        >
          <template #default="scope">
            <el-tag
              class="tag"
              effect="plain"
              v-if="scope.row.status === '1'"
              type="success"
              size="large"
              >{{ scope.row.statusText }}</el-tag
            >
            <el-tag
              class="tag"
              effect="plain"
              v-if="scope.row.status === '2'"
              type="info"
              size="large"
              >{{ scope.row.statusText }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 5"
          :key="index"
          :label="item.v"
          prop="version"
          width="110"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 6"
          :key="index"
          :label="item.v"
          prop="description"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 7"
          :key="index"
          :label="item.v"
          prop="addTime"
          width="180"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 8"
          :key="index"
          :label="item.v"
          prop="updateTime"
          width="180"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 9"
          :key="index"
          :label="item.v"
          prop="opration"
          align="center"
          fixed="right"
          width="160"
        >
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="formSubmitEdit(row)"> 编辑 </el-button>
            <el-button
              type="danger"
              v-if="row.status === '1'"
              size="small"
              @click="formSubmitStatus(row.id, 2)"
            >
              历史
            </el-button>
            <el-button
              type="success"
              v-if="row.status === '2'"
              size="small"
              @click="formSubmitStatus(row.id, 1)"
            >
              显示
            </el-button>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <Paging class="mt-15" @change="handlePaginationChange" :total="pagination.total" />
  </div>
  <el-dialog width="65%" :title="title" v-model="visible" @close="cancelButton" top="5vh">
    <el-form ref="formVm" :model="formData" :rules="formRules" class="form-data">
      <el-form-item label="标题:" :label-width="formLabelWidth" prop="name">
        <el-input
          v-model="formData.name"
          class="select-width"
          placeholder="请输入协议标题"
          type="text"
        />
      </el-form-item>
      <el-form-item label="类型:" :label-width="formLabelWidth" prop="type">
        <el-select v-model="formData.type" class="select-width" clearable="true">
          <el-option
            v-for="item in selectData.typeList"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="版本号:" :label-width="formLabelWidth" prop="version">
        <el-input
          v-model="formData.version"
          class="select-width"
          placeholder="请输入版本号；例如：1.0、1.0.1、*******"
          type="text"
        />
      </el-form-item>
      <el-form-item label="状态:" :label-width="formLabelWidth" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio v-for="item in selectData.statusList" :key="item.k" :label="item.k">{{
            item.v
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="描述:" :label-width="formLabelWidth" prop="description">
        <el-input
          v-model="formData.description"
          class="select-width"
          placeholder="请输入协议描述"
          type="textarea"
        />
      </el-form-item>
      <el-form-item label="内容:" :label-width="formLabelWidth" prop="content">
        <WangEditor ref="editorRef" v-model="formData.content" showCustomUploadFile />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmButton">确认</el-button>
        <el-button @click="cancelButton"> 取消 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts">
import { reactive, toRefs, ref } from 'vue'
import Paging from '/@/components/base/paging.vue'
import {
  agreementIndex,
  agreementAdd,
  agreementEdit,
  agreementFilter,
  agreementEditInit,
  agreementStatus
} from '/@/api/agreementConfig'
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import WangEditor from '/@/components/wangEditor/index.vue'

export default {
  name: 'agreementConfig',
  components: {
    InputAutocomplete,
    Paging,
    WangEditor
  },
  setup() {
    const formVmSearch = ref()
    const formVm = ref()
    const editorRef = ref()
    const listHeader = ref([
      {
        k: 1,
        v: '协议ID',
        name: 'id',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '协议标题',
        name: 'name',
        select: true,
        default: true
      },
      {
        k: 3,
        v: '协议类型',
        name: 'typeText',
        select: true,
        default: true
      },
      {
        k: 4,
        v: '协议状态',
        name: 'statusText',
        select: true,
        default: false
      },
      {
        k: 5,
        v: '版本号',
        name: 'version',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '描述',
        name: 'description',
        select: true,
        default: true
      },
      {
        k: 7,
        v: '添加时间',
        name: 'addTime',
        select: true,
        default: true
      },
      {
        k: 8,
        v: '更新时间',
        name: 'updateTime',
        select: true,
        default: true
      },
      {
        k: 9,
        v: '操作',
        name: 'opration',
        select: true,
        default: true
      }
    ])
    const state = reactive({
      formLabelWidth: '120px',
      loading: false,
      visible: false,
      title: '',
      list: [],
      selectData: {
        statusList: [],
        typeList: []
      },
      pagination: {
        total: 0,
        pageSize: 20,
        page: 1
      },
      formDataSearch: {
        id: '',
        name: '',
        status: '',
        type: ''
      },
      submitType: 0,
      rowId: 0,
      rowStatus: '',
      formData: {
        name: '',
        content: '',
        status: '',
        version: '',
        type: '',
        description: ''
      }
    })

    /**
     * 列表
     * @returns {Promise<void>}
     */
    const getList = async () => {
      state.loading = true
      const { data, pages } = await agreementIndex(state.formDataSearch)
      state.list = data
      state.pagination.total = pages.total
      state.loading = false
    }

    /**
     * 获取过滤列表
     */
    const getFilter = async () => {
      const { typeList, statusList } = await agreementFilter()
      state.selectData.typeList = typeList
      state.selectData.statusList = statusList
    }

    /**
     * 初始化调用
     */
    getList()
    getFilter()

    /**
     * 分页
     * @param data
     */
    const handlePaginationChange = (data) => {
      state.formDataSearch.page = data.page
      state.formDataSearch.pageSize = data.limit
      getList()
    }

    /**
     * 重置搜索表单
     */
    const reset = () => {
      formVmSearch.value.resetFields()
      getList()
    }

    /**
     * 添加
     */
    const formSubmitAdd = () => {
      state.title = '新增'
      state.submitType = 1
      state.visible = true
    }

    /**
     * 编辑初始化
     * @param row
     */
    const formSubmitEdit = (row) => {
      state.title = '编辑'
      state.rowId = row.id
      state.rowStatus = row.status
      state.submitType = 2
      // 初始化
      agreementEditInit({ id: row.id }).then((res) => {
        state.formData.name = res.name
        state.formData.status = res.status
        state.formData.type = res.type
        state.formData.version = res.version
        editorRef.value.updateEditor(res.content)
        state.formData.description = res.description
      })
      state.visible = true
    }

    /**
     * 确认提交表单
     */
    const confirmButton = () => {
      formVm.value.validate((validate_result, data) => {
        if (!validate_result) {
          return
        }
        if (state.submitType === 1) {
          agreementAdd(state.formData).then((resp) => {
            state.visible = false
            editorRef.value.clearEditor()
            formVm.value.resetFields()
            getList()
          })
        } else if (state.submitType === 2) {
          state.formData.id = state.rowId
          agreementEdit(state.formData).then((resp) => {
            state.visible = false
            editorRef.value.clearEditor()
            formVm.value.resetFields()
            getList()
          })
        }
      })
    }

    /**
     * 取消提交表单
     */
    const cancelButton = () => {
      editorRef.value.clearEditor()
      formVm.value.resetFields()
      state.visible = false
    }

    /**
     * 验证表单
     * @type {Ref<UnwrapRef<{personActiveDayNumber: [{trigger: string[], message: string, required: boolean}], invitePersonResumeIds: [{trigger: string[], message: string, required: boolean}], inviteDeliveryWay: [{required: boolean}], inviteSelectText: [{trigger: string[], message: string, required: boolean}], inviteSelect: [{required: boolean}], inviteNumber: [{trigger: string[], message: string, required: boolean}], inviteTime: [{trigger: string[], message: string, required: boolean}]}>>}
     */
    const formRules = ref({
      name: [{ required: true, message: '协议标题不允许为空', trigger: ['blur', 'change'] }],
      type: [{ required: true, message: '请选择协议类型', trigger: ['blur', 'change'] }],
      status: [{ required: true, message: '请选择协议状态', trigger: ['blur', 'change'] }],
      content: [{ required: true, message: '协议内容不允许为空', trigger: ['blur', 'change'] }],
      version: [{ required: true, message: '版本号不允许为空', trigger: ['blur', 'change'] }]
    })

    /**
     * 状态按钮
     */
    const formSubmitStatus = (id, status) => {
      let title = ''
      let msg = ''
      if (status === 1) {
        title = '显示协议操作'
        msg = `确定将ID为：${id}显示吗？`
      } else if (status === 2) {
        title = '历史版本操作'
        msg = `确定将ID为：${id}下线吗？`
      } else {
        return
      }
      ElMessageBox.confirm(msg, title, {
        cancelButtonText: '取消',
        confirmButtonText: '确认',
        type: 'warning'
      })
        .then(() => {
          title = `${title}(二次确认)`
          msg = `再次确认，${msg}`
          ElMessageBox.confirm(msg, title, {
            cancelButtonText: '取消',
            confirmButtonText: '确认',
            type: 'warning'
          })
            .then(() => {
              agreementStatus({ id, status }).then((resp) => {
                getList()
              })
            })
            .catch(() => {
              ElMessage({
                type: 'info',
                message: '操作取消'
              })
            })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '操作取消'
          })
        })
    }

    /**
     * 产品类型改变
     */
    const agreementCategoryIdChange = () => {
      if (state.formData.agreementCategoryId === '1') {
        state.formData.equityIds = ['1', '2', '3', '4', '5', '6', '7', '8']
      } else if (state.formData.agreementCategoryId === '2') {
        state.formData.equityIds = ['1', '2']
      } else if (state.formData.agreementCategoryId === '3') {
        state.formData.equityIds = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11']
      } else if (state.formData.agreementCategoryId === '4') {
        state.formData.equityIds = ['9', '10', '11']
      } else {
        state.formData.equityIds = []
      }
    }

    return {
      ...toRefs(state),
      getList,
      formVmSearch,
      formVm,
      listHeader,
      handlePaginationChange,
      reset,
      editorRef,
      formRules,
      formSubmitAdd,
      formSubmitEdit,
      confirmButton,
      cancelButton,
      formSubmitStatus,
      agreementCategoryIdChange
    }
  }
}
</script>

<style scoped>
.main {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.el-select .el-input {
  width: 130px;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

.ml20 {
  margin-left: 20px;
}

.btn-group {
  margin: 15px 0;
}

.select-width {
  width: 50% !important;
}

.radio-width {
  width: 40px !important;
}

.select-width-100 {
  width: 100%;
}

.tip-color {
  font-weight: bold;
  color: #ff4d4f;
}

.tag {
  font-weight: bold;
}

.btn-search-group {
  width: 100%;
  justify-content: right;
  padding-right: 40px;
}
</style>
