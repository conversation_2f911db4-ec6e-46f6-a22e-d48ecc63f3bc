<template>
  <div class="detail-main">
    <el-dialog v-model="dialogVisible" title="套餐配置" width="30%" @close="close">
      <div class="flex mb-20">手机号：{{ detailData.mobile }}</div>

      <div class="flex ai-center mb-20">
        <div class="mr-10">姓名</div>
        <el-input style="width: 200px" :disabled="audit" v-model="detailData.name" />
      </div>

      <div class="flex mb-20">订单号：{{ detailData.tradeNo }}</div>
      <div class="flex mb-20">订单金额：{{ detailData.realAmount }}</div>
      <div class="flex mb-20">产品名称：{{ detailData.equityPackageName }}</div>
      <div class="flex mb-20">
        开通凭证：
        <div class="flex-column">
          <div
            class="file"
            v-for="item in detailData.settingFileList"
            :key="item.id"
            @click="handlePriview(item.fullUrl)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>

      <div class="flex mb-20">
        备注：
        <el-input
          style="width: 500px"
          type="textarea"
          show-word-limit
          maxlength="200"
          :disabled="audit"
          v-model="detailData.settingRemark"
        />
      </div>

      <div class="flex mb-20">
        <el-button type="primary" @click="handleConfirm">{{ confirmText }}</el-button>
        <el-button @click="handleReject">{{ cancelText }}</el-button>
      </div>
    </el-dialog>

    <el-image-viewer
      v-if="licenseShow"
      :url-list="imageList"
      :initial-index="currentImageIndex"
      @close="licenseShow = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { getMealDetail, mealViewEdit, mealAudit } from '/@/api/configuration'
import { ElMessageBox } from 'element-plus'

const emits = defineEmits(['update:modelValue', 'update'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },

  id: {
    type: String,
    default: null
  },

  audit: {
    type: Boolean,
    default: false
  }
})

const detailData = <any>ref({})

const confirmText = computed(() => (props.audit ? '审核通过' : '确定'))
const cancelText = computed(() => (props.audit ? '审核拒绝' : '取消'))

const id = <any>computed(() => props.id)

const getDetail = async () => {
  detailData.value = await getMealDetail({ id: props.id })
}

const dialogVisible = computed({
  get() {
    if (props.modelValue && id.value) {
      getDetail()
    }
    return props.modelValue
  },
  set(val: boolean) {
    emits('update:modelValue', val)
  }
})

const priviewUrl = ref('')

const licenseShow = ref(false)

const currentImageIndex = ref(0)

const imageList = computed(
  () => detailData.value.settingFileList?.map((item) => item.fullUrl) || []
)

const close = () => {
  id.value = null
  dialogVisible.value = false
}

const handleReject = () => {
  if (props.audit) {
    ElMessageBox.prompt('', '请录入驳回原因', {
      inputPattern: /^(.+)$/,
      inputErrorMessage: '请录入驳回原因'
    })
      .then(async ({ value }) => {
        await mealAudit({ id: id.value, status: 4, settingRejectRemark: value })
        id.value = null
        dialogVisible.value = false
        emits('update')
      })
      .catch(() => {})
    return
  }

  close()
}

const handlePriview = (url: string) => {
  priviewUrl.value = url
  currentImageIndex.value = imageList.value.indexOf(url)
  licenseShow.value = true
}

const handleConfirm = async () => {
  const api = props.audit ? mealAudit : mealViewEdit
  const postData = props.audit
    ? { id: id.value, status: 3 }
    : { id: id.value, name: detailData.value.name, settingRemark: detailData.value.settingRemark }

  await api(postData)
  emits('update')

  close()
}
</script>

<style lang="scss" scoped>
.file {
  cursor: pointer;
  margin-bottom: 10px;
  color: #409eff;
}
</style>
