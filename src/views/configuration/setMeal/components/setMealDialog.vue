<template>
  <div class="dialog-main">
    <el-dialog title="套餐配置" v-model="dialogVisible" width="40%" @close="cancel">
      <div class="color-danger mb-10" v-if="settingRejectRemark">
        审核驳回原因： {{ settingRejectRemark }}
      </div>
      <el-form :model="formData" label-width="80px" :rules="formRules" ref="formRef">
        <el-form-item label="手机号" prop="mobileCode" :rules="mobileRules">
          <mobilePhoneCode v-model="mobileData" />
          <div class="tips" v-if="mealTips !== ''">{{ mealTips }}</div>
        </el-form-item>

        <el-form-item label="姓名" prop="name">
          <el-input
            placeholder="请输入用户姓名"
            v-model="formData.name"
            maxlength="32"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="订单号" prop="tradeNo">
          <el-input
            placeholder="请输入用户的订单号"
            v-model="formData.tradeNo"
            maxlength="60"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="订单金额" prop="realAmount">
          <el-input
            placeholder="请输入订单金额"
            @input="validateMount"
            v-model="formData.realAmount"
          />
        </el-form-item>

        <el-form-item label="产品名称" prop="equityPackageId">
          <el-select placeholder="请选择产品名称" v-model="formData.equityPackageId">
            <el-option v-for="item in productList" :key="item.k" :label="item.v" :value="item.k">{{
              item.v
            }}</el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="开通凭证" prop="settingFileIds">
          <el-upload
            class="upload-demo"
            action="/upload/image"
            :file-list="fileList"
            :limit="5"
            :show-file-list="false"
            :on-success="handleUploadSuccess"
          >
            <el-button type="primary">+ 上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                请上传开通凭证，如后台订单截图或沟通确认记录等；支持jpg、png、jpeg格式
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <div class="file-list">
          <div class="file" v-for="item in fileList" :key="item.id">
            <div @click="handlePriview(item.fullUrl)">{{ item.name }}</div>
            <span @click="handleUploadRemove(item.id)">X</span>
          </div>
        </div>

        <el-form-item label="备注" prop="settingRemark">
          <el-input
            type="textarea"
            show-word-limit
            maxlength="200"
            v-model="formData.settingRemark"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSumbit">确定</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-image-viewer
      v-if="licenseShow"
      :url-list="imageList"
      :initial-index="currentImageIndex"
      @close="licenseShow = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import {
  getSetMealParamsList,
  checkMobile,
  getMealDetail,
  addMeal,
  editMeal
} from '/@/api/configuration'
import mobilePhoneCode from '/@select/mobile.vue'
import { verifyNumberIntegerAndFloat, verifyPhone } from '/@/utils/toolsValidate'
import { ElMessageBox } from 'element-plus'

const emits = defineEmits(['update:modelValue', 'update'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },

  id: {
    type: String,
    default: null
  }
})

const licenseShow = ref(false)

const formRef = ref()

const productList = ref([{ k: '', v: '' }])

const fileList = <any>ref([])

const setMealText = ref('')

const settingRejectRemark = ref(null || '')

const mealTips = ref(null || '')

const priviewUrl = ref('')

const currentImageIndex = ref(0)

const imageList = computed(() => fileList.value.map((item) => item.fullUrl))

const mobileData = ref({
  mobile: '',
  mobileCode: '+86'
})

const id = <any>computed(() => props.id)

const fileIds = computed(() => fileList.value.map((item: any) => item.id).join())

const formData = <any>ref({
  mobile: computed(() => mobileData.value.mobile),
  mobileCode: computed(() => mobileData.value.mobileCode),
  name: '',
  tradeNo: '',
  realAmount: '',
  equityPackageId: '',
  settingFileIds: computed(() => fileIds.value),
  settingRemark: '',
  id: computed(() => id.value)
})

const productText = computed(
  () => productList.value.find((item) => item.k === formData.value.equityPackageId)?.v
)

const formRules = ref({
  tradeNo: [{ required: true, message: '请输入用户的订单号', trigger: 'blur' }],
  realAmount: [{ required: true, message: '请输入订单金额', trigger: 'blur' }],
  equityPackageId: [{ required: true, message: '请选择产品名称', trigger: 'change' }],
  settingFileIds: [{ required: true, message: '请上传开通凭证', trigger: 'change' }]
})

const mobileValidate = async (rule, value, callback) => {
  mealTips.value = ''

  const { mobile, mobileCode } = mobileData.value
  if (mobile === '') {
    callback('请输入手机号码')
    return
  }

  if (mobileCode === '+86' && !verifyPhone(mobile)) {
    callback('请输入正确的手机号')
    return
  }

  const { checkResult, checkText } = await checkMobile(mobileData.value)

  if (!checkResult) {
    setMealText.value = checkText
    callback(checkText)
  } else {
    mealTips.value = checkText
  }

  callback()
}

const mobileRules = ref({
  required: true,
  validator: mobileValidate,
  trigger: 'blur'
})

const getDetail = async () => {
  const resp = await getMealDetail({ id: id.value })
  formData.value.equityPackageId = resp.equityPackageId
  formData.value.name = resp.name
  formData.value.realAmount = resp.realAmount
  formData.value.tradeNo = resp.tradeNo
  formData.value.settingRemark = resp.settingRemark
  settingRejectRemark.value = resp.settingRejectRemark
  fileList.value = resp.settingFileList
  mobileData.value.mobile = resp.mobile
  mobileData.value.mobileCode = resp.mobileCode
}

const dialogVisible = computed({
  get() {
    if (props.modelValue && id.value) {
      getDetail()
    }
    return props.modelValue
  },
  set(val: boolean) {
    emits('update:modelValue', val)
  }
})

const cancel = () => {
  formRef.value.resetFields()
  mobileData.value = { mobile: '', mobileCode: '+86' }
  fileList.value = []
  mealTips.value = ''
  settingRejectRemark.value = ''
  dialogVisible.value = false
}

const handleSumbit = () => {
  formRef.value.validate().then(async () => {
    const content = `
    <div>配置成功后将不可撤销，确定要为用户${mobileData.value.mobile}配置${productText.value}套餐吗？</div>
    <div style="font-size: 12px; color:#f56c6c;">${mealTips.value}</div>
    `
    ElMessageBox.confirm(content, '提示', {
      dangerouslyUseHTMLString: true
    })
      .then(async () => {
        const api = id.value ? editMeal : addMeal
        await api(formData.value)
        emits('update')
        cancel()
      })
      .catch(() => {})
  })
}

const handleUploadRemove = (uid: string) => {
  fileList.value = fileList.value.filter((item: any) => item.id !== uid)
}

const handleUploadSuccess = (res) => {
  fileList.value = [
    { name: res.data.name, id: res.data.id, fullUrl: res.data.fullUrl },
    ...fileList.value
  ]
}

const handlePriview = (url: string) => {
  priviewUrl.value = url
  currentImageIndex.value = imageList.value.indexOf(url)
  licenseShow.value = true
}

const getParamsList = async () => {
  const { resumeEquityPackage } = await getSetMealParamsList()

  productList.value = resumeEquityPackage
}

const validateMount = () => {
  formData.value.realAmount = verifyNumberIntegerAndFloat(formData.value.realAmount)
}

getParamsList()
</script>

<style lang="scss" scoped>
.file-list {
  padding: 0 80px;
  .file {
    display: flex;
    justify-content: space-between;
    text-decoration: none;
    cursor: pointer;
    margin-bottom: 10px;
    color: #409eff;
    span {
      cursor: pointer;
    }
  }
}

.tips {
  color: var(--el-color-danger);
  font-size: 12px;
}
</style>
