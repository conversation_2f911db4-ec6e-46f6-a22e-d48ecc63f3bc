<template>
  <div>
    <el-dialog title="资源配置" v-model="visible" width="650px" @close="handleClose">
      <el-form
        class="pt-10 pr-100 mr-20"
        :model="formData"
        :rules="formRules"
        ref="form"
        label-width="110px"
      >
        <el-form-item label="单位信息：" prop="companyId">
          <div class="ai-center p-relative">
            <InputAutocomplete
              :value="companyInfo.fullName"
              value-key="fullName"
              class="flex-1"
              @change="handleCompanyIdChange"
              @select="handleCompanyIdSelect"
              placeholder="请填写单位ID或名称"
            >
              <template #default="{ row }">
                <div>{{ row.fullName }}</div>
              </template>
            </InputAutocomplete>
            <div class="right p-absolute opacity-80 fs-13">{{ companyInfo.packageTypeName }}</div>
          </div>
        </el-form-item>

        <el-form-item label="配置权益：" prop="equityType">
          <el-select v-model="formData.equityType" @change="handleEquityChange">
            <el-option v-for="item in otherList" :key="item.k" :label="item.v" :value="item.k">{{
              item.v
            }}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型：" prop="type">
          <div class="ai-center p-relative">
            <el-radio-group v-model="formData.type" class="ml-4">
              <el-radio label="1">增加{{ hasSms }}数</el-radio>
              <el-radio label="2">扣减{{ hasSms }}数</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>

        <el-form-item label="现有数量" prop="nowResumeDownloadAmount">
          <div class="ai-center p-relative">
            <!-- <el-input
              v-model="formData.nowResumeDownloadAmount"
              disabled
              disabledplaceholder="现有数量"
            ></el-input> -->
            <span>&nbsp;&nbsp;{{ nowAmount }}</span>
            <div class="right p-absolute opacity-80 fs-13">{{ hasSms }}</div>
          </div>
        </el-form-item>

        <el-form-item label="计算后数量">
          <div class="ai-center p-relative">
            <!-- <el-input
              v-model="formData.nowResumeDownloadAmount"
              disabled
              disabledplaceholder="现有数量"
            ></el-input> -->
            <span v-if="formData.type == 1"
              >&nbsp;&nbsp;{{ nowAmount * 1 + formData.num * 1 }}</span
            >
            <span v-if="formData.type == 2"
              >&nbsp;&nbsp;{{ nowAmount * 1 - formData.num * 1 }}</span
            >
            <span v-if="!formData.type">&nbsp;&nbsp;{{ nowAmount }}</span>
            <div class="right p-absolute opacity-80 fs-13">{{ hasSms }}</div>
          </div>
        </el-form-item>

        <el-form-item label="数量：" prop="num">
          <div class="ai-center p-relative">
            <el-input v-model="formData.num" placeholder="数量" filterable clearable></el-input>
            <div class="right p-absolute opacity-80 fs-13">{{ hasSms }}</div>
          </div>
        </el-form-item>

        <el-form-item label="备注：" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            rows="4"
            resize="none"
            placeholder="备注"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleComfirm">确定</el-button>
          <el-button @click="handleClose">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, toRefs, reactive, ref, onMounted, computed, nextTick } from 'vue'
// import DatePicker from '/@/components/base/datePicker.vue'
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'

import {
  getCompanyList,
  getCompanyPackageList,
  setResumeDownloadNum,
  getOtherPackageList
} from '/@/api/configuration'
import { ElMessageBox } from 'element-plus'

export default defineComponent({
  name: 'packageResumeDownDialog',
  components: { /* DatePicker, */ InputAutocomplete },
  emits: ['update'],
  setup(props, { emit }) {
    const form = ref()
    const state = reactive({
      visible: false,
      confirmVisible: false,
      loading: false,
      formData: {
        companyId: '',
        type: '',
        num: 0,
        equityType: '1',
        nowResumeDownloadAmount: 0,
        remark: ''
      },
      // 权益包数
      packageAmount: computed(() => {
        return state.formData.packageAmount > 0 ? state.formData.packageAmount : 1
      }),
      // 权益包列表
      packageList: [],
      // 试用会员明细
      trialInfo: {},
      otherList: [],
      nowAmount: '0',
      // 权益包某套餐信息
      packageInfo: computed(() => {
        const [data = {}] = state.packageList.filter(
          (item: any) => item.id === state.formData.packageId
        )
        const { detail = {} } = data
        return detail
      }),
      // 单位信息
      companyInfo: {
        fullName: ''
      },
      reviewPackageInfo: computed(() => {
        const { packageInfo, trialInfo } = state
        const { isSenior } = state.formData
        return /1/.test(isSenior) ? packageInfo : trialInfo
      }),
      chatAmount: computed(() =>
        state.companyInfo.package?.chatAmount ? state.companyInfo.package?.chatAmount : 0
      ),
      smsAmount: computed(() =>
        state.companyInfo.package?.smsAmount ? state.companyInfo.package?.smsAmount : 0
      ),
      hasSms: computed(() => (Number(state.formData.equityType) === 3 ? '条' : '点'))
    })

    const validateCompanyId = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请填写单位'))
        return
      }
      const { packageType }: any = state.companyInfo
      /**
       * packageType 1 免费会员、2 高级会员、3过期会员、4 试用会员
       */
      const notConfiguration = [1, 3]
      if (notConfiguration.includes(packageType)) {
        callback(new Error('必须有生效中套餐才能配置'))
        return
      }
      if (value === -1) {
        callback(new Error('该单位不存在，请重新选择'))
      } else {
        callback()
      }
    }

    const formRules = ref({
      companyId: [{ validator: validateCompanyId, trigger: 'blur' }],
      type: [{ required: true, message: '请选择类型', trigger: 'change' }],
      remark: [{ required: true, message: '必须填写备注', trigger: ['blur', 'change'] }],
      num: [
        {
          required: true,
          pattern: /^[1-9]\d*$/,
          message: '请填写正整数',
          trigger: ['blur', 'change']
        }
      ]
    })

    const getPackageList = async () => {
      getCompanyPackageList().then((resp: any) => {
        const [packageList = [], trial = []] = resp
        const [trialInfo = {}] = trial
        const { detail } = trialInfo
        state.packageList = packageList
        state.trialInfo = detail
      })
      state.otherList = await getOtherPackageList()
      // state.otherList = [{"k":"1","v":"简历下载点数"},{"k":"2","v":"直聊点数"},{"k":"3","v":"短信条数"}]
    }

    onMounted(() => {
      getPackageList()
    })

    const open = () => {
      state.visible = true
      state.companyInfo = {
        fullName: ''
      }
      nextTick(() => {
        form.value.resetFields()
        nextTick(() => {
          form.value.clearValidate()
        })
      })
    }

    const handleDisabledDate = (time: any) => {
      return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
    }

    const handleEquityChange = (val: string) => {
      let res = 0
      if (val === '1') {
        res = state.formData.nowResumeDownloadAmount
      } else if (val === '2') {
        res = state.chatAmount
      } else {
        res = state.smsAmount
      }
      state.nowAmount = res
    }

    // 单位ID模糊搜索 start
    const handleCompanyIdChange = (kw: any, callback) => {
      state.formData.companyId = -1
      state.companyInfo.fullName = kw
      getCompanyList({ name: kw }).then((resp: any) => {
        callback(resp)
        if (!state.companyInfo.fullName.includes(kw) && !resp.length) {
          state.formData.companyId = -1
          setTimeout(() => {
            form.value.validateField('companyId', () => {})
          }, 210)
        }
      })
    }
    const handleCompanyIdSelect = (val: any) => {
      if (val) {
        state.companyInfo = val
        state.formData.companyId = val.id
        if (val.package) {
          state.formData.nowResumeDownloadAmount = val.package.resumeDownloadAmount ?? 0
        } else {
          state.formData.nowResumeDownloadAmount = 0
        }
      }
      form.value.validateField('companyId', () => {
        handleEquityChange(state.formData.equityType)
      })
    }
    // 单位ID模糊搜索 end

    const handlePackageDetail = (baseAmount, amount) => {
      const { isSenior, packageAmount } = state.formData
      const str = /1/.test(isSenior) ? `${baseAmount}+${amount}*${packageAmount}` : amount
      return str
    }

    const handleClose = () => {
      state.visible = false
      state.confirmVisible = false
      state.nowAmount = '0'
    }

    const submit = () => {
      state.loading = true
      /**
       * isSenior 1 高级会员，2试用会员
       */
      const { companyId, num, type, remark, equityType } = state.formData
      const formData: any = { companyId, num, type, remark, equityType }

      setResumeDownloadNum(formData)
        .then(() => {
          state.loading = false
          handleClose()
          emit('update')
        })
        .catch(() => {
          state.loading = false
        })
    }

    const handleComfirm = () => {
      form.value.validate((valid: boolean) => {
        console.log('valid', valid)

        if (!valid) return
        // 出个提示弹窗
        let title = ''
        let total = 0
        const textArr = [
          {
            text1: '简历下载',
            text2: '点'
          },
          {
            text1: '直聊',
            text2: '点'
          },
          {
            text1: '短信条数',
            text2: '条'
          }
        ]
        const type = Number(state.formData.type)
        const computedTypeText = type === 1 ? '增加' : '减少'
        const equityType = Number(state.formData.equityType)
        const { text1, text2 } = textArr[equityType - 1]
        if (type === 1) {
          total = state.formData.num * 1 + state.nowAmount * 1
        } else {
          total = state.nowAmount * 1 - state.formData.num * 1
        }

        title = `确定为 ${state.companyInfo.fullName} ${computedTypeText}${text1}${text2}数${state.formData.num}${text2}吗？${computedTypeText}后总${text2}数为${total}${text2}`
        if (total < 0) {
          return
        }
        ElMessageBox.confirm(title, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            submit()
          })
          .catch(() => {})
      })
    }

    return {
      form,
      open,
      handleDisabledDate,
      handleCompanyIdChange,
      handleCompanyIdSelect,
      handleComfirm,
      handleEquityChange,
      submit,
      handleClose,
      formRules,
      handlePackageDetail,
      ...toRefs(state)
    } as any
  }
})
</script>

<style scoped lang="scss">
.right {
  left: calc(100% + 10px);
  white-space: nowrap;
}
</style>
