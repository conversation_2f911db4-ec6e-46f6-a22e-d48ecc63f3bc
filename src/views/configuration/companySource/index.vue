<template>
  <div class="main">
    <el-form ref="formVmSearch" :model="formDataSearch" label-width="70px">
      <div class="flex">
        <el-col :span="4">
          <el-form-item label="单位ID" prop="companyId">
            <el-input v-model="formDataSearch.companyId" clearable placeholder="请输入单位ID" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="单位名称" prop="companyName">
            <el-input
              v-model="formDataSearch.companyName"
              clearable
              placeholder="模糊搜索单位名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="操作人" prop="adminName">
            <el-input v-model="formDataSearch.adminName" clearable placeholder="模糊搜索运营人员" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="周期类型" prop="type">
            <el-select v-model="formDataSearch.type" clearable placeholder="请选择周期类型">
              <el-option label="日" value="1" />
              <el-option label="周" value="2" />
              <el-option label="月" value="3" />
              <el-option label="年" value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="状态" prop="deleteStatus">
            <el-select v-model="formDataSearch.deleteStatus" clearable placeholder="请选择状态">
              <el-option label="删除" value="1" />
              <el-option label="正常" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="添加时间" prop="addTimeStart">
            <DatePickerRange
              v-model:start="formDataSearch.addTimeStart"
              v-model:end="formDataSearch.addTimeEnd"
            />
          </el-form-item>
        </el-col>
      </div>
      <div class="flex">
        <el-col :span="4">
          <el-form-item label="更新时间" prop="updateTimeStart">
            <DatePickerRange
              v-model:start="formDataSearch.updateTimeStart"
              v-model:end="formDataSearch.updateTimeEnd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" style="line-height: 100%">
          <div class="nowrap ml-15 btn-search-group">
            <el-button type="primary" @click="getList">搜索</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </el-col>
      </div>
    </el-form>
    <div class="btn-group">
      <el-button type="primary" class="el-icon-plus" @click="formSubmitAdd()">
        新增单位邀约资源配置</el-button
      >
    </div>
    <el-table :data="list" border v-loading="loading" ref="companySourceTable">
      <template v-for="(item, index) in listHeader">
        <el-table-column
          v-if="item.select && item.k === 1"
          :key="index"
          :label="item.v"
          prop="companyId"
          align="center"
          width="100"
        />

        <el-table-column
          v-if="item.select && item.k === 2"
          :key="index"
          :label="item.v"
          prop="companyName"
          align="center"
          show-overflow-tooltip
        />

        <el-table-column
          v-if="item.select && item.k === 3"
          :key="index"
          :label="item.v"
          prop="typeText"
          align="center"
          width="100"
        />
        <el-table-column
          v-if="item.select && item.k === 4"
          :key="index"
          :label="item.v"
          prop="inviteNumber"
          width="160"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 5"
          :key="index"
          :label="item.v"
          prop="adminName"
          align="center"
          width="100"
        />
        <el-table-column
          v-if="item.select && item.k === 6"
          :key="index"
          :label="item.v"
          prop="addTime"
          width="220px"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 7"
          :key="index"
          :label="item.v"
          prop="updateTime"
          width="220px"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 8"
          :key="index"
          :label="item.v"
          prop="deleteText"
          width="100"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 9"
          :key="index"
          :label="item.v"
          prop="opration"
          align="center"
          width="180px"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              :disabled="row.isDelete === '1'"
              size="small"
              @click="formSubmitEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              :disabled="row.isDelete === '1'"
              size="small"
              @click="formSubmitDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <Paging class="mt-15" @change="handlePaginationChange" :total="pagination.total" />
  </div>
  <el-dialog width="40%" :title="title" v-model="visible" @close="cancelButton">
    <el-form ref="formVm" :model="formData" class="form-data">
      <el-form-item prop="companyId" label="单位ID:" :label-width="formLabelWidth">
        <InputAutocomplete
          :value="companyInfo.fullName"
          value-key="fullName"
          style="width: 80%"
          @change="handleCompanyIdChange"
          @select="handleCompanyIdSelect"
          placeholder="请填写单位ID或名称"
        >
          <template #default="{ row }" class="select-width">
            <div>{{ row.fullName }}</div>
          </template>
        </InputAutocomplete>
      </el-form-item>
      <el-form-item
        class="invite-select"
        prop="type"
        label="周期类型:"
        :label-width="formLabelWidth"
      >
        <el-select v-model="formData.type" placeholder="请选择周期类型" class="select-width">
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="周期邀约次数:" :label-width="formLabelWidth" prop="inviteNumber">
        <el-input v-model="formData.inviteNumber" class="select-width" type="text" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmButton">确认</el-button>
        <el-button @click="cancelButton"> 取消 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, ref } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { indexSource, editSource, addSource, deleteSource } from '../../../api/companySource'
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'
import { getCompanyList } from '../../../api/configuration'

export default {
  name: 'companySource',
  components: {
    DatePickerRange,
    InputAutocomplete,
    Paging
  },
  setup() {
    const formVmSearch = ref()
    const formVm = ref()
    const listHeader = ref([
      {
        k: 1,
        v: '单位ID',
        name: 'companyId',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '单位名称',
        name: 'companyName',
        select: true,
        default: true
      },
      {
        k: 3,
        v: '周期类型',
        name: 'typeText',
        select: true,
        default: true
      },
      {
        k: 4,
        v: '周期邀约次数',
        name: 'inviteNumber',
        select: true,
        default: false
      },
      {
        k: 5,
        v: '操作人',
        name: 'adminText',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '添加时间',
        name: 'addTime',
        select: true,
        default: false
      },
      {
        k: 7,
        v: '更新时间',
        name: 'updateTime',
        select: true,
        default: true
      },
      {
        k: 8,
        v: '状态',
        name: 'deleteText',
        select: true,
        default: true
      },
      {
        k: 9,
        v: '操作',
        name: 'opration',
        select: true,
        default: true
      }
    ])
    const state = reactive({
      formLabelWidth: '120px',
      typeList: [
        {
          label: '日',
          value: '1'
        },
        {
          label: '周',
          value: '2'
        },
        {
          label: '月',
          value: '3'
        },
        {
          label: '年',
          value: '4'
        }
      ],
      loading: false,
      visible: false,
      title: '',
      list: [],
      companyInfo: {
        fullName: ''
      },
      pagination: {
        total: 0,
        pageSize: 20,
        page: 1
      },
      formDataSearch: {
        companyId: '',
        companyName: '',
        adminName: '',
        type: '',
        deleteStatus: '',
        addTimeStart: '',
        addTimeEnd: '',
        updateTimeStart: '',
        updateTimeEnd: ''
      },
      submitType: 0,
      rowId: 0,
      formData: {
        companyId: '',
        type: '',
        inviteNumber: ''
      }
    })

    const getList = async () => {
      state.loading = true
      const { list, pages } = await indexSource(state.formDataSearch)
      state.list = list
      state.pagination.total = pages.total
      state.loading = false
    }

    getList()

    const handlePaginationChange = (data) => {
      state.formDataSearch.page = data.page
      state.formDataSearch.pageSize = data.limit
      getList()
    }

    const reset = () => {
      formVmSearch.value.resetFields()
      state.formDataSearch.addTimeEnd = ''
      state.formDataSearch.addTimeStart = ''
      state.formDataSearch.updateTimeEnd = ''
      state.formDataSearch.updateTimeStart = ''
      getList()
    }

    // 重置表单数据
    const resetVm = () => {
      state.companyInfo.fullName = ''
      state.formData.companyId = ''
      state.formData.type = ''
      state.formData.inviteNumber = ''
    }

    // 关闭弹窗，并且数据初始化
    const cancelButton = () => {
      resetVm()
      state.visible = false
    }

    const confirmButton = () => {
      state.loading = true
      if (state.submitType === 1) {
        addSource(state.formData)
          .then((resp) => {
            state.visible = false
            resetVm()
            getList()
          })
          .catch(() => {
            state.loading = false
          })
      } else if (state.submitType === 2) {
        const editFormData = state.formData
        editFormData.id = state.rowId
        editSource(editFormData)
          .then((resp) => {
            state.visible = false
            resetVm()
            getList()
          })
          .catch(() => {
            state.loading = false
          })
      }
    }

    const formSubmitAdd = () => {
      state.title = '新增'
      state.submitType = 1
      resetVm()
      state.visible = true
    }

    const formSubmitEdit = (row) => {
      state.title = '编辑'
      state.rowId = row.id
      state.formData.companyId = row.oldCompanyId
      state.formData.inviteNumber = row.inviteNumber
      state.formData.type = row.type
      state.companyInfo.fullName = row.companyName
      state.submitType = 2
      state.visible = true
    }

    const formSubmitDelete = (row) => {
      ElMessageBox.confirm(
        `确认删除单位ID:${row.companyId}的邀约次数配置吗？删除后将立即采用全局配置。`,
        '删除',
        {
          cancelButtonText: '取消',
          confirmButtonText: '确认',
          type: 'warning'
        }
      )
        .then(() => {
          deleteSource({ id: row.id }).then((resp) => {
            getList()
          })
          ElMessage({
            type: 'success',
            message: '删除成功'
          })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '删除取消'
          })
        })
    }

    // 单位ID模糊搜索 start
    const handleCompanyIdChange = (val, callback) => {
      state.formData.companyId = val
      state.companyInfo.fullName = val
      getCompanyList({ name: val }).then((resp) => {
        callback(resp)
      })
    }

    const handleCompanyIdSelect = (val) => {
      if (!val) return
      state.formData.companyId = val.id
      state.companyInfo = val
    }
    // 单位ID模糊搜索 end

    return {
      ...toRefs(state),
      handleCompanyIdChange,
      handleCompanyIdSelect,
      formVmSearch,
      formVm,
      listHeader,
      getList,
      handlePaginationChange,
      reset,
      resetVm,
      cancelButton,
      confirmButton,
      formSubmitAdd,
      formSubmitEdit,
      formSubmitDelete
    }
  }
}
</script>

<style scoped>
.main {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}
.count {
  padding: 0 15px;
  margin: 20px 0;
  height: 30px;
  line-height: 30px;
  background-color: #edf9ff;
}
.select-width {
  width: 80% !important;
}

.danger {
  color: #d9041a;
  font-weight: bold;
  padding-right: 5px;
}

.dialog-content-invite {
  font-size: 16px;
}

.invite-label {
  font-weight: bold;
  margin: 8px 8px 8px 15px;
  width: 15%;
}

.invite-content {
  margin: 8px 0;
  width: 78%;
}

.btn-group {
  margin: 10px 20px 10px 0;
}

.btn-search-group {
  padding-left: 30px;
}
</style>
