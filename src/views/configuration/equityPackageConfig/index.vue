<template>
  <div class="main">
    <el-form ref="formVmSearch" :model="formDataSearch" label-width="120px">
      <div class="flex">
        <el-col :span="6">
          <el-form-item label="产品ID:" prop="id">
            <el-input v-model="formDataSearch.id" clearable placeholder="请输ID" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="产品名称:" prop="name">
            <el-input v-model="formDataSearch.name" clearable placeholder="产品名称进行模糊搜索" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="产品类型:" prop="equityPackageCategoryId">
            <el-select
              class="select-width-100"
              v-model="formDataSearch.equityPackageCategoryId"
              clearable
              placeholder="产品类型进行模糊搜索"
            >
              <el-option
                v-for="item in selectData.equityPackageCategoryList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="产品包类型:" prop="equityPackageType">
            <el-select
              class="select-width-100"
              v-model="formDataSearch.equityPackageType"
              clearable
              placeholder="请选择显示状态"
            >
              <el-option
                v-for="item in selectData.equityPackageTypeList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </div>
      <div class="flex">
        <el-col :span="6">
          <el-form-item label="服务周期:" prop="days">
            <el-input v-model="formDataSearch.days" clearable placeholder="请输入服务周期天数" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态:" prop="status">
            <el-select
              class="select-width-100"
              v-model="formDataSearch.status"
              clearable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in selectData.statusList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </div>
      <div class="flex">
        <el-col :span="24">
          <div class="nowrap ml-15 btn-search-group">
            <el-button type="primary" @click="getList">搜索</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </el-col>
      </div>
    </el-form>
    <div class="btn-group">
      <el-button type="success" class="el-icon-plus" @click="formSubmitAdd()"> 新增产品 </el-button>
    </div>
    <el-table
      :data="list"
      border
      v-loading="loading"
      @sort-change="handleSortable"
      ref="ConfigTable"
    >
      <template v-for="(item, index) in listHeader">
        <el-table-column
          v-if="item.select && item.k === 1"
          :key="index"
          :label="item.v"
          prop="id"
          align="center"
          width="70"
        />
        <el-table-column
          v-if="item.select && item.k === 2"
          :key="index"
          :label="item.v"
          prop="name"
          align="center"
          width="160"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div>
              {{ row.name }}
              <el-tooltip placement="right" effect="light">
                <template #content>
                  <ul>
                    <li class="ml20" v-for="item in row.resumeEquityPackageRelationSetting">
                      {{ item.resumeEquitySetting.name }}
                    </li>
                  </ul>
                </template>
                <el-icon :size="18" class="p-s-icon">
                  <i class="el-icon-warning-outline"></i>
                </el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 3"
          :key="index"
          :label="item.v"
          prop="subname"
          align="center"
          width="120"
        />
        <el-table-column
          v-if="item.select && item.k === 4"
          :key="index"
          :label="item.v"
          prop="originalAmount"
          width="110"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 5"
          :key="index"
          :label="item.v"
          prop="realAmount"
          width="110"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 6"
          :key="index"
          :label="item.v"
          prop="days"
          width="110"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 7"
          :key="index"
          :label="item.v"
          prop="equityPackageTypeText"
          width="110"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 8"
          :key="index"
          :label="item.v"
          prop="buyTypeText"
          width="110"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 9"
          :key="index"
          :label="item.v"
          prop="statusText"
          width="110"
          align="center"
        >
          <template #default="scope">
            <el-tag
              class="tag"
              effect="plain"
              v-if="scope.row.status === '-1'"
              type="danger"
              size="large"
              >{{ scope.row.statusText }}</el-tag
            >
            <el-tag
              class="tag"
              effect="plain"
              v-if="scope.row.status === '0'"
              type="info"
              size="large"
              >{{ scope.row.statusText }}</el-tag
            >
            <el-tag
              class="tag"
              effect="plain"
              v-if="scope.row.status === '1'"
              type="success"
              size="large"
              >{{ scope.row.statusText }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          v-if="item.select && item.k === 10"
          :key="index"
          :label="item.v"
          prop="buyDesc"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 11"
          :key="index"
          :label="item.v"
          prop="updateTime"
          align="center"
        />
        <el-table-column
          v-if="item.select && item.k === 12"
          :key="index"
          :label="item.v"
          prop="opration"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="formSubmitEdit(row)"> 编辑 </el-button>
            <el-button
              type="success"
              v-if="row.status === '0' || row.status === '-1'"
              size="small"
              @click="formSubmitStatus(row.id, 1)"
            >
              上线
            </el-button>
            <el-button
              type="danger"
              v-if="row.status === '1'"
              size="small"
              @click="formSubmitStatus(row.id, -1)"
            >
              下线
            </el-button>
            <el-button
              type="danger"
              v-if="row.status === '0'"
              size="small"
              @click="formSubmitStatus(row.id, -9)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <Paging class="mt-15" @change="handlePaginationChange" :total="pagination.total" />
  </div>
  <el-dialog width="45%" :title="title" v-model="visible" @close="cancelButton">
    <el-form ref="formVm" :model="formData" :rules="formRules" class="form-data">
      <el-form-item label="产品名称:" :label-width="formLabelWidth" prop="name">
        <el-input
          v-model="formData.name"
          :disabled="submitType === 2 && rowStatus !== '0'"
          class="select-width"
          placeholder="请输入产品名称，例如：黄金VIP(30天)"
          type="text"
        />
      </el-form-item>
      <el-form-item label="产品类型:" :label-width="formLabelWidth" prop="equityPackageCategoryId">
        <el-select
          v-model="formData.equityPackageCategoryId"
          class="select-width"
          clearable="true"
          :disabled="submitType === 2 && rowStatus !== '0'"
          @change="equityPackageCategoryIdChange()"
        >
          <el-option
            v-for="item in selectData.equityPackageCategoryList"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="划线金额:" :label-width="formLabelWidth" prop="originalAmount">
        <el-input
          :disabled="submitType === 2 && rowStatus !== '0'"
          v-model="formData.originalAmount"
          class="select-width"
          placeholder="请输入划线金额"
          type="number"
        />
      </el-form-item>
      <el-form-item label="真实金额:" :label-width="formLabelWidth" prop="realAmount">
        <el-input
          :disabled="submitType === 2 && rowStatus !== '0'"
          v-model="formData.realAmount"
          class="select-width"
          placeholder="请输入真实金额"
          type="number"
        />
      </el-form-item>
      <el-form-item label="服务周期(天):" :label-width="formLabelWidth" prop="days">
        <el-input
          :disabled="submitType === 2 && rowStatus !== '0'"
          v-model="formData.days"
          class="select-width"
          placeholder="请输入服务周期"
          type="number"
        />
      </el-form-item>
      <el-form-item label="权益:" :label-width="formLabelWidth" prop="equityIds">
        <el-select
          :disabled="true"
          v-model="formData.equityIds"
          multiple="true"
          class="select-width"
          clearable="true"
        >
          <el-option
            v-for="item in selectData.equityList"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="产品包类型:" :label-width="formLabelWidth" prop="equityPackageType">
        <el-radio-group v-model="formData.equityPackageType">
          <el-radio
            :disabled="submitType === 2 && rowStatus !== '0'"
            v-for="item in selectData.equityPackageTypeList"
            :key="item.k"
            :label="item.k"
            >{{ item.v }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item label="标签:" :label-width="formLabelWidth" prop="buyType">
        <el-select v-model="formData.buyType" class="select-width" clearable="true">
          <el-option
            v-for="item in selectData.buyTypeList"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="产品描述:" :label-width="formLabelWidth" prop="buyDesc">
        <el-input
          v-model="formData.buyDesc"
          class="select-width"
          placeholder="请输入产品描述"
          type="text"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmButton">确认</el-button>
        <el-button @click="cancelButton"> 取消 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, ref } from 'vue'
import Paging from '/@/components/base/paging.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  equityPackageIndex,
  equityPackageAdd,
  equityPackageEdit,
  equityPackageFilter,
  equityPackageEditInit,
  equityPackageStatus
} from '../../../api/equityPackageConfig'
import InputAutocomplete from '/@/components/base/inputAutocomplete.vue'

export default {
  name: 'equityPackageConfig',
  components: {
    InputAutocomplete,
    Paging
  },
  setup() {
    const formVmSearch = ref()
    const formVm = ref()
    const listHeader = ref([
      {
        k: 1,
        v: '产品ID',
        name: 'id',
        select: true,
        default: true
      },
      {
        k: 2,
        v: '产品名称',
        name: 'name',
        select: true,
        default: true
      },
      {
        k: 3,
        v: '产品类型',
        name: 'subname',
        select: true,
        default: true
      },
      {
        k: 4,
        v: '划线金额',
        name: 'originalAmount',
        select: true,
        default: false
      },
      {
        k: 5,
        v: '真实金额',
        name: 'realAmount',
        select: true,
        default: true
      },
      {
        k: 6,
        v: '服务周期',
        name: 'days',
        select: true,
        default: true
      },
      {
        k: 7,
        v: '产品包类型',
        name: 'equityPackageTypeText',
        select: true,
        default: true
      },
      {
        k: 8,
        v: '标签',
        name: 'buyTypeText',
        select: true,
        default: true
      },
      {
        k: 9,
        v: '状态',
        name: 'statusText',
        select: true,
        default: true
      },
      {
        k: 10,
        v: '描述',
        name: 'buyDesc',
        select: true,
        default: true
      },
      {
        k: 11,
        v: '更新时间',
        name: 'updateTime',
        select: true,
        default: true
      },
      {
        k: 12,
        v: '操作',
        name: 'opration',
        select: true,
        default: true
      }
    ])
    const state = reactive({
      formLabelWidth: '120px',
      loading: false,
      visible: false,
      title: '',
      list: [],
      selectData: {
        statusList: [],
        equityPackageTypeList: [],
        buyTypeList: [],
        equityList: [],
        equityPackageCategoryList: []
      },
      pagination: {
        total: 0,
        pageSize: 20,
        page: 1
      },
      formDataSearch: {
        id: '',
        name: '',
        equityPackageCategoryId: '',
        equityPackageType: '',
        days: '',
        status: ''
      },
      fileList: [],
      submitType: 0,
      rowId: 0,
      rowStatus: '',
      formData: {
        name: '',
        equityPackageCategoryId: '',
        originalAmount: '',
        realAmount: '',
        equityPackageType: '1',
        buyType: '',
        days: '',
        buyDesc: '',
        equityIds: []
      }
    })

    /**
     * 列表
     * @returns {Promise<void>}
     */
    const getList = async () => {
      state.loading = true
      const { data, pages } = await equityPackageIndex(state.formDataSearch)
      state.list = data
      state.pagination.total = pages.total
      state.loading = false
    }

    /**
     * 获取过滤列表
     */
    const getFilter = async () => {
      const {
        equityPackageTypeList,
        buyTypeList,
        equityList,
        statusList,
        equityPackageCategoryList
      } = await equityPackageFilter()
      state.selectData.equityPackageTypeList = equityPackageTypeList
      state.selectData.buyTypeList = buyTypeList
      state.selectData.equityList = equityList
      state.selectData.statusList = statusList
      state.selectData.equityPackageCategoryList = equityPackageCategoryList
    }

    /**
     * 初始化调用
     */
    getList()
    getFilter()

    /**
     * 分页
     * @param data
     */
    const handlePaginationChange = (data) => {
      state.formDataSearch.page = data.page
      state.formDataSearch.pageSize = data.limit
      getList()
    }

    /**
     * 重置搜索表单
     */
    const reset = () => {
      formVmSearch.value.resetFields()
      getList()
    }

    /**
     * 添加
     */
    const formSubmitAdd = () => {
      state.title = '新增'
      state.submitType = 1
      state.visible = true
    }

    /**
     * 编辑初始化
     * @param row
     */
    const formSubmitEdit = (row) => {
      state.title = '编辑'
      state.rowId = row.id
      state.rowStatus = row.status
      state.submitType = 2
      // 初始化
      equityPackageEditInit({ id: row.id }).then((res) => {
        state.formData.name = res.name
        state.formData.equityPackageCategoryId = res.equityPackageCategoryId
        state.formData.originalAmount = res.originalAmount
        state.formData.realAmount = res.realAmount
        state.formData.days = res.days
        state.formData.realAmount = res.realAmount
        state.formData.equityIds = res.equityIds
        state.formData.equityPackageType = res.equityPackageType
        state.formData.equityIds = res.equityIds
        state.formData.buyType = res.buyType
        state.formData.buyDesc = res.buyDesc
      })
      state.visible = true
    }

    /**
     * 确认提交表单
     */
    const confirmButton = () => {
      formVm.value.validate((validate_result, data) => {
        if (!validate_result) {
          return
        }
        if (state.submitType === 1) {
          equityPackageAdd(state.formData).then((res) => {
            state.visible = false
            formVm.value.resetFields()
            getList()
          })
        } else if (state.submitType === 2) {
          state.formData.id = state.rowId
          equityPackageEdit(state.formData).then((res) => {
            state.visible = false
            formVm.value.resetFields()
            getList()
          })
        }
      })
    }

    /**
     * 取消提交表单
     */
    const cancelButton = () => {
      formVm.value.resetFields()
      state.fileList = []
      state.visible = false
    }

    /**
     * 验证表单
     * @type {Ref<UnwrapRef<{personActiveDayNumber: [{trigger: string[], message: string, required: boolean}], invitePersonResumeIds: [{trigger: string[], message: string, required: boolean}], inviteDeliveryWay: [{required: boolean}], inviteSelectText: [{trigger: string[], message: string, required: boolean}], inviteSelect: [{required: boolean}], inviteNumber: [{trigger: string[], message: string, required: boolean}], inviteTime: [{trigger: string[], message: string, required: boolean}]}>>}
     */
    const formRules = ref({
      name: [{ required: true, message: '产品名称不允许为空', trigger: ['blur', 'change'] }],
      equityPackageCategoryId: [
        { required: true, message: '请选择产品类型', trigger: ['blur', 'change'] }
      ],
      originalAmount: [
        { required: true, message: '划线金额不允许为空', trigger: ['blur', 'change'] }
      ],
      realAmount: [{ required: true, message: '真实金额不允许为空', trigger: ['blur', 'change'] }],
      days: [{ required: true, message: '服务周期不允许为空', trigger: ['blur', 'change'] }],
      equityPackageType: [
        { required: true, message: '请选择产品包类型', trigger: ['blur', 'change'] }
      ],
      equityIds: [{ required: true, message: '请选择产品所包含权益', trigger: ['blur', 'change'] }]
    })

    /**
     * 状态按钮
     */
    const formSubmitStatus = (id, status) => {
      let title = ''
      let msg = ''
      if (status === 1) {
        title = '上线操作'
        msg = `确定将产品ID为：${id}产品上线售卖吗？`
      } else if (status === -1) {
        title = '下线操作'
        msg = `确定将产品ID为：${id}产品下线停止购买吗？`
      } else if (status === -9) {
        title = '删除操作'
        msg = `确定将产品ID为：${id}产品删除吗？确认删除后将无法找回`
      }
      ElMessageBox.confirm(msg, title, {
        cancelButtonText: '取消',
        confirmButtonText: '确认',
        type: 'warning'
      })
        .then(() => {
          title += '(二次确认)'
          msg = `再次确认，${msg}`
          ElMessageBox.confirm(msg, title, {
            cancelButtonText: '取消',
            confirmButtonText: '确认',
            type: 'warning'
          })
            .then(() => {
              equityPackageStatus({ id, status }).then((resp) => {
                getList()
              })
            })
            .catch(() => {
              ElMessage({
                type: 'info',
                message: '操作取消'
              })
            })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '操作取消'
          })
        })
    }

    /**
     * 产品类型改变
     */
    const equityPackageCategoryIdChange = () => {
      if (state.formData.equityPackageCategoryId === '1') {
        state.formData.equityIds = ['1', '2', '3', '4', '5', '6', '7', '8']
      } else if (state.formData.equityPackageCategoryId === '2') {
        state.formData.equityIds = ['1', '2']
      } else if (state.formData.equityPackageCategoryId === '3') {
        state.formData.equityIds = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11']
      } else if (state.formData.equityPackageCategoryId === '4') {
        state.formData.equityIds = ['9', '10', '11']
      } else {
        state.formData.equityIds = []
      }
    }

    return {
      ...toRefs(state),
      getList,
      formVmSearch,
      formVm,
      listHeader,
      handlePaginationChange,
      reset,
      formRules,
      formSubmitAdd,
      formSubmitEdit,
      confirmButton,
      cancelButton,
      formSubmitStatus,
      equityPackageCategoryIdChange
    }
  }
}
</script>

<style scoped>
.main {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.el-select .el-input {
  width: 130px;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

.ml20 {
  margin-left: 20px;
}

.btn-group {
  margin: 15px 0;
}

.select-width {
  width: 80% !important;
}

.radio-width {
  width: 40px !important;
}

.select-width-100 {
  width: 100%;
}

.tip-color {
  font-weight: bold;
  color: #ff4d4f;
}

.tag {
  font-weight: bold;
}

.btn-search-group {
  width: 100%;
  justify-content: right;
  padding-right: 40px;
}
</style>
