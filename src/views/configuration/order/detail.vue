<template>
  <div class="order-detail-container" v-loading="loading">
    <el-descriptions class="detail-card" title="订单信息" :column="4" border>
      <el-descriptions-item v-for="({ label, value }, index) in orderInfo" :key="index">
        <template #label>
          <div class="cell-item">{{ label }}</div>
        </template>
        {{ value }}
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions class="detail-card">
      <template #title>
        <div class="title">
          订单备注<el-button type="primary" link @click="handleNote">编辑</el-button>
        </div>
      </template>

      <el-descriptions-item>
        {{ result.remark }}
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions class="detail-card" :title="productName" :column="4" border>
      <el-descriptions-item v-for="({ label, value }, index) in productInfo" :key="index">
        <template #label>
          <div class="cell-item">{{ label }}</div>
        </template>
        {{ value }}
      </el-descriptions-item>
    </el-descriptions>

    <el-dialog
      v-model="dialogVisible"
      title="订单备注"
      width="568px"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-input
        v-model="content"
        type="textarea"
        :rows="8"
        resize="none"
        placeholder="请添加备注"
      />

      <template #footer>
        <el-button type="primary" @click="handleConfirm">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue'
import { useRoute } from 'vue-router'

import { getPersonOrderDetail, setPersonOrderDetailNote } from '/@/api/configuration'
import { ElMessage } from 'element-plus'

const route = useRoute()

const loading = ref(false)

const result: any = ref({})

const orderInfo = computed(() => {
  const {
    orderNo,
    addTime,
    tradeNo,
    payTime,
    realAmount,
    statusName,
    paywayName,
    platformName,
    discountsAmount,
    resumeId
  } = unref(result)
  const { name = '', resumeMobile = '' } = unref(result).resumeInfo ?? {}

  return [
    { label: '订单号：', value: orderNo },
    { label: '下单时间：', value: addTime },
    { label: '下单用户：', value: `${name}（ID：${resumeId}）` },
    { label: '手机号：', value: resumeMobile },
    { label: '交易流水号：', value: tradeNo },
    { label: '支付时间：', value: payTime },
    { label: '订单金额：', value: `￥${realAmount}` },
    { label: '支付状态：', value: statusName },
    { label: '支付方式：', value: paywayName },
    { label: '下单渠道：', value: platformName },
    { label: '优惠金额', value: discountsAmount },
    { label: '', value: '' }
  ]
})

const productName = computed(
  () => unref(result)?.equityPackageInfo?.resumeEquityPackageCategorySetting?.name
)

const productInfo = computed(() => {
  const { resumeEquityPackageCategorySetting = {} } = unref(result)?.equityPackageInfo ?? {}

  const { equityPackageId, snapshotInfo } = unref(result)
  const { name: type = '' } = resumeEquityPackageCategorySetting

  // 这三个从快照里面取
  const services = snapshotInfo?.equityContent ?? ''
  const name = snapshotInfo?.equityPackageName ?? ''
  const days = snapshotInfo?.serviceDays ?? ''

  return [
    { label: '产品ID：', value: equityPackageId },
    { label: '产品名称：', value: name },
    { label: '产品类型：', value: type },
    { label: '服务时长：', value: `${days}天` },
    { label: '包含服务：', value: services }
  ]
})

const dialogVisible = ref(false)

const content = ref('')

const validRouteId = () => /^\d+$/.test(<string>route.params.id)

const handleNote = () => {
  if (validRouteId() === false) return

  content.value = unref(result).remark
  dialogVisible.value = true
}

const fetchData = async () => {
  if (validRouteId() === false) {
    ElMessage.warning('订单有误，请调整后再查询')
    return
  }

  loading.value = true

  try {
    const data = await getPersonOrderDetail(<string>route.params.id)

    result.value = data
  } finally {
    loading.value = false
  }
}

const handleConfirm = async () => {
  await setPersonOrderDetailNote({ id: route.params.id, remark: unref(content) })
  dialogVisible.value = false
  fetchData()
}

fetchData()
</script>

<style lang="scss" scoped>
.order-detail-container {
  .detail-card {
    margin-bottom: 15px;
    padding: 20px 20px 30px;
    background-color: var(--color-whites);
    border-radius: 4px;
  }

  .title {
    display: flex;
    align-items: center;

    .el-button {
      margin-left: 24px;
    }
  }
}
</style>
