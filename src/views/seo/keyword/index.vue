<template>
  <div class="main">
    <el-card>
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="热词管理" name="first">
          <el-form ref="form" :model="formData" :inline="true">
            <div class="flex">
              <el-form-item class="span-4" label="关键词检索" prop="keyword">
                <el-input v-model="formData.keyword" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item class="span-4" label="code检索" prop="code">
                <el-input v-model="formData.code" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item class="span-4" label="创建时间" prop="startAddTime">
                <DatePickerRange
                  v-model:start="formData.startAddTime"
                  v-model:end="formData.endAddTime"
                />
              </el-form-item>
            </div>
          </el-form>

          <el-row style="margin-bottom: 20px">
            <el-button type="primary" @click="getKeywordsList">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button @click="downloadExcel">下载</el-button>
            <el-button type="primary" @click="openAddDialog">关键词导入</el-button>
          </el-row>

          <el-table :data="tableList" border size="small">
            <el-table-column prop="id" label="ID" align="center" />
            <el-table-column prop="code" label="code" align="center" />
            <el-table-column prop="keyword" label="关键词" align="center" />
            <el-table-column prop="addTime" label="导入时间" align="center" />
            <el-table-column label="操作" align="center">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleEdit(row.keyword, row.id)"
                  >编辑</el-button
                >
                <el-button type="danger" size="small" @click="handleDelete(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <Paging class="mt-20" :total="total" @change="handlePageChange" />
        </el-tab-pane>
        <el-tab-pane label="职位百科" name="second">
          <baike></baike>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <AddKeywords v-model="addDialogVisible" @update="getKeywordsList" />

    <EditKeywords
      v-model="editDialogVisible"
      v-model:keyword="keyword"
      :id="keywordId"
      @update="getKeywordsList"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessageBox } from 'element-plus'
import { getSeoKeywordsList, delSeoKeywords, downloadExcelKeywords } from '/@/api/seo'
import AddKeywords from '/@/views/seo/keyword/components/addKeywords.vue'
import EditKeywords from '/@/views/seo/keyword/components/editKeywords.vue'
import baike from '/@/views/seo/keyword/components/baike.vue'

import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'

defineOptions({ name: 'keyword' })

const activeName = ref('first')

const currentPage = ref(1)
const pageSize = ref(20)

const formData = ref({
  keyword: '',
  code: '',
  startAddTime: '',
  endAddTime: '',
  page: computed(() => currentPage.value),
  pageSize: computed(() => pageSize.value)
})

const total = ref(0)

const addDialogVisible = ref(false)

const editDialogVisible = ref(false)

const tableList = ref([])
const form = ref<any>({})

const keyword = ref('')
const keywordId = ref('')

const getKeywordsList = async () => {
  const { list, count } = await getSeoKeywordsList(formData.value)
  tableList.value = list
  total.value = count
}

getKeywordsList()

const handleDelete = (id: string) => {
  ElMessageBox.confirm('删除后将不可恢复，确认要删除关键词吗？', '删除关键词')
    .then(async () => {
      await delSeoKeywords({ id })
      await getKeywordsList()
    })
    .catch(() => false)
}

const handlePageChange = (data: any) => {
  const { page, limit } = data

  currentPage.value = page
  pageSize.value = limit

  getKeywordsList()
}

const openAddDialog = () => {
  addDialogVisible.value = true
}

const handleEdit = (value: string, id: string) => {
  keyword.value = value
  keywordId.value = id
  editDialogVisible.value = true
}

const handleReset = async () => {
  await form.value.resetFields()

  getKeywordsList()
}

const downloadExcel = () => {
  downloadExcelKeywords(formData.value)
}
</script>

<style lang="scss" scoped>
.demo-tabs {
  background-color: #fff;
}
</style>
