<template>
  <el-dialog title="详情" v-model="visible" top="8vh" width="900px">
    <div class="flex">
      关联公告：
      <el-link type="primary" :underline="false">重庆大学2021年度博士生招聘简章</el-link>
    </div>
    <!-- 没有审核通过历史的职位详情 -->
    <div v-if="false" class="mt-10">
      <div class="pb-10 border-bottom">
        <div class="fw-bold mb-10 color-dark">基本信息</div>
        <div class="flex mb-10">
          <div class="span-3">职位名称：博士后</div>
          <div class="span-3">职位类型：大学教师</div>
          <div class="span-3">用人部门：xxx</div>
        </div>
        <div class="flex mb-10">
          <div class="span-3">工作性质：博士后</div>
          <div class="span-3">招聘人数：大学教师</div>
          <div class="span-3">工作地点：xxx</div>
        </div>
        <div class="fw-bold mb-10 color-dark">其他要求</div>
        <div class="flex mb-10">
          <div class="span-3">专业要求：xxx</div>
          <div class="span-3">学历要求：xxx</div>
          <div class="span-3">职称要求：xxx</div>
        </div>
        <div class="flex mb-10">
          <div class="span-3">工作经验：xxx</div>
          <div class="span-3">年龄要求：xxx</div>
          <div class="span-3">性别要求：xxx</div>
        </div>
        <div class="flex mb-10">
          <div class="span-3">政治面貌：xxx</div>
          <div class="span-3">海外经历：xxx</div>
        </div>
      </div>
      <div class="py-20 border-bottom">
        <div class="fw-bold mb-10 color-dark">岗位职责</div>
        <div>此处莱克斯顿封建时代开了房上课两地分居实力坑爹附件楼上的咖啡机路上看到房价</div>
      </div>
      <div class="py-20 border-bottom">
        <div class="fw-bold mb-10 color-dark">任职要求</div>
        <div>此处莱克斯顿封建时代开了房上课两地分居实力坑爹附件楼上的咖啡机路上看到房价</div>
      </div>
      <div class="py-20 border-bottom">
        <div class="fw-bold mb-10 color-dark">其他说明</div>
        <div>此处莱克斯顿封建时代开了房上课两地分居实力坑爹附件楼上的咖啡机路上看到房价</div>
      </div>
    </div>
    <!-- 有审核记录的职位详情 -->
    <div v-else>
      <div class="py-20 border-bottom">
        <div class="fw-bold mb-10 color-dark">修改前</div>
        <div class="fw-bold mb-10 color-dark">任职要求</div>
        <div>此处莱克斯顿封建时代开了房上课两地分居实力坑爹附件楼上的咖啡机路上看到房价</div>
      </div>
      <div class="py-20 border-bottom">
        <div class="fw-bold mb-10 color-dark">修改后</div>
        <div class="fw-bold mb-10 color-dark">任职要求</div>
        <div>此处莱克斯顿封建时代开了房上课两地分居实力坑爹附件楼上的咖啡机路上看到房价</div>
      </div>
    </div>

    <div class="py-20">
      <div class="fw-bold mb-10 color-dark">审核处理意见</div>
      <el-input type="textarea" resize="none" placeholder="审核拒绝须明确拒绝原因"></el-input>
      <div class="mt-15">
        <el-button type="primary">审核通过</el-button>
        <el-button type="primary">审核拒绝</el-button>
      </div>
    </div>
    <div>
      <div>
        <el-button @click="showRecord = !showRecord" type="primary" plain>审核处理历史</el-button>
      </div>
      <el-table v-show="showRecord" :data="list" size="small" class="mt-15">
        <el-table-column
          prop="1"
          align="center"
          header-align="center"
          label="审核人"
        ></el-table-column>
        <el-table-column
          prop="1"
          align="center"
          header-align="center"
          label="审核时间"
          min-width="120px"
        ></el-table-column>
        <el-table-column
          prop="1"
          align="center"
          header-align="center"
          label="审核状态"
        ></el-table-column>
        <el-table-column
          prop="1"
          align="center"
          header-align="center"
          min-width="150px"
          label="处理意见"
          show-overflow-tooltip
        ></el-table-column>
        <template #empty>
          <el-empty description="暂无记录" :image-size="100"></el-empty>
        </template>
      </el-table>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const visible = ref(false)
const showRecord = ref(false)
const list = ref([])

const open = () => {
  visible.value = true
}

defineExpose({
  open
})
</script>

<style scoped lang="scss">
.border-bottom {
  border-bottom: 1px solid #efefef;
}
</style>
