<template>
  <div>
    <div class="box">
      <Filter @search="handleSearch" />
      <div class="jc-between amount">
        <div>
          共计:
          <span class="danger">{{ amount.jobAmount }}</span>
          个职位&nbsp;&nbsp;待审核:
          <span class="danger">{{ amount.jobWaitAmount }}</span>
          个职位&nbsp;&nbsp;审核拒绝:
          <span class="danger">{{ amount.jobRefuseAmount }}</span>
          个职位
        </div>
      </div>
      <el-table :data="list" border size="small" v-loading="loading" @sort-change="handleSortable">
        <el-table-column
          prop="id"
          align="center"
          header-align="center"
          label="职位ID"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="sortName"
          align="center"
          header-align="center"
          sortable="custom"
          label="职位名称"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-link class="fw-normal fs-12" :underline="false" @click="handleJobDetail(row.id)">{{
              row.name
            }}</el-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="basicInformation"
          align="center"
          header-align="center"
          label="基本信息"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="1"
          align="center"
          header-align="center"
          label="关联公告"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <router-link
              :to="`/cms/announcementDetail/${row.announcementId}/${row.announcementStatus}`"
              >{{ row.announcementTitle }}</router-link
            >
          </template></el-table-column
        >
        <el-table-column
          align="center"
          header-align="center"
          label="所属单位"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <router-link :to="`/company/details?id=${row.companyId}`">{{
              row.company
            }}</router-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="auditStatusTitle"
          align="center"
          header-align="center"
          label="审核状态"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="creator"
          align="center"
          header-align="center"
          label="创建人"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop=""
          align="center"
          header-align="center"
          label="发布模式"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div>
              {{ row.isArticle == 1 ? '职位+公告模式' : '纯职位模式' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sortReleaseTime"
          align="center"
          header-align="center"
          sortable="custom"
          min-width="110px"
          label="发布时间"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div>{{ row.releaseTime }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sortApplyTime"
          align="center"
          header-align="center"
          sortable="custom"
          min-width="110px"
          label="申请审核时间"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div>{{ row.applyAuditTime }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="1"
          align="center"
          header-align="center"
          label="操作"
          show-overflow-tooltip
          ><template #default="{ row }">
            <el-button @click="openAuditDetail(row.id)" size="small" type="primary">审核</el-button>
          </template></el-table-column
        >
        <template #empty>
          <el-empty description="暂无数据"></el-empty>
        </template>
      </el-table>
      <Pagination
        v-if="list.length"
        class="mt-15"
        :total="pagination.total"
        @change="handlePaginationChange"
      />
    </div>
    <AuditDialog ref="auditDialog" />
    <JobDetail ref="jobDetail" />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import Filter from './components/filter.vue'
import Pagination from '/@/components/base/paging.vue'
import AuditDialog from './components/auditDialog.vue'
import JobDetail from '/@/components/job/jobDetailDialog.vue'

import { getJobAuditList } from '/@/api/job'

export default defineComponent({
  name: 'jobAudit',
  components: { Filter, AuditDialog, Pagination, JobDetail },
  setup() {
    const auditDialog = ref()
    const router = useRouter()
    const jobDetail = ref()

    const state = reactive({
      loading: false,
      formData: {
        sortName: '',
        sortReleaseTime: '',
        sortApplyTime: '',
        export: 0,

        page: 1,
        limit: 20
      },
      // 分页信息
      pagination: {
        total: 0
      },
      amount: {},
      list: []
    })

    const getList = async () => {
      state.loading = true
      await getJobAuditList(state.formData).then((resp: any) => {
        if (state.formData.export === 1) {
          const aEl = document.createElement('a')
          aEl.setAttribute('href', resp.excelUrl)
          aEl.click()
        } else {
          state.list = resp.list
          state.amount = resp.amount
          state.pagination.total = Number(resp.page.count)
        }
      })
      state.loading = false
    }

    onActivated(() => {
      getList()
    })

    const handleSearch = (filter: any) => {
      state.formData = {
        ...state.formData,
        ...filter
      }
      getList()
    }

    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.limit = data.limit
      getList()
    }

    // 排序
    const handleSortable = ({ prop, order }) => {
      Reflect.deleteProperty(state.formData, 'sortName')
      Reflect.deleteProperty(state.formData, 'sortReleaseTime')
      Reflect.deleteProperty(state.formData, 'sortApplyTime')
      if (order === 'ascending') {
        // 正序
        state.formData[prop] = 2
      } else if (order === 'descending') {
        state.formData[prop] = 1
      }
      getList()
    }

    const openAuditDetail = (id: string) => {
      router.push(`/job/audit/detail/${id}`)
    }

    // 职位详情
    const handleJobDetail = (id: string) => {
      jobDetail.value.open(id)
    }

    return {
      jobDetail,
      getList,
      handlePaginationChange,
      handleSearch,
      handleSortable,
      auditDialog,
      openAuditDetail,
      handleJobDetail,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}
.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}
</style>
