<template>
  <el-dialog title="职位操作日志" v-model="visible" top="13vh" width="980px" @close="handleClose">
    <el-form :model="formData" ref="form" :inline="true">
      <el-form-item prop="handlerName">
        <el-input
          v-model="formData.handlerName"
          placeholder="操作用户"
          filterable
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="handleTimeStart">
        <DatePickerRange
          v-model:start="formData.handleTimeStart"
          v-model:end="formData.handleTimeEnd"
          placeholder="操作时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table max-height="50vh" :data="list" border size="small" v-loading="loading">
      <el-table-column
        prop="handlerName"
        align="center"
        header-align="center"
        label="操作用户"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="addTime"
        align="center"
        header-align="center"
        label="操作时间"
        min-width="130px"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="ip"
        align="center"
        header-align="center"
        label="操作IP"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="handleTypeTitle"
        align="center"
        header-align="center"
        label="操作类型"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="handleBefore"
        align="center"
        header-align="center"
        label="操作前"
        min-width="200px"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <p v-for="(v, k) in row.handleBefore" :key="k">{{ k }}:{{ v }}</p>
        </template>
      </el-table-column>
      <el-table-column
        prop="handleAfter"
        align="center"
        min-width="200px"
        header-align="center"
        label="操作后"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <p v-for="(v, k) in row.handleAfter" :key="k">{{ k }}:{{ v }}</p>
        </template></el-table-column
      >
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>
    <Pagination
      v-if="pagination.total > 0"
      :total="pagination.total"
      class="mt-15"
      @change="handlePaginationChange"
      align="right"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Pagination from '/@/components/base/paging.vue'
import { getJobHandleLogList } from '/@/api/job'

const form = ref()
const visible = ref(false)
const loading = ref(false)
const formData = reactive({
  jobId: '',
  handlerName: '',
  handleTimeStart: '',
  handleTimeEnd: '',
  limit: 20,
  page: 1
})

const pagination = reactive({
  total: 0
})

const list = ref([])

const getList = () => {
  loading.value = true
  getJobHandleLogList(formData).then((resp: any) => {
    loading.value = false
    list.value = resp.list.map((item: any) => {
      return {
        ...item,
        handleAfter: item.handleAfter[0],
        handleBefore: item.handleBefore[0]
      }
    })
    pagination.total = resp.page.count
  })
}

const open = (id: string) => {
  visible.value = true
  formData.jobId = id
  getList()
}

const handlePaginationChange = (data: any) => {
  formData.page = data.page
  formData.limit = data.limit
  getList()
}

const handleClose = () => {
  form.value?.resetFields()
}

defineExpose({
  open
})
</script>

<style scoped lang="scss"></style>
