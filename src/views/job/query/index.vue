<template>
  <div class="box">
    <div id="top-container" class="pb-20">
      <FilterView
        @search="handleSearch"
        v-model:isSimple="state.isSimple"
        @toggleShowMore="getTableHeight"
      />

      <div class="jc-between amount">
        <div>
          共计:
          <span class="danger">{{ state.pagination.total }}</span>
          个职位
        </div>
        <el-link :underline="false" type="primary" @click="handleOpenCustomColumn">选择列</el-link>
      </div>
    </div>

    <el-table
      :data="state.list"
      :max-height="maxTableHeight"
      border
      v-loading="state.loading"
      @sort-change="handleSortTable"
      @selection-change="handleSelectionChange"
      ref="jobTable"
    >
      <el-table-column type="selection" width="40" align="center" />

      <template v-for="item in customColumns">
        <el-table-column
          v-if="item.select"
          :key="item.key"
          :prop="item.prop"
          align="center"
          header-align="center"
          :label="item.label"
          :sortable="item.sortable"
          :min-width="setColumnMinWidth(item.key)"
          :fixed="item.key === 'operation' ? 'right' : false"
          :width="item.key === 'operation' ? '256px' : 'auto'"
        >
          <template v-if="item.headerSlot === 'offlineTime'" #header>
            <div class="ai-center inline-flex">
              {{ item.label }}
              <el-tooltip
                effect="dark"
                content="含已下线信息实际下线时间、在线信息预计下线时间、在线信息未配置下线时间情况"
                placement="top"
              >
                <el-icon><QuestionFilled color="#909399" /></el-icon>
              </el-tooltip>
            </div>
          </template>

          <template v-if="item.slot === 'name'" #default="{ row }">
            <el-link class="fw-normal" :underline="false" @click="handleJobDetail(row.jobId)"
              >{{ row.jobName }}
            </el-link>
          </template>

          <template v-else-if="item.slot === 'basicInformation'" #default="{ row }">
            {{ row.city }} | {{ row.educationTypeTitle }} | {{ row.experienceType }} |
            {{ row.wage }}
          </template>

          <template v-else-if="item.slot === 'announcementTitle'" #default="{ row }">
            <router-link
              :to="`/cms/announcementDetail/${row.announcementId}/${row.announcementStatus}`"
              >{{ row.announcementTitle }}
            </router-link>
          </template>

          <template v-else-if="item.slot === 'company'" #default="{ row }">
            <router-link :to="`/company/details?id=${row.companyId}`"
              >{{ row.companyName }}
            </router-link>
          </template>

          <template v-else-if="item.slot === 'applyTotal'" #default="{ row }">
            <span
              v-if="row.applyTotal !== 0"
              @click="openBusiness(row.jobId)"
              class="bg-primary point"
              >{{ row.applyTotal }}</span
            >
            <span v-else>{{ row.applyTotal }}</span>
          </template>

          <template v-else-if="item.slot === 'contact'" #default="{ row }">
            <el-popover placement="top-start">
              <template #reference>
                {{ row.jobContact?.contact }}
              </template>
              <div>
                <div>职位联系人</div>
                <div class="flex mt-5">
                  <span>{{ row.jobContact?.companyMemberType === '0' ? '主' : '子' }}</span>
                  <span>{{ row.jobContact?.contact }} / {{ row.jobContact?.department }}</span>
                </div>
                <div>{{ row.jobContact?.email }} {{ row.jobContact?.mobile }}</div>
              </div>
            </el-popover>
          </template>

          <template v-else-if="item.slot === 'contactSynergyInfo'" #default="{ row }">
            <div v-if="row.jobContactSynergyCount === 0">{{ row.jobContactSynergyCount }}</div>

            <template v-else>
              <div v-for="item in row.jobContactSynergy" :key="item.id">
                <div class="flex mt-5">
                  <!-- <span class="color-primary mr-5">{{ item.isContact == '1' ? '主' : '子' }}</span> -->
                  <div>{{ item.contact }} / {{ item.department }}</div>
                </div>
                <div>{{ item.email }} {{ item.mobile }}</div>
              </div>
            </template>
          </template>

          <template v-else-if="item.slot === 'isMiniappTxt'" #default="{ row }">
            <isMiniappChange
              :value="row.isMiniapp"
              v-model="row.isMiniapp"
              type="job"
              :id="row.jobId"
            ></isMiniappChange>
          </template>

          <template v-else-if="item.slot === 'operation'" #default="{ row }">
            <div class="table-button-group">
              <template v-if="row.btnList.length < 7">
                <el-button
                  v-for="item in row.btnList"
                  :key="item.key"
                  size="small"
                  :disabled="item.disabled === 2"
                  :class="item.class"
                  @click="btnGroupEvent(item.key, row)"
                  >{{ item.label }}
                </el-button>
              </template>

              <template v-else>
                <el-button
                  v-for="item in row.btnList?.slice(0, 5)"
                  :key="item.key"
                  size="small"
                  :disabled="item.disabled === 2"
                  :class="item.class"
                  @click="btnGroupEvent(item.key, row)"
                  >{{ item.label }}
                </el-button>

                <template v-if="row.btnList?.slice(5).length">
                  <el-popover placement="left" width="auto" trigger="click">
                    <template #reference>
                      <el-button class="white" size="small">更多</el-button>
                    </template>

                    <div class="table-popover-button">
                      <el-button
                        v-for="item in row.btnList.slice(5)"
                        :key="item.key"
                        size="small"
                        :disabled="item.disabled === 2"
                        :class="item.class"
                        @click="btnGroupEvent(item.key, row)"
                        >{{ item.label }}
                      </el-button>
                    </div>
                  </el-popover>
                </template>
              </template>
            </div>
          </template>
        </el-table-column>
      </template>

      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>

    <div
      id="bottom-container"
      v-show="state.pagination.total > 0"
      class="pt-15 jc-between ai-center"
      style="flex-shrink: 0"
    >
      <div class="ai-center">
        <el-checkbox
          label="全选"
          class="mr-10"
          @change="jobSelectChange"
          :indeterminate="isIndeterminate"
          v-model="state.isCheckAll"
        ></el-checkbox>
        <el-select
          placeholder="批量操作"
          clearable
          filterable
          :disabled="!state.jobSelection.length"
          v-model="state.batchValue"
          @change="jobSelectBatch"
        >
          <el-option
            v-for="item in state.batchOptions"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          />
        </el-select>
      </div>

      <Pagination
        :total="state.pagination.total"
        @change="handlePaginationChange"
        :page="state.formData.page"
      />
    </div>
  </div>

  <CustomColumnDialog ref="customColumnDialog" v-model:data="customColumns" />
  <LogDialog ref="logDialog" />
  <JobDetail ref="jobDetail" />
  <DeliveryInvite ref="deliveryInvite" />
  <DialogWelfare
    :member-id="state.memberId"
    title="职位福利"
    ref="dialogWelfare"
    @confirm="handleWelfare"
  />

  <EstablishmentDialog
    v-model:visible="state.dialogFormVisible"
    :job-id="establishmentJobId"
    :is-simple="currentOperationType === 1"
    @update="getList"
  />

  <el-dialog
    v-model="state.accountVisible"
    title="协同子账号&联系人 批量设置"
    class="account"
    @close="handleAccountDialogClose"
  >
    <div>设置成功，将替换职位原来的相关设置，请谨慎操作</div>
    <div class="mt-20 mb-20">所属单位：{{ state.checkCompanyName }}</div>
    <el-form :model="state.accountFrom">
      <JobCooperate
        v-if="state.accountVisible"
        hasAccount
        :companyId="state.companyId"
        v-model="synergyData"
      />
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleAccountDialogClose">取 消</el-button>
      <el-button type="primary" @click="handleAccount">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { QuestionFilled } from '@element-plus/icons-vue'
import { reactive, ref, onBeforeMount, onActivated, computed, nextTick } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import FilterView from '/@/components/form/jobFilter.vue'
import CustomColumnDialog from '/@/components/business/customColumnDialog-V2.vue'
import LogDialog from './components/logDialog.vue'
import Pagination from '/@/components/base/paging.vue'
import isMiniappChange from '/@/components/base/select/isMiniappChange.vue'
import JobDetail from '/@/components/job/jobDetailDialog.vue'
import { useRouter } from 'vue-router'

import { getTableStagingField } from '/@/api/config'
import {
  getCooperationJobList,
  jobRefresh,
  jobReleaseAgain,
  jobOffline,
  changeJobShow,
  batchJobRefresh,
  batchChangeJobHide,
  batchChangeJobShow,
  batchJobReleaseAgain,
  batchJobOffline,
  batchJobEstablishment,
  batchAddDeliveryEducationLimit,
  batchDelDeliveryEducationLimit,
  changeDeliveryEducationLimit,
  changeDeliveryFileLimit,
  batchDelDeliveryFileLimit,
  batchAddDeliveryFileLimit,
  // getSimpleJobList,
  changeJobContact,
  accountBatchEdit,
  batchEditWelfare,
  batchEditWelfareCheck,
  jobDelete,
  jobOfflineCheck,
  jobHideCheck
} from '/@/api/job'
import DeliveryInvite from '/@/views/job/query/components/deliveryInvite.vue'
import { useStore } from '/@/store'
import JobCooperate from '/@/components/job/jobCooperate.vue'
import DialogWelfare from '/@/components/base/welfare.vue'
import EstablishmentDialog from '/@/components/job/establishmentDialog.vue'

interface State {
  welfareArray: any[]
  batchOptions: Array<{ k: number; v: string }>
  dialogFormVisible: boolean
  establishmentDisabled: boolean
  establishmentTypeList: any[]
  jobQueryBtn: any[]
  isCheckAll: boolean
  batchValue: any
  jobSelection: any[]
  pageSize: number
  loading: boolean
  formData: {
    export: number
    page: number
    limit: number
    [key: string]: any
  }
  pagination: {
    total: number
    limit: number
    page: number
  }
  list: any[]
  companyId: string
  memberId: string
  accountFrom: {
    jobContactId: string
    jobContactSynergyIds: any[]
    companyId: any
  }
  isSimple: boolean
  accountVisible: boolean
  checkCompanyName: string
}

const dialogWelfare = ref()
const customColumnDialog = ref()
const deliveryInvite = ref()
const logDialog = ref()
const jobDetail = ref()
const jobTable = ref()
const router = useRouter()
const isIndeterminate = ref(false)
const maxTableHeight = ref(450)
const currentOperationJobId = ref('')
// 操作类型：1单个，2批量
const currentOperationType = ref(1)

const establishmentJobId = ref('')

const isFirst = ref(true)

const sortQuery = ref({})

const state = reactive<State>({
  welfareArray: [],
  batchOptions: [
    { k: 1, v: '取消学历限制' },
    { k: 2, v: '添加学历限制' },
    { k: 3, v: '取消附件限制' },
    { k: 4, v: '添加附件限制' },
    { k: 5, v: '刷新职位' },
    { k: 6, v: '隐藏职位' },
    { k: 7, v: '显示职位' },
    { k: 8, v: '再发布职位' },
    { k: 9, v: '下线职位' },
    { k: 10, v: '协同子账号&联系人设置' },
    { k: 11, v: '编制设置' },
    { k: 12, v: '批量福利' }
  ],
  dialogFormVisible: false,
  establishmentDisabled: false,

  establishmentTypeList: [],
  jobQueryBtn: [],
  isCheckAll: false,
  batchValue: null,
  jobSelection: [],
  pageSize: 20,
  loading: false,
  formData: {
    export: 0,
    page: 1,
    limit: 20
  },
  pagination: {
    total: 0,
    limit: 20,
    page: 1
  },
  list: [],
  companyId: '',
  memberId: '',
  accountFrom: {
    jobContactId: '',
    jobContactSynergyIds: [],
    companyId: computed(() => state.companyId)
  },
  isSimple: true,
  accountVisible: false,
  checkCompanyName: ''
})

const store = useStore()
const requestOldRoutesAction: any = computed(
  () => store.state.requestOldRoutes.requestOldRoutesAction
)
state.jobQueryBtn = requestOldRoutesAction.value.jobQuery ?? []

const customColumns = ref([
  {
    prop: 'jobUuid',
    key: 'id',
    label: '职位ID',
    select: true,
    default: true
  },
  {
    prop: 'jobName',
    key: 'name',
    label: '职位名称',
    slot: 'name',
    select: true,
    default: true
  },
  {
    prop: 'basicInformation',
    key: 'basicInformation',
    label: '基本信息',
    slot: 'basicInformation',
    select: true,
    default: true
  },
  {
    prop: 'announcementTitle',
    key: 'announcementTitle',
    label: '关联公告',
    slot: 'announcementTitle',
    select: true,
    default: true
  },
  {
    prop: 'company',
    key: 'company',
    label: '所属单位',
    slot: 'company',
    select: true,
    default: true
  },
  {
    prop: 'click',
    key: 'click',
    label: '点击量',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'applyTotal',
    key: 'applyTotal',
    label: '投递次数',
    sortable: 'custom',
    slot: 'applyTotal',
    select: true,
    default: true
  },
  {
    prop: 'deliveryWay',
    key: 'deliveryWay',
    label: '投递方式',
    select: true,
    default: true
  },
  {
    prop: 'status',
    key: 'status',
    label: '招聘状态',
    select: true,
    default: true
  },
  {
    prop: 'amount',
    key: 'amount',
    label: '招聘人数',
    // sortable: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'department',
    key: 'department',
    label: '用人部门',
    select: false,
    default: false
  },
  {
    prop: 'auditStatus',
    key: 'auditStatus',
    label: '审核状态',
    select: false,
    default: false
  },
  {
    prop: 'isShow',
    key: 'isShow',
    label: '显示状态',
    select: false,
    default: false
  },
  {
    prop: 'publishMode',
    key: 'isArticle',
    label: '发布模式',
    slot: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'majorText',
    key: 'majorId',
    label: '学科专业',
    select: false,
    default: false
  },

  {
    prop: 'creator',
    key: 'creator',
    label: '创建人',
    select: false,
    default: false
  },
  {
    prop: 'auditAdminName',
    key: 'contactSynergy',
    label: '审核人',
    select: false,
    default: false
  },
  {
    prop: 'firstReleaseTimeDate',
    key: 'firstReleaseTimeStart',
    label: '初始发布时间',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'publishTimeDate',
    key: 'releaseTime',
    label: '发布时间',
    select: true,
    default: true
  },

  {
    prop: 'realRefreshTimeDate',
    key: 'refreshTime',
    label: '刷新时间',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'addTimeDate',
    key: 'addTime',
    label: '创建时间',
    sortable: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'offlineTimeDate',
    key: 'periodDate',
    label: '下线时间',
    // sortable: 'custom',
    headerSlot: 'offlineTime',
    select: false,
    default: false
  },
  {
    prop: 'contact',
    key: 'contact',
    label: '职位联系人',
    select: false,
    default: false
  },
  {
    prop: 'jobContactSynergyCount',
    key: 'contactSynergyNum',
    label: '协同子账号',
    select: false,
    default: false
  },
  {
    prop: 'contactSynergyInfo',
    key: 'contactSynergyInfo',
    label: '子账号信息',
    slot: 'contactSynergyInfo',
    select: false,
    default: false
  },
  {
    prop: 'isMiniapp',
    key: 'isMiniappTxt',
    label: '是否小程序',
    slot: 'isMiniappTxt',
    select: false,
    default: false
  },
  {
    prop: 'operation',
    key: 'operation',
    label: '操作',
    slot: 'operation',
    disabled: true,
    select: true,
    default: true
  }
])

const synergyData = computed({
  get() {
    const {
      accountFrom: { jobContactId, jobContactSynergyIds }
    } = state
    return { jobContactId, jobContactSynergyIds }
  },
  set(val: object) {
    Object.keys(val).forEach((key) => {
      state.accountFrom[key] = val[key]
    })
  }
})

const getTableHeight = async () => {
  await nextTick(() => {})
  const topHeight = document.getElementById('top-container')?.clientHeight || 0
  const height = Number(
    document.getElementById('layout-main')?.getAttribute('data-view-height') || 0
  )
  const bottomHeight = document.getElementById('bottom-container')?.clientHeight || 0
  // .box 内边框
  const padding = 40
  maxTableHeight.value = height - topHeight - bottomHeight - padding
}

onActivated(() => {
  // if (isFirst.value) return
  // 移除自动重新加载数据，保持keep-alive缓存效果
  // getList()
})

onBeforeMount(async () => {
  await getList()
  isFirst.value = false

  getTableStagingField('jobQuery').then((resp: any) => {
    if (!resp.value) return
    const value = resp.value.split(',')
    customColumns.value = customColumns.value.map((item: any) => {
      return {
        ...item,
        select: value.includes(item.key)
      }
    })
  })
})

const getList = async () => {
  state.loading = true
  // if (state.isSimple) {
  //   await getSimpleJobList({
  //     ...state.formData,
  //     majorId:
  //       state.formData.majorId instanceof Array
  //         ? state.formData.majorId.join()
  //         : state.formData.majorId,
  //     isCooperation: 1
  //   }).then((resp: any) => {
  //     state.list = resp.list
  //     state.amount = resp.amount ?? {}
  //     state.pagination.total = Number(resp.page?.count)
  //   })
  // } else {
  //   await getCooperationJobList({
  //     ...state.formData,
  //     majorId:
  //       state.formData.majorId instanceof Array
  //         ? state.formData.majorId.join()
  //         : state.formData.majorId
  //   }).then((resp: any) => {
  //     state.list = resp.list
  //     state.amount = resp.page
  //     state.pagination.total = Number(resp.page?.count)
  //   })
  // }

  await getCooperationJobList({
    ...state.formData,
    ...sortQuery.value,
    majorId:
      state.formData.majorId instanceof Array
        ? state.formData.majorId.join()
        : state.formData.majorId
  }).then((resp: any) => {
    if (!state.formData.export) {
      state.list = resp.list
      state.pagination.total = Number(resp.page?.count)
    }
  })

  state.loading = false
  getTableHeight()
}

const openDialogWelfare = () => {
  dialogWelfare.value.openDialog([])
}

const handleWelfare = async (welfare: any) => {
  const batchSelectJobIds = state.jobSelection.map((item: any) => item.jobId)?.join(',')
  const welfareTag = welfare.map((item: any) => item.k).join(',')
  state.welfareArray = welfare
  await batchEditWelfare({
    jobIds: batchSelectJobIds,
    welfareIds: welfareTag
  }).then(() => {
    getList()
  })
}

function messageBox(title, message, callback) {
  ElMessageBox({
    title,
    message,
    showCancelButton: true,
    beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        callback()
          .then(() => {
            instance.confirmButtonLoading = false
            done()
            getList()
          })
          .catch(() => {
            instance.confirmButtonLoading = false
          })
      } else {
        done()
      }
    }
  })
}

const handleSortTable = ({ prop, order }) => {
  // 倒序：descending， 正序：ascending
  // 1:倒序；2:正序
  const sortMap = {
    click: 'sortClick',
    applyTotal: 'sortApplyTotal',
    amount: 'sortAmount',
    firstReleaseTimeDate: 'sortFirstReleaseTime',
    realRefreshTimeDate: 'sortRealRefreshTime',
    addTimeDate: 'sortAddTime',
    offlineTimeDate: 'sortOfflineTime'
  }

  const key = sortMap[prop]
  const sort = order === 'ascending' ? 2 : 1
  sortQuery.value = order ? { [key]: sort } : {}
  getList()
}

const handleSelectionChange = (data: any) => {
  state.jobSelection = data
  if (data.length === state.list.length) {
    state.isCheckAll = true
    isIndeterminate.value = false
  } else {
    state.isCheckAll = false
    isIndeterminate.value = data.length > 0
  }
}

const handleSearch = (filter: any) => {
  state.formData = {
    ...state.formData,
    ...filter,
    page: 1
  }
  getList()
}

const handlePaginationChange = (data: any) => {
  state.formData.page = data.page
  state.formData.limit = data.limit
  getList()
}

const handleOpenCustomColumn = () => {
  customColumnDialog.value.open('jobQuery')
}

const openLogDialog = (id: string) => {
  logDialog.value.open(id)
}

const openDeliveryInvite = (row: object) => {
  deliveryInvite.value.open(row)
}

const handleEdit = (id: string) => {
  router.push({
    path: `/job/edit/${id}`
  })
}

const openBusiness = (jobId: string) => {
  router.push({
    path: `/job/business`,
    query: { jobId }
  })
}

const handleRefresh = (id: string) => {
  messageBox('提示', '是否刷新该职位？', async () => {
    await jobRefresh({ jobId: id })
  })
}

const handleReleaseAgain = (id: string) => {
  messageBox('提示', '确认再次发布吗？', async () => {
    await jobReleaseAgain({ jobId: id })
  })
}

const handleDelete = (id: string) => {
  messageBox('提示', '删除后无法恢复，确定删除吗？', async () => {
    await jobDelete({ jobId: id })
  })
}

const handleOfflinePrompt = (callback) => {
  ElMessageBox.prompt('请填写职位下线原因', '提示', {
    showCancelButton: true,
    inputPattern: /[\S]/,
    inputErrorMessage: '请填写职位下线原因',
    beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        const { inputValue } = instance
        callback(inputValue, instance, done)
      } else {
        done()
      }
    }
  })
}

const handleEstablishment = () => {
  state.dialogFormVisible = true
}

const handleOffline = async (id: string) => {
  await handleOfflinePrompt(async (value, instance, done) => {
    try {
      await jobOffline({ jobId: id, reason: value })
      instance.confirmButtonLoading = false
      getList()
      done()
    } catch {
      instance.confirmButtonLoading = false
    }
  })
}

const handleChangeJobShow = (id: string, isShow: any) => {
  const message = `确认${isShow === 2 ? '隐藏' : '显示'}该职位吗？`
  messageBox('提示', message, async () => {
    await changeJobShow({ jobId: id, isShow })
  })
}

const jobSelectChange = () => {
  jobTable.value.toggleAllSelection()
}

const batchCatch = async (api: Function, params: Object = {}) => {
  const { jobSelection } = state
  const ids = jobSelection.map((item: any) => item.jobId).join()
  const { content } = await api({ jobId: ids, ...params })
  if (content) {
    ElMessageBox.alert(content, '提示', {
      dangerouslyUseHTMLString: true,
      showConfirmButton: false
    })
  }
  state.batchValue = null
  getList()
}

const jobSelectBatch = async (val) => {
  currentOperationType.value = 2
  const options = {
    1: batchDelDeliveryEducationLimit,
    2: batchAddDeliveryEducationLimit,
    3: batchDelDeliveryFileLimit,
    4: batchAddDeliveryFileLimit,
    5: batchJobRefresh,
    6: batchChangeJobHide,
    7: batchChangeJobShow,
    8: batchJobReleaseAgain,
    9: batchJobOffline,
    11: batchJobEstablishment,
    12: batchEditWelfare
  }

  state.batchValue = null

  if (options[val]) {
    if (val === 9) {
      handleOfflinePrompt(async (value, instance, done) => {
        try {
          await batchCatch(options[val], { reason: value })
          done()
        } finally {
          instance.confirmButtonLoading = false
        }
      })
      return
    }

    if (val === 11) {
      establishmentJobId.value = state.jobSelection.map((item: any) => item.jobId)?.join(',')
      handleEstablishment()
      return
    }

    if (val === 12) {
      const { jobSelection } = state
      const jobIds = jobSelection.map((item: any) => item.jobId).join()
      const res = await batchEditWelfareCheck({ jobIds })
      if (res.companyMemberId) {
        state.memberId = res.companyMemberId
        openDialogWelfare()
      }
      return
    }

    batchCatch(options[val])
  }

  if (val === 10) {
    const companyIds = state.jobSelection.map((item: any) => item.companyId)
    const subUsed = state.jobSelection.every((item: any) => item.subUsed === 0)
    const isSame = companyIds.every((item: any) => item === companyIds[0])
    state.companyId = <any>companyIds[0]
    state.checkCompanyName = state.jobSelection[0].companyName

    if (!isSame) {
      ElMessage.warning('请选择同一单位的职位！')
      return
    }

    if (subUsed) {
      ElMessage.warning('请先添加子账号！')
      return
    }
    state.accountVisible = true
  }
}

const handleAccount = async () => {
  const { jobContactSynergyIds, ...other } = state.accountFrom

  const isSimple = currentOperationType.value === 1
  const fetch = isSimple ? changeJobContact : accountBatchEdit

  const batchSelectJobIds = state.jobSelection.map((item: any) => item.jobId)?.join(',')

  const params = {
    ...other,
    jobContactSynergyIds: jobContactSynergyIds.join(),
    jobId: isSimple ? currentOperationJobId.value : batchSelectJobIds
  }

  await fetch(params)

  state.accountVisible = false
  getList()
}

const handleJobDetail = (id: string) => {
  jobDetail.value.open(id)
}

const handleChangeLimit = (jobId, isLimit, changeLimit) => {
  const data = { jobId, isLimit }
  const message = `确认${isLimit === 1 ? '添加' : '取消'}该职位限制吗？`
  messageBox('提示', message, async () => {
    await changeLimit(data)
  })
}

const handleChangeAcademicQualifications = async (jobId, isLimit) => {
  handleChangeLimit(jobId, isLimit, changeDeliveryEducationLimit)
}

const handleChangeAttachmentRestrictions = (jobId, isLimit) => {
  handleChangeLimit(jobId, isLimit, changeDeliveryFileLimit)
}

const handleAccountDialogClose = () => {
  state.accountFrom.jobContactId = ''
  state.accountFrom.jobContactSynergyIds = []
  state.accountVisible = false
}

const setColumnMinWidth = (key: string) => {
  let minWidth = 90

  switch (key) {
    case 'announcementTitle':
      minWidth = 210
      break
    case 'company':
      minWidth = 210
      break
    case 'applyTotal':
      minWidth = 110
      break
    case 'amount':
      minWidth = 110
      break
    case 'firstReleaseTimeStart':
      minWidth = 140
      break
    case 'click':
      minWidth = 100
      break
    case 'refreshTime':
      minWidth = 110
      break
    case 'addTime':
      minWidth = 110
      break
    case 'periodDate':
      minWidth = 110
      break
    case 'contact':
      minWidth = 110
      break
    case 'majorId':
      minWidth = 210
      break
    case 'contactSynergyNum':
      minWidth = 110
      break
    case 'contactSynergyInfo':
      minWidth = 110
      break
    case 'isMiniappTxt':
      minWidth = 110
      break
    case 'operation':
      minWidth = 210
      break

    default:
      minWidth = 90
      break
  }

  return minWidth
}

const openJobContact = (row: any) => {
  const { companyId, companyName } = row
  state.companyId = companyId
  state.checkCompanyName = companyName
  state.accountChangeType = 1
  state.accountVisible = true
}

const beforeJobOfflineCheck = (jobId) => {
  jobOfflineCheck({ jobId }).then((resp) => {
    if (resp.msg) {
      ElMessageBox({
        title: '提示',
        message: resp.msg,
        showCancelButton: true,
        async beforeClose(action, instance, done) {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            try {
              await handleOffline(jobId)
              getList()
              instance.confirmButtonLoading = false
              done()
            } catch (error) {
              instance.confirmButtonLoading = false
            }
          } else {
            done()
          }
        }
      })
    } else {
      handleOffline(jobId)
    }
  })
}

const beforeJobHideCheck = (jobId) => {
  jobHideCheck({ jobId }).then((resp) => {
    if (resp.msg) {
      ElMessageBox({
        title: '提示',
        message: resp.msg,
        showCancelButton: true,
        async beforeClose(action, instance, done) {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            try {
              await changeJobShow({ jobId, isShow: 2 })
              instance.confirmButtonLoading = false
              done()
              getList()
            } catch (error) {
              instance.confirmButtonLoading = false
            }
          } else {
            done()
          }
        }
      })
    } else {
      handleChangeJobShow(jobId, 2)
    }
  })
}

const btnGroupEvent = async (key: string, row: any) => {
  const { jobId } = row
  currentOperationType.value = 1
  currentOperationJobId.value = jobId
  switch (key) {
    // 业务
    case 'business':
      await openBusiness(jobId)
      break
    // 刷新
    case 'refresh':
      await handleRefresh(jobId)
      break
    // 隐藏
    case 'hide':
      await beforeJobHideCheck(jobId)
      break
    // 显示
    case 'show':
      await handleChangeJobShow(jobId, 1)
      break
    // 日志
    case 'log':
      await openLogDialog(jobId)
      break
    // 编辑
    case 'edit':
      await handleEdit(jobId)
      break
    // 下线
    case 'offline':
      await beforeJobOfflineCheck(jobId)
      break
    // 再发布
    case 'republish':
      await handleReleaseAgain(jobId)
      break
    // 删除
    case 'delete':
      await handleDelete(jobId)
      break
    // 添加学历限制
    case 'addEducation':
      await handleChangeAcademicQualifications(jobId, 1)
      break
    // 取消学历限制
    case 'removeEducation':
      await handleChangeAcademicQualifications(jobId, 2)
      break
    // 添加附件限制
    case 'addAttachment':
      await handleChangeAttachmentRestrictions(jobId, 1)
      break
    // 取消附件限制
    case 'removeAttachment':
      await handleChangeAttachmentRestrictions(jobId, 2)
      break
    // 投递邀约
    case 'invite':
      await openDeliveryInvite(row)
      break
    // 协同子账号&联系人设置
    case 'contact':
      await openJobContact(row)
      break
    // 编制设置
    case 'establishment':
      establishmentJobId.value = jobId
      await handleEstablishment()
      break

    default:
      break
  }
}
</script>

<script lang="ts">
export default {
  name: 'jobQuery'
}
</script>

<style scoped lang="scss">
:deep() {
  @import '/@/theme/tableScrollBar.scss';
}

.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}

.amount {
  margin: 20px 0 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;

  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}

.radio-box {
  margin-left: 20px;
}

.radio-item-box {
  margin-left: 20px;
  margin-bottom: 20px;
}
.dialog-footer {
  text-align: center;
}
</style>
