<template>
  <div class="box">
    <el-form ref="form" label-width="70px" :model="formData">
      <div class="flex">
        <el-form-item class="span-5" label="资讯检索" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请填写资讯标题或编号"
            clearable
            @keyup.enter="getList"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-5" label="所属栏目" prop="homeColumnId">
          <el-cascader
            clearable
            placeholder="请选择所属栏目"
            :show-all-levels="false"
            v-model="formData.homeColumnId"
            :options="homeColumnList"
            :props="{ value: 'k', label: 'v', emitPath: false }"
          ></el-cascader>
        </el-form-item>
        <el-form-item class="span-5" label="发 布 人" prop="author">
          <el-input
            v-model="formData.author"
            placeholder="请填写发布人信息"
            clearable
            @keyup.enter="getList"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-5" label="发布时间" prop="refreshTimeStart">
          <DatePickerRange
            v-model:start="formData.refreshTimeStart"
            v-model:end="formData.refreshTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-5" label="更新时间" prop="updateTimeStart">
          <DatePickerRange
            v-model:start="formData.updateTimeStart"
            v-model:end="formData.updateTimeEnd"
          />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-5" label="审核状态" prop="status">
          <el-select v-model="formData.status" filterable clearable>
            <el-option
              v-for="item in statusList"
              :label="item.v"
              :key="item.k"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="span-5" label="调用站点" label-width="94px" prop="useSiteType">
          <el-select
            placeholder="请选择调用站点"
            v-model="formData.useSiteType"
            filterable
            clearable
          >
            <el-option
              v-for="item in useSiteTypeList"
              :key="item.v"
              :label="item.v"
              :value="item.k"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="span-5" label="资讯属性" prop="homeColumnId">
          <el-cascader
            clearable
            placeholder="请选择资讯属性"
            collapse-tags
            v-model="formData.attribute"
            :options="attributeList"
            :props="{ value: 'k', label: 'v', emitPath: false, multiple: true }"
          ></el-cascader>
        </el-form-item>
        <el-form-item class="span-5" label-width="10px">
          <div class="nowrap">
            <el-button type="primary" @click="getList">搜索</el-button>
            <el-button @click="handleResetField">重置</el-button>
          </div>
        </el-form-item>
      </div>
      <div>
        <el-form-item class="span-5" label-width="10px">
          <el-button type="primary" @click="handleAddNews">+ 发布资讯</el-button>
        </el-form-item>
      </div>
    </el-form>
    <el-table
      :data="list"
      border
      size="small"
      ref="table"
      v-loading="loading"
      @sort-change="handleSortable"
      @cell-mouse-enter="handleCellMouseEnter"
      @cell-mouse-leave="handleCellMouseLeave"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column
        prop="newsId"
        label="资讯编号"
        align="center"
        header-align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="title"
        label="资讯标题"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="homeColumnIdTitle"
        label="所属栏目"
        align="center"
        header-align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        :width="100"
        prop="homeSubColumnIdsTitle"
        label="副栏目"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.homeSubColumnIdsTitle }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="排序"
        prop="company"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div>
            <div v-show="row.newsId === sortEditId">
              onkeyup="value=value.replace(/[^\d]/g,'')"
              <el-input
                :input-style="{ 'text-align': 'center' }"
                v-model.trim="row.sort"
                @keydown.enter="handleSort(row.newsId, row.sort)"
              ></el-input>
            </div>
            <span v-show="row.newsId !== sortEditId">{{ row.sort }}</span>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column
        label="点击"
        prop="sortClick"
        align="center"
        header-align="center"
        sortable="custom"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.click }}</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="sortApplyNum"
        label="审核状态"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.statusTitle }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sortRefreshTime"
        label="发布时间"
        align="center"
        header-align="center"
        sortable="custom"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.refreshTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sortUpdateTime"
        label="更新时间"
        align="center"
        header-align="center"
        sortable="custom"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.updateTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="creator"
        label="发布人"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="useSiteTypeText"
        label="调用站点"
        align="center"
        header-align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" align="center" header-align="center" min-width="150px">
        <template #default="{ row }">
          <div class="jc-end">
            <el-button
              v-if="['7'].includes(row.status)"
              @click="handleAudDialog(row.newsId)"
              type="primary"
              size="small"
              >审核</el-button
            >
            <el-button
              v-if="['1', '-1', '3'].includes(row.status)"
              @click="handleEdit(row.newsId)"
              size="small"
              >编辑</el-button
            >
            <el-button @click="handleDelete(row.newsId)" size="small">删除</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>
    <div v-show="list.length" class="jc-between mt-15 pl-10">
      <div class="ai-center">
        <el-checkbox
          v-model="checkedAll"
          @change="handleChange"
          class="mr-10"
          label="全选"
        ></el-checkbox>
        <el-select :disabled="!multipleSelection.length" v-model="batchId" filterable clearable>
          <el-option
            v-for="item in batchList"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          ></el-option>
        </el-select>
      </div>
      <Pagination :total="pagination.total" @change="handlePaginationChange" />
    </div>
    <OperationDialog @update="getList" :columnList="homeColumnList" ref="operationDialog" />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, onMounted, ref, watch, defineComponent } from 'vue'
import { useRouter } from 'vue-router'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Pagination from '/@/components/base/paging.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import OperationDialog from './component/operationDialog.vue'

import { debounce } from 'throttle-debounce'

import { getNewsList, deleteNews, getListGetParams } from '/@/api/news'

export default defineComponent({
  name: 'cmsNewsList',
  components: { DatePickerRange, Pagination, OperationDialog },
  setup() {
    const form = ref()
    const table = ref()
    const operationDialog = ref()

    const router = useRouter()

    const state = reactive({
      loading: false,
      submitLoading: false,
      // 审核状态
      statusList: [],
      useSiteTypeList: [],
      attributeList: [],
      homeColumnList: [],
      batchList: [
        {
          k: '1',
          v: '编辑属性'
        },
        {
          k: '2',
          v: '复制文档'
        },
        {
          k: '3',
          v: '移动文档'
        },
        {
          k: '4',
          v: '删除文档'
        },
        {
          k: '5',
          v: '文档审核'
        }
      ],

      batchId: <any>'',
      checkedAll: false,

      visible: false,
      dialogTitle: '批量编辑属性',

      // 当前排序ID
      sortEditId: '',

      // 选中项
      multipleSelection: [],

      formData: {
        title: '',
        homeColumnId: '',
        author: '',
        useSiteType: '',
        attribute: [],
        refreshTimeStart: '',
        releaseTimeEnd: '',
        releaseTimeStart: '',
        refreshTimeEnd: '',
        status: '',
        limit: 20,
        page: 1
      },
      pagination: {
        total: 0
      },
      list: []
    })
    const getSelectList = async () => {
      const { useSiteTypeList, attributeList, statusList, columnList } = <any>(
        await getListGetParams({})
      )
      state.useSiteTypeList = useSiteTypeList
      state.attributeList = attributeList
      state.statusList = statusList
      state.homeColumnList = columnList
    }

    const getList = () => {
      state.loading = true
      const attribute = state.formData.attribute.join(',')
      getNewsList({ ...state.formData, attribute }).then((resp: any) => {
        state.pagination.total = resp.page.count
        state.list = resp.list.map((item: any) => {
          return {
            ...item,
            homeSubColumnIdsTitle: item.homeSubColumnIdsTitle.map((i: any) => i.name).join(',')
          }
        })
        state.loading = false
      })
    }

    onMounted(() => {
      getList()
      getSelectList()
    })

    const handleResetField = () => {
      form.value.resetFields()
      state.formData.attribute = []
      getList()
    }

    const handlePaginationChange = (data: any) => {
      state.formData.page = data.page
      state.formData.limit = data.limit
      getList()
    }

    const handleSortable = ({ prop, order }) => {
      Reflect.deleteProperty(state.formData, 'sortClick')
      Reflect.deleteProperty(state.formData, 'sortRefreshTime')
      Reflect.deleteProperty(state.formData, 'sortRefreshTime')
      if (order === 'ascending') {
        // 正序
        state.formData[prop] = 2
      } else if (order === 'descending') {
        state.formData[prop] = 1
      }
      getList()
    }

    // 鼠标移入单元格时
    const handleCellMouseEnter = (row, column) => {
      if (column.label === '排序') {
        state.sortEditId = row.newsId
      }
    }
    // 鼠标移出单元格时
    const handleCellMouseLeave = () => {
      state.sortEditId = ''
    }
    // const changeSort = (data: any) => {
    const changeSort = () => {
      // changeHomePositionSort(data)
    }
    // 添加防抖
    const debounceFn = debounce(3000, true, changeSort)
    // 修改排序
    const handleSort = (id, sort) => {
      debounceFn({ id, sort })
    }

    const handleSelectionChange = (val) => {
      state.multipleSelection = val
      if (val.length === state.list.length) {
        state.checkedAll = true
      } else {
        state.checkedAll = false
      }
    }
    // 是否全选
    const handleChange = () => {
      table.value.toggleAllSelection()
    }
    const handleAudDialog = (id: string) => {
      operationDialog.value.open('5', id, '审核文档')
    }

    const handleEdit = (id: string) => {
      router.push(`/cms/newsEdit/${id}`)
    }
    const handleAddNews = () => {
      router.push('/cms/newsAdd')
    }

    const handleDelete = (id: string) => {
      ElMessageBox({
        title: '提示',
        message: '此操作将永久删除选中的数据，是否继续？',
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // eslint-disable-next-line no-param-reassign
            instance.confirmButtonLoading = true
            deleteNews({ newsIds: id })
              .then(() => {
                // eslint-disable-next-line no-param-reassign
                instance.confirmButtonLoading = false
                done()
                getList()
              })
              .catch(() => {
                // eslint-disable-next-line no-param-reassign
                instance.confirmButtonLoading = false
              })
          } else {
            done()
          }
        }
      })
    }

    watch(
      () => state.batchId,
      (val: any) => {
        if (!val) return
        const newsIds = state.multipleSelection.map((item: any) => item.newsId).join(',')
        if (['1', '2', '3', '5'].includes(val)) {
          if (val === '2' && state.multipleSelection.length > 10) {
            ElMessage({
              type: 'error',
              message: '复制文档不能超过10条'
            })
            return
          }
          operationDialog.value.open(val, newsIds)
        } else {
          handleDelete(newsIds)
        }
      }
    )

    return {
      form,
      table,
      operationDialog,
      handleAddNews,
      handleResetField,
      getList,
      handlePaginationChange,
      handleSortable,
      handleCellMouseEnter,
      handleCellMouseLeave,
      handleSelectionChange,
      handleChange,
      handleAudDialog,
      handleEdit,
      handleDelete,
      handleSort,
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}
</style>
