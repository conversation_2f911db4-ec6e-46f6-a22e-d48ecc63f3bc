<template>
  <div class="main" v-loading="loading">
    <el-form :model="formData" label-width="95px" :rules="rules" ref="form">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card class="h100">
            <div class="flex">
              <el-form-item class="span-2" label="资讯标题" prop="title">
                <el-input v-model="formData.title" placeholder="请填写资讯标题"></el-input>
              </el-form-item>
              <el-form-item class="span-2" label="所属栏目" prop="homeColumnId">
                <el-cascader
                  placeholder="请选择所属栏目"
                  v-model="formData.homeColumnId"
                  class="block w100"
                  filterable
                  :show-all-levels="false"
                  :options="columnList"
                  collapse-tags
                  :props="{ value: 'k', label: 'v', checkStrictly: true, emitPath: false }"
                  clearable
                />
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                class="span-2"
                label="副 栏 目"
                label-width="94px"
                prop="homeSubColumnIds"
              >
                <el-cascader
                  placeholder="请选择副栏目"
                  v-model="formData.homeSubColumnIds"
                  class="w100"
                  filterable
                  :show-all-levels="false"
                  :options="subColumnList"
                  collapse-tags
                  :props="{ value: 'k', label: 'v', multiple: true, emitPath: false }"
                  clearable
                />
              </el-form-item>
              <el-form-item class="span-2" label="调用站点" label-width="94px" prop="useSiteType">
                <el-select
                  placeholder="请选择调用站点"
                  v-model="formData.useSiteType"
                  class="w100"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in useSiteTypeList"
                    :key="item.v"
                    :label="item.v"
                    :value="item.k"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item class="flex-1" label="资讯详情"> </el-form-item>
            </div>

            <WangEditor ref="editorRef" v-model="formData.content" :height="550" />
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="h100">
            <template #header>
              <div class="card-header">高级属性</div>
            </template>
            <el-form-item class="flex-1" label="文档属性：" prop="attribute">
              <el-checkbox-group v-model="formData.attribute">
                <el-checkbox v-for="item in attributeList" :label="item.value">{{
                  item.label
                }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <div>高才海外相关属性：</div>
            <el-form-item class="flex-1" prop="abroadAttribute">
              <el-checkbox-group v-model="formData.abroadAttribute">
                <el-checkbox v-for="item in abroadAttributeList" :label="item.value">{{
                  item.label
                }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item
              v-if="formData.attribute.includes('13')"
              class="flex-1"
              label="跳转链接："
              prop="linkUrl"
            >
              <el-input v-model="formData.linkUrl" placeholder="请填写跳转链接"></el-input>
            </el-form-item>
            <el-form-item class="flex-1" label="来源：" prop="original">
              <el-input v-model="formData.original" placeholder="请填写来源"></el-input>
            </el-form-item>
            <el-form-item class="flex-1" label="作者：" prop="author">
              <el-input v-model="formData.author" placeholder="请填写作者"></el-input>
            </el-form-item>
            <el-form-item class="flex-1" label="网址：" prop="originalUrl">
              <el-input v-model="formData.originalUrl" placeholder="请填写网址"></el-input>
            </el-form-item>
            <el-form-item class="flex-1" label="标签：" prop="tagIds">
              <el-input v-model="formData.tagIds" placeholder="请填写TAG标签"></el-input>
            </el-form-item>
            <el-form-item class="flex-1" label="摘要：" prop="seoDescription">
              <el-input
                type="textarea"
                rows="3"
                resize="none"
                v-model="formData.seoDescription"
                placeholder="请填写资讯摘要"
              ></el-input>
            </el-form-item>
            <el-form-item label="关键词：" prop="seoKeywords">
              <el-input
                v-model="formData.seoKeywords"
                type="textarea"
                rows="4"
                resize="none"
                placeholder='多个关键词用英文逗号","隔开'
              ></el-input>
            </el-form-item>
            <el-form-item label="正文头图：" prop="coverThumb">
              <div class="ai-center">
                <el-upload
                  class="avatar-uploader jc-start"
                  action="/upload/image"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload"
                >
                  <img v-if="formData.coverThumb" :src="formData.coverThumb" class="avatar" />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
                <el-link
                  v-show="formData.coverThumb"
                  @click="handleClearCoverThumb"
                  type="primary"
                  class="ml-10"
                  :underline="false"
                  >清除</el-link
                >
              </div>
            </el-form-item>
          </el-card>
        </el-col>
      </el-row>
      <el-row class="mt-15">
        <el-col :span="16">
          <div class="jc-center">
            <el-button @click="submit(1)" type="primary">发布</el-button>
            <el-button @click="submit(3)">保存</el-button>
            <el-button v-if="formData.id" @click="handlePreview">保存并预览</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts">
import { onMounted, reactive, ref, toRefs, defineComponent, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import {
  getHomeColumnList,
  newsAdd,
  getAttributeNewsList,
  getAbroadAttributeNewsList,
  getNewsDetails
} from '/@/api/news'
import { getUseSiteTypeList } from '/@/api/config'
import WangEditor from '/@/components/wangEditor/index.vue'

export default defineComponent({
  components: { WangEditor },
  name: 'cmsNewsAdd',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const form = ref()
    const upload = ref()
    const editorRef = ref()

    const state = reactive({
      loading: false,
      columnList: [],
      subColumnList: [],
      attributeList: <any>[],
      abroadAttributeList: <any>[],
      useSiteTypeList: <any>[],
      formData: <any>{
        title: '',
        homeColumnId: [],
        homeSubColumnIds: [],
        useSiteType: '',
        content: '',
        attribute: [],
        abroadAttribute: [],
        author: '',
        original: '',
        originalUrl: '',
        linkUrl: '',
        coverThumb: '',
        seoDescription: '',
        seoKeywords: '',
        listThumb: '',
        status: '',
        tagIds: '',
        isPreview: ''
      },
      rules: {
        title: [
          {
            required: true,
            message: '请输入资讯标题',
            trigger: 'blur'
          }
        ],
        homeColumnId: [
          {
            required: true,
            message: '请选择所属栏目',
            trigger: 'change'
          }
        ],
        linkUrl: [
          {
            required: true,
            message: '请填写跳转链接',
            trigger: 'change'
          }
        ]
      }
    })

    const getDetails = async (id: any) => {
      state.loading = true
      getNewsDetails({ newsId: id }).then((resp: any) => {
        nextTick(() => {
          state.formData = resp
          const stringToArray = ['homeSubColumnIds', 'attribute', 'abroadAttribute']
          stringToArray.forEach((key) => {
            const value = resp[key]
            state.formData[key] = value ? value.split(',') : []
          })
          state.formData.id = id
          state.formData.useSiteType = resp.useSiteType && resp.useSiteType - 0
          state.loading = false
        })
      })
    }
    const getColumnList = () => {
      getHomeColumnList().then((resp: any) => {
        const { columnList, subColumnList } = resp
        state.columnList = columnList
        state.subColumnList = subColumnList
      })
    }

    const getAttributeList = async () => {
      state.attributeList = await (<any>getAttributeNewsList())
    }

    const getAbroadAttributeList = async () => {
      state.abroadAttributeList = await (<any>getAbroadAttributeNewsList())
    }

    const handleGetUseSiteTypeList = async () => {
      state.useSiteTypeList = await (<any>getUseSiteTypeList({}))
    }

    // 页面加载时
    onMounted(() => {
      nextTick(() => {
        getAttributeList()
        getAbroadAttributeList()
        getColumnList()
        handleGetUseSiteTypeList()
        const { id } = route.params
        if (id) {
          state.formData.id = id
          getDetails(id)
        }
      })
    })

    const resetFields = () => {
      editorRef.value.clearEditor()
      form.value.resetFields()
      nextTick(() => {
        form.value.clearValidate()
      })
    }

    const submit = (status: Number | String = '') => {
      // status: 	保存：3；发布：1
      form.value.validate((valid: Boolean) => {
        const isPreview = state.formData.isPreview === '1'
        if (!valid) return

        const postData = {
          ...state.formData,
          status
        }

        const arrayToString = ['homeSubColumnIds', 'attribute', 'abroadAttribute']
        arrayToString.forEach((key) => {
          const value = postData[key]
          postData[key] = value.join(',')
        })

        newsAdd(postData).then((r) => {
          if (status === 1 && !state.formData.id) {
            resetFields()
          }
          if (!state.formData.id && r.newsId) {
            // 新增成功
            // 去到编辑页面
            router.replace(`/cms/newsEdit/${r.newsId}`)
            return
          }

          if (isPreview) {
            getDetails(state.formData.id)
            window.open(r.previewUrl, '_blank')
          }
        })
        state.formData.isPreview = ''
      })
    }

    const handlePreview = () => {
      state.formData.isPreview = '1'
      submit('3')
    }

    // 略缩图上传
    const beforeAvatarUpload = (file: any) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        ElMessage.error('上传图片只能是 JPG或PNG 格式!')
      }
      if (!isLt5M) {
        ElMessage.error('图片上传不能超过5M')
      }
      return isJPG && isLt5M
    }

    // 上传正文头图
    const handleAvatarSuccess = ({ data }: any) => {
      state.formData.coverThumb = data.fullUrl
    }
    // 清除正文头图
    const handleClearCoverThumb = () => {
      state.formData.coverThumb = ''
    }

    return {
      form,
      submit,
      beforeAvatarUpload,
      handleAvatarSuccess,
      handlePreview,
      handleClearCoverThumb,
      upload,
      editorRef,
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
.main {
  background-color: #fff;
  border-radius: 5px;
  padding: 20px;
}
.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
:deep(.el-upload--text) {
  display: flex;
  align-items: center;
  justify-content: space-around;
  .avatar-uploader-icon {
    width: 100px;
    height: 100px;
  }
  .logo {
    font-size: 12px;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
