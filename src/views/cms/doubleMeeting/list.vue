<template>
  <div class="formbigbox">
    <el-form ref="form" :model="formData" label-width="70px" :size="formSize">
      <div class="flex">
        <el-form-item class="span-4" label="表单检索">
          <el-input
            class="w100"
            v-model="formData.name"
            placeholder="请输入表单名称或编号"
            clearable
            @keyup.enter="search()"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-4" label="创建时间" prop="addTimeStart">
          <DatePickerRange
            :size="formSize"
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item class="span-4" label-width="10px">
          <el-button type="primary" @click="search()">搜索</el-button>
          <el-button type="default" @click="handleAddActivity()">新增报名表</el-button>
        </el-form-item>
      </div>
    </el-form>
    <el-table ref="table" :data="tableData" border v-loading="loading" size="small" stripe>
      <el-table-column
        prop="id"
        label="表单ID"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column prop="name" label="表单名称" align="center" header-align="center" />
      <el-table-column
        prop="addTime"
        label="创建时间"
        align="center"
        header-align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="registrationFormNumber"
        label="表单数量"
        align="center"
        header-align="center"
      />
      <el-table-column prop="code" label="表单代码" align="center" header-align="center" />
      <el-table-column
        label="操作"
        align="center"
        header-align="center"
        min-width="150px"
        fixed="right"
      >
        <template #default="{ row }">
          <div>
            <el-link type="primary" :underline="false">
              <router-link class="edit-btn" :to="`/cms/double-meeting/${row.id}`">编辑</router-link>
            </el-link>
            &nbsp;
            <el-link type="primary" @click="handleCopy(row.link)" :underline="false"
              >报名链接</el-link
            >
            &nbsp;
            <el-link type="primary" @click="showSignList(row)" :underline="false">签到链接</el-link>
            &nbsp;
            <el-link
              :disabled="row.registrationFormNumber == 0"
              :type="row.registrationFormNumber == 0 ? 'info' : 'primary'"
              :underline="false"
              @click="handleExportData(row)"
              >导出数据</el-link
            >
            &nbsp;
            <el-link
              type="primary"
              v-if="row.registrationFormNumber == 0"
              :underline="false"
              @click="handleDelete(row.id)"
              >删除</el-link
            >
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty></el-empty>
      </template>
    </el-table>
    <div v-show="tableData.length" class="mt-15">
      <Paging :total="total" :page="formData.page" @change="changePage"></Paging>
    </div>

    <el-dialog
      v-model="exportVisible"
      title="导出数据"
      width="550px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form>
        <el-form-item label="时间段" label-width="50px">
          <el-date-picker
            v-model="exportDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :shortcuts="[
              {
                text: '最近一周',
                value: () => {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                  return [start, end]
                }
              },
              {
                text: '最近一个月',
                value: () => {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                  return [start, end]
                }
              },
              {
                text: '最近三个月',
                value: () => {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                  return [start, end]
                }
              },
              {
                text: '本周',
                value: () => {
                  // 从周一到现在
                  const end = new Date()
                  const start = new Date()
                  start.setDate(start.getDate() - start.getDay())
                  return [start, end]
                }
              },
              {
                text: '本月',
                value: () => {
                  // 从1号到现在
                  const end = new Date()
                  const start = new Date()
                  start.setDate(1)
                  return [start, end]
                }
              }
            ]"
          />
        </el-form-item>
        <el-form-item label-width="50px">
          <p>* 若未选择时间段，则导出全量数据</p>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer-buttons">
          <div class="button-row">
            <el-button type="primary" @click="handleDownloadExcel">下载excel</el-button>
            <el-button type="primary" @click="renameVisible = true">下载附件</el-button>
            <el-button type="primary" @click="showSelectAttachmentDialog">选择下载附件</el-button>
            <el-button type="primary" @click="showDownloadSignList">下载签到数据</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="renameVisible" title="下载附件" width="500px">
      <el-form>
        <el-form-item label="命名方式">
          <el-select v-model="renameMode" style="width: 100%">
            <el-option label="文件命名规则" value="default" />
            <el-option label="自定义命名" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="renameMode === 'default'" label="文件命名规则">
          <el-select v-model="renameType" placeholder="请选择命名规则">
            <el-option
              v-for="item in renameTypeOption"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="renameMode === 'custom'" label="自定义命名字段">
          <el-select
            v-model="renameFields"
            multiple
            filterable
            placeholder="请选择命名字段（可多选并排序）"
            style="width: 100%"
          >
            <el-option
              v-for="item in customRenameFields"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div>
        <template v-if="renameMode === 'default'">
          默认文件名规则示例：序号_姓名_附件n_{附件名称}
        </template>
        <template v-else>
          自定义命名规则示例：
          <span v-for="(f, idx) in renameFields" :key="f">
            {{ getFieldLabel(f) }}<span v-if="idx !== renameFields.length - 1">-</span>
          </span>
        </template>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleDownload">立即下载</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="signOptionVisible" title="签到链接" width="600px">
      <div>请选择本活动的签到选项/场次：</div>
      <br />
      <el-select v-model="signOptionId" class="sign-option" filterable>
        <el-option v-for="item in signOptionList" :key="item.k" :label="item.v" :value="item.k" />
      </el-select>
      <br />
      <br />
      <div v-show="signOptionId">
        <div>
          <span>签到链接</span>&nbsp;<el-button type="primary" @click="hanCopySignUrl(signUrl)"
            >复制</el-button
          >
        </div>
        <br />
        <div>
          <a
            :href="signUrl"
            target="_blank"
            style="color: #409eff; display: block; text-decoration: none"
            >{{ signUrl }}</a
          >
        </div>
        <br />
        <div>
          <span>签到码</span>&nbsp;<el-button
            type="primary"
            @click="hanDownloadSignCodeUrl(signCodeUrl)"
            >下载</el-button
          >
        </div>
        <img :src="signCodeUrl" />
      </div>
    </el-dialog>

    <!-- 下载签到数据 -->
    <el-dialog v-model="downSignOptionVisible" title="签到链接" width="600px">
      <div>请选择本活动的签到选项/场次：</div>
      <br />
      <el-select v-model="downSignOptionId" class="sign-option" filterable>
        <el-option
          v-for="item in downSignOptionList"
          :key="item.k"
          :label="item.v"
          :value="item.k"
        />
      </el-select>
      <div v-show="downSignOptionId != ''">
        <br />
        <p v-show="!haveSign" style="color: red">⚠️ 该选项暂无报名数据，不支持下载</p>
        <el-button :disabled="!haveSign" type="primary" @click="handelDownloadSignExcel()"
          >立即下载</el-button
        >
      </div>
    </el-dialog>

    <!-- 选择下载附件 -->
    <el-dialog
      v-model="selectAttachmentVisible"
      title="选择下载附件"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="select-attachment-content">
        <el-row :gutter="20">
          <!-- 左侧：人员列表 -->
          <el-col :span="16">
            <div class="left-panel">
              <div class="search-section">
                <el-form :inline="true" :model="attachmentSearchForm" size="small">
                  <el-form-item label="搜索">
                    <el-input
                      v-model="attachmentSearchForm.keyword"
                      placeholder="请输入姓名、用户ID、学校、专业等关键词"
                      clearable
                      @keyup.enter="searchRegistrations"
                      style="width: 200px"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="searchRegistrations">搜索</el-button>
                    <el-button @click="resetAttachmentSearch">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>

              <div class="selection-section">
                <span class="selection-info"> 总共已选 {{ selectedAttachments.length }} 人 </span>
              </div>

              <el-table
                ref="attachmentTable"
                :data="registrationList"
                border
                v-loading="attachmentLoading"
                size="small"
                stripe
                max-height="400px"
                @selection-change="handleAttachmentSelectionChange"
                :row-key="getRowKey"
              >
                <el-table-column type="selection" width="55" :reserve-selection="true" />
                <el-table-column prop="name" label="姓名" width="100" />
                <el-table-column prop="uuid" label="用户ID" width="120" />
                <el-table-column
                  prop="optionName"
                  label="报名场次"
                  width="120"
                  show-overflow-tooltip
                />
                <el-table-column prop="school" label="毕业学校" show-overflow-tooltip />
                <el-table-column prop="major" label="所学专业" show-overflow-tooltip />
                <el-table-column prop="education" label="学历水平" width="100" />
                <el-table-column prop="attachmentCount" label="附件数量" width="80" align="center">
                  <template #default="{ row }">
                    <span :class="{ 'text-danger': row.attachmentCount === 0 }">
                      {{ row.attachmentCount || 0 }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="addTime" label="报名时间" width="150" />
              </el-table>

              <div v-show="registrationList.length" class="mt-15">
                <el-pagination
                  v-model:current-page="attachmentSearchForm.page"
                  :page-size="attachmentSearchForm.limit"
                  :total="attachmentTotal"
                  layout="total, prev, pager, next"
                  @current-change="changeAttachmentPage"
                />
              </div>
            </div>
          </el-col>

          <!-- 右侧：已选人员 -->
          <el-col :span="8">
            <div class="right-panel">
              <div class="selected-header">
                <h4>已选择人员 ({{ selectedAttachments.length }})</h4>
                <el-button
                  size="small"
                  type="danger"
                  @click="clearAllSelected"
                  :disabled="selectedAttachments.length === 0"
                >
                  清空全部
                </el-button>
              </div>

              <div class="selected-list" v-if="selectedAttachments.length > 0">
                <div v-for="person in selectedAttachments" :key="person.id" class="selected-item">
                  <div class="person-info">
                    <div class="person-name">{{ person.name }}</div>
                    <div class="person-details">
                      <span>ID: {{ person.uuid }}</span>
                      <span class="attachment-count">{{ person.attachmentCount }}个附件</span>
                    </div>
                    <div class="person-intention">{{ person.optionName }}</div>
                    <div class="person-school">{{ person.school }}</div>
                  </div>
                  <el-button
                    size="small"
                    type="text"
                    @click="removeSelectedPerson(person)"
                    class="remove-btn"
                  >
                    移除
                  </el-button>
                </div>
              </div>

              <div v-else class="empty-selected">
                <el-empty description="暂未选择任何人员" :image-size="80" />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="selectAttachmentVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleDownloadSelectedAttachments"
            :disabled="selectedAttachments.length === 0"
          >
            下载选中附件 ({{ selectedAttachments.length }})
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { reactive, onMounted, toRefs, defineComponent, watch } from 'vue'
import { ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import commonFunction from '/@/utils/commonFunction.ts'
import Paging from '/@/components/base/paging.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'

import {
  getActivityList,
  downloadExcel,
  downattachment,
  deleteActivityFrom,
  getIntentionOptionList,
  downSignExcel,
  getRegistrationList
} from '/@/api/doubleMeeting'

export default defineComponent({
  components: {
    DatePickerRange,
    Paging
  },
  name: 'cmsDoubleMeeting',
  setup() {
    const router = useRouter()
    const { copyText } = commonFunction()

    const state = reactive({
      loading: false,
      formSize: 'default',
      formData: {
        name: '',
        addTimeStart: '',
        addTimeEnd: '',
        page: 1,
        limit: 20
      },

      tableData: [] as any[],
      total: 0,

      exportVisible: false,
      renameVisible: false,
      signOptionVisible: false,
      downSignOptionVisible: false,
      haveSign: false,
      signOptionList: [] as any[],
      signOptionId: '',
      downSignOptionList: [] as any[],
      downSignOptionId: '',
      signUrl: '',
      signCodeUrl: '',
      signTitle: '',
      renameMode: 'custom',
      renameType: '1',
      renameFields: ['name', 'major'],
      renameTypeOption: [
        {
          k: '1',
          v: '附件名称（默认）'
        },
        {
          k: '2',
          v: '毕业学校'
        },
        {
          k: '3',
          v: '二级学院'
        },
        {
          k: '4',
          v: '所学专业'
        },
        {
          k: '5',
          v: '学历水平'
        },
        {
          k: '6',
          v: '手机号'
        }
      ],
      customRenameFields: [
        { k: 'name', v: '姓名' },
        { k: 'major', v: '所学专业' },
        { k: 'school', v: '毕业学校' },
        { k: 'education', v: '学历水平' }
      ],

      exportFileId: '',
      exportData: {
        exportAttachmentLink: '',
        exportRegistrationFormLink: ''
      },
      exportDateRange: [],

      // 选择附件下载相关状态
      selectAttachmentVisible: false,
      attachmentLoading: false,
      registrationList: [] as any[],
      attachmentTotal: 0,
      selectedAttachments: [] as any[], // 跨页选择的人员
      attachmentTable: null as any, // 表格引用
      attachmentSearchForm: {
        keyword: '',
        page: 1,
        limit: 50
      },

      // 标记是否为选中下载模式
      isSelectedDownloadMode: false
    })

    const getList = () => {
      state.loading = true
      getActivityList(state.formData).then((resp: any) => {
        state.total = resp.page.count
        state.tableData = resp.list
        state.loading = false
      })
    }

    const search = () => {
      state.formData.page = 1
      getList()
    }

    const changePage = (r: any) => {
      state.formData.page = r.page
      state.formData.limit = r.limit
      getList()
    }

    // 获取后台数据类型
    onMounted(async () => {
      getList()
    })

    const handleCopy = (link) => {
      const suffix = '?fromType=1'
      const copyLink = `${link + suffix}`
      const message = `<a href='${copyLink}' target='_blank' style='text-align:center;color:#409eff;display:block;text-decoration:none'>${copyLink}</a>`
      ElMessageBox.alert(message, '报名链接', {
        confirmButtonText: '复制',
        dangerouslyUseHTMLString: true,
        callback: (action) => {
          if (action === 'confirm') {
            copyText(copyLink)
          }
        }
      })
    }

    const showSignList = (row) => {
      const { id } = row
      // 先重置
      state.signOptionId = ''
      getIntentionOptionList({ activityFormId: id }).then((resp: any) => {
        state.signOptionVisible = true
        state.signOptionList = resp.map((item: any) => {
          return {
            k: item.optionId,
            v: item.title,
            signUrl: item.signUrl,
            signCodeUrl: item.signCodeUrl
          }
        })
      })
    }

    const handleExportData = (row) => {
      const { id, registrationFormNumber } = row
      if (/^0$/.test(registrationFormNumber)) {
        return
      }
      state.exportVisible = true
      state.exportFileId = id
      state.exportDateRange = []
      state.renameMode = 'custom'
      state.renameType = '1'
      state.renameFields = ['name', 'major']
      state.renameVisible = false
      state.downSignOptionVisible = false
      state.downSignOptionId = ''
    }

    const handleDownloadExcel = () => {
      const { exportFileId } = state
      downloadExcel({
        activityFormId: exportFileId,
        beginTime: state.exportDateRange?.[0],
        endTime: state.exportDateRange?.[1]
      })
      state.exportVisible = false
    }

    const handleDownload = () => {
      state.exportVisible = false
      const { exportFileId, renameMode, renameType, renameFields } = state
      const beginTime = state.exportDateRange?.[0]
      const endTime = state.exportDateRange?.[1]

      // 如果是选中下载模式，添加ids参数
      const selectedIds = state.isSelectedDownloadMode
        ? state.selectedAttachments.map((item: any) => item.id).join(',')
        : undefined

      if (renameMode === 'default') {
        const params: any = { activityFormId: exportFileId, type: renameType, beginTime, endTime }
        if (selectedIds) params.ids = selectedIds

        downattachment(params).then(() => {
          state.renameType = '1'
          if (state.isSelectedDownloadMode) {
            state.selectAttachmentVisible = false
            state.isSelectedDownloadMode = false
          }
        })
      } else {
        if (!renameFields.length) {
          ElMessageBox.alert('请至少选择一个命名字段', '提示')
          return
        }

        const params: any = {
          activityFormId: exportFileId,
          fields: renameFields,
          beginTime,
          endTime
        }
        if (selectedIds) params.ids = selectedIds

        downattachment(params).then(() => {
          state.renameFields = ['name', 'major']
          if (state.isSelectedDownloadMode) {
            state.selectAttachmentVisible = false
            state.isSelectedDownloadMode = false
          }
        })
      }
      state.renameVisible = false
    }

    const handleAddActivity = () => {
      router.push({ name: 'cmsDoubleMeetingAdd' })
    }

    const handleDelete = (id) => {
      ElMessageBox.alert('确定删除该活动表单吗？', '报名链接', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
        dangerouslyUseHTMLString: true,
        callback: async (action) => {
          if (action === 'confirm') {
            await deleteActivityFrom({ activityFormId: id })
            getList()
          }
        }
      })
    }

    const hanCopySignUrl = (url) => {
      copyText(url)
    }

    // 从这里开始是制作下载图的一些辅助方法
    async function loadImage(params) {
      // 图片src 必传
      const { src } = params
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.src = src
        img.crossOrigin = 'anonymous'
        img.onload = () => {
          resolve(img)
        }
        img.onerror = reject
      })
    }

    // 图片转base64
    function img2Base64(image) {
      // 图片画到canvas
      const canvas = document.createElement('canvas')
      canvas.width = image.width
      canvas.height = image.height
      const context = canvas.getContext('2d')
      context.drawImage(image, 0, 0)
      return canvas.toDataURL('image/jpg', 1.0)
    }
    // base64转blob
    function base64ToBlob(base64Code) {
      const parts = base64Code.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], {
        type: contentType
      })
    }

    const hanDownloadSignCodeUrl = (codeUrl) => {
      // 一个图片下载
      loadImage({ src: codeUrl }).then((image) => {
        const base64 = img2Base64(image)
        const blob = base64ToBlob(base64)
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        // title.jpg
        // 找到场次名称
        a.download = `${state.signTitle}.jpg`
        a.click()
        URL.revokeObjectURL(url)
      })
    }

    const showDownloadSignList = () => {
      const { exportFileId } = state
      state.downSignOptionVisible = true
      state.downSignOptionId = ''
      getIntentionOptionList({ activityFormId: exportFileId }).then((resp: any) => {
        state.downSignOptionList = resp.map((item: any) => {
          return {
            k: item.optionId,
            v: item.title,
            haveSign: item.haveSign
          }
        })
      })
    }

    const handelDownloadSignExcel = () => {
      downSignExcel({
        optionId: state.downSignOptionId,
        beginTime: state.exportDateRange?.[0],
        endTime: state.exportDateRange?.[1]
      }).then((_) => {
        // 关闭弹窗
        state.downSignOptionVisible = false
        state.exportVisible = false
      })
    }

    // 写个watch监听
    watch(
      () => state.signOptionId,
      (val) => {
        if (val === '') {
          state.signUrl = ''
          state.signCodeUrl = ''
          return
        }
        const selectItem = state.signOptionList.find((item) => item.k === val)
        state.signUrl = selectItem.signUrl
        state.signCodeUrl = selectItem.signCodeUrl
        state.signTitle = selectItem.v
      }
    )

    watch(
      () => state.downSignOptionId,
      (val) => {
        if (val === '') {
          state.haveSign = false
          return
        }
        const selectItem = state.downSignOptionList.find((item) => item.k === val)
        if (selectItem.haveSign === 1) {
          state.haveSign = true
        } else {
          state.haveSign = false
        }
      }
    )

    // 监听命名方式切换，切换到自定义命名时默认勾选"姓名"和"所学专业"
    watch(
      () => state.renameMode,
      (val) => {
        if (val === 'custom') {
          state.renameFields = ['name', 'major']
        } else if (val === 'default') {
          state.renameFields = []
        }
      }
    )

    // 字段label映射
    const getFieldLabel = (key: string) => {
      const item = state.customRenameFields.find((f) => f.k === key)
      return item ? item.v : key
    }

    // 显示选择附件下载弹窗
    const showSelectAttachmentDialog = () => {
      state.selectAttachmentVisible = true
      state.selectedAttachments = []
      state.attachmentSearchForm = {
        keyword: '',
        page: 1,
        limit: 50
      }
      getRegistrationListData()
    }

    // 获取行的唯一标识
    const getRowKey = (row: any) => {
      return row.id
    }

    // 获取报名人员列表
    const getRegistrationListData = () => {
      state.attachmentLoading = true
      const params = {
        activityFormId: state.exportFileId,
        ...state.attachmentSearchForm
      }

      // 真实API调用
      getRegistrationList(params)
        .then((resp: any) => {
          state.registrationList = resp.list || []
          state.attachmentTotal = parseInt(resp.page.count) || 0
          state.attachmentLoading = false

          // 数据加载完成后更新选择状态
          setTimeout(() => {
            updateTableSelection()
          }, 100)
        })
        .catch((error) => {
          console.error('获取报名人员列表失败:', error)
          state.registrationList = []
          state.attachmentTotal = 0
          state.attachmentLoading = false
        })
    }

    // 搜索报名人员
    const searchRegistrations = () => {
      state.attachmentSearchForm.page = 1
      getRegistrationListData()
    }

    // 重置搜索
    const resetAttachmentSearch = () => {
      state.attachmentSearchForm = {
        keyword: '',
        page: 1,
        limit: 50
      }
      getRegistrationListData()
    }

    // 分页变化
    const changeAttachmentPage = (page: number) => {
      state.attachmentSearchForm.page = page
      getRegistrationListData()
    }

    // 处理表格选择变化（跨页选择）
    const handleAttachmentSelectionChange = (selection: any[]) => {
      // 获取当前页面的所有人员ID
      const currentPageIds = state.registrationList.map((item: any) => item.id)

      // 移除当前页面的所有选择
      state.selectedAttachments = state.selectedAttachments.filter(
        (item: any) => !currentPageIds.includes(item.id)
      )

      // 添加当前页面新选择的人员
      state.selectedAttachments.push(...selection)
    }

    // 更新表格选择状态
    const updateTableSelection = () => {
      const selectedIds = state.selectedAttachments.map((item: any) => item.id)
      const table = state.attachmentTable
      if (table) {
        table.clearSelection()
        state.registrationList.forEach((row: any) => {
          if (selectedIds.includes(row.id)) {
            table.toggleRowSelection(row, true)
          }
        })
      }
    }

    // 移除选中的人员
    const removeSelectedPerson = (person: any) => {
      state.selectedAttachments = state.selectedAttachments.filter(
        (item: any) => item.id !== person.id
      )
      updateTableSelection()
    }

    // 清空所有选择
    const clearAllSelected = () => {
      state.selectedAttachments = []
      updateTableSelection()
    }

    // 下载选中的附件
    const handleDownloadSelectedAttachments = () => {
      if (state.selectedAttachments.length === 0) {
        ElMessageBox.alert('请至少选择一个报名人员', '提示')
        return
      }

      // 检查选中人员中是否有附件
      const hasAttachments = state.selectedAttachments.some((item: any) => item.attachmentCount > 0)
      if (!hasAttachments) {
        ElMessageBox.alert('选中的人员中没有附件，无法下载', '提示')
        return
      }

      // 标记为选中下载模式，然后显示原有的下载附件弹窗
      state.isSelectedDownloadMode = true
      state.renameVisible = true
    }

    return {
      getList,
      search,
      handleAddActivity,
      changePage,
      handleCopy,
      handleExportData,
      handleDownloadExcel,
      handleDownload,
      handleDelete,
      showSignList,
      hanCopySignUrl,
      hanDownloadSignCodeUrl,
      showDownloadSignList,
      handelDownloadSignExcel,
      getFieldLabel,
      showSelectAttachmentDialog,
      searchRegistrations,
      resetAttachmentSearch,
      changeAttachmentPage,
      handleAttachmentSelectionChange,
      handleDownloadSelectedAttachments,
      getRowKey,
      removeSelectedPerson,
      clearAllSelected,
      ...toRefs(state)
    }
  }
})
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}

.formbigbox {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
}

.edit-btn {
  color: inherit;
  text-decoration: none;
}
.sign-option {
  width: 100%;
}

.dialog-footer-buttons {
  .button-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.select-attachment-content {
  .left-panel {
    .search-section {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }

    .selection-section {
      margin-bottom: 10px;
      padding: 8px 0;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;
      gap: 15px;

      .selection-info {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .right-panel {
    border-left: 1px solid #ebeef5;
    padding-left: 20px;
    height: 480px;
    display: flex;
    flex-direction: column;

    .selected-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      flex-shrink: 0;

      h4 {
        margin: 0;
        font-size: 14px;
        color: #303133;
      }
    }

    .selected-list {
      flex: 1;
      overflow-y: auto;
      padding-right: 5px;

      .selected-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 10px;
        margin-bottom: 8px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e9ecef;

        .person-info {
          flex: 1;

          .person-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }

          .person-details {
            display: flex;
            gap: 10px;
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;

            .attachment-count {
              color: #409eff;
              font-weight: 500;
            }
          }

          .person-intention {
            font-size: 12px;
            color: #67c23a;
            margin-bottom: 4px;
            font-weight: 500;
          }

          .person-school {
            font-size: 12px;
            color: #999;
          }
        }

        .remove-btn {
          color: #f56c6c;
          padding: 0;
          margin-left: 10px;

          &:hover {
            color: #f78989;
          }
        }
      }
    }

    .empty-selected {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .text-danger {
    color: #f56c6c;
  }
}
</style>
