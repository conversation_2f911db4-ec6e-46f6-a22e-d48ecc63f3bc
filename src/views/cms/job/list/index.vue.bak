<template>
  <div>
    <div class="box">
      <Filter @search="handleSearch" />
      <div class="jc-between mb-10">
        <el-button @click="handleAdd" type="primary">+ 新增职位</el-button>
        <el-switch
          v-model="isSimple"
          size="large"
          active-text="简版(只支持部分搜索条件和显示结果)"
          inactive-text="全功能"
        />
        <el-link :underline="false" type="primary" @click="handleOpenCustomColumn">选择列</el-link>
      </div>
      <el-table
        ref="table"
        :data="list"
        border
        size="small"
        v-loading="loading"
        @sort-change="handleSortable"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" show-overflow-tooltip></el-table-column>
        <template v-for="(item, index) in customColumns">
          <el-table-column
            v-if="item.select && item.k === 1"
            :key="index"
            prop="uid"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-if="item.select && item.k === 2"
            :key="index"
            prop="sortName"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-link class="fw-normal fs-12" :underline="false" @click="handleJobDetail(row.id)"
                >{{ row.name }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 3"
            :key="index"
            prop="message"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-if="item.select && item.k === 4"
            :key="index"
            prop="fullName"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 5"
            :key="index"
            prop="click"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <router-link to="">{{ row.company }}</router-link>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 6"
            sortable="custom"
            prop="sortClick"
            :key="index"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.click }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 7"
            :key="index"
            prop="sortJobApplyAmount"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-link
                @click="openApplyListDialog(row.id, row.jobApplyNum)"
                :underline="false"
                class="fw-normal fs-12"
                >{{ row.jobApplyNum }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 8"
            :key="index"
            prop="statusTitle"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 9"
            :key="index"
            prop="auditStatusTitle"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div :class="row.auditStatus == -1 ? 'color-danger' : ''">
                {{ row.auditStatusTitle }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 10"
            :key="index"
            prop="sortRefreshTime"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.refreshTime }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 11"
            :key="index"
            prop="creator"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 12"
            :key="index"
            prop="sortClick"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.statusTitle }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 13"
            :key="index"
            prop="sortAmount"
            align="center"
            header-align="center"
            sortable="custom"
            :label="item.v"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 14"
            :key="index"
            prop="department"
            align="center"
            header-align="center"
            :label="item.v"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            v-if="item.select && item.k === 17"
            :key="index"
            :label="item.v"
            prop="isMiniappTxt"
            align="center"
          >
            <template #default="{ row }">
              <isMiniappChange :value="row.isMiniapp" type="job" :id="row.id"></isMiniappChange>
            </template>
          </el-table-column>
          <el-table-column
            v-if="item.select && item.k === 16"
            :key="index"
            align="center"
            header-align="center"
            :label="item.v"
            width="160px"
          >
            <template #default="{ row }">
              <el-row :gutter="10">
                <el-col v-if="row.auditStatus == 7" :span="6">
                  <el-tooltip class="item" content="审核" placement="top">
                    <el-button
                      size="small"
                      type="primary"
                      link
                      class="el-icon-s-check"
                      @click="pushAudit(row.id)"
                    ></el-button>
                  </el-tooltip>
                </el-col>
                <el-col :span="6">
                  <el-tooltip class="item" content="编辑" placement="top">
                    <el-button
                      :disabled="row.auditStatus == 7"
                      @click="handleEdit(row.id)"
                      size="small"
                      type="primary"
                      link
                      class="el-icon-edit-outline"
                    ></el-button>
                  </el-tooltip>
                </el-col>
                <el-col v-if="row.status == 1" :span="6">
                  <el-tooltip class="item" content="下线" placement="top">
                    <el-button
                      @click="handleOffline(row.id)"
                      size="small"
                      type="primary"
                      link
                      class="el-icon-bottom"
                    ></el-button>
                  </el-tooltip>
                </el-col>
                <el-col v-if="row.status == 0" :span="6">
                  <el-tooltip class="item" content="再发布" placement="top">
                    <el-button
                      @click="handleReleaseAgain(row.id)"
                      size="small"
                      type="primary"
                      link
                      class="el-icon-top"
                    ></el-button>
                  </el-tooltip>
                </el-col>
                <el-col v-if="row.isShow == 2" :span="6">
                  <el-tooltip class="item" content="显示" placement="top">
                    <el-button
                      @click="handleChangeJobShow(row.id, 1)"
                      size="small"
                      type="primary"
                      link
                      class="iconfont icon-xianshimima"
                    ></el-button>
                  </el-tooltip>
                </el-col>
                <el-col v-else :span="6">
                  <el-tooltip class="item" content="隐藏" placement="top">
                    <el-button
                      @click="handleChangeJobShow(row.id, 2)"
                      size="small"
                      type="primary"
                      link
                      class="iconfont icon-yincangmima"
                    ></el-button>
                  </el-tooltip>
                </el-col>
                <el-col :span="6">
                  <el-tooltip class="item" content="删除" placement="top">
                    <el-button
                      size="small"
                      type="primary"
                      link
                      class="el-icon-delete"
                      @click="handleDelete(row.id)"
                    ></el-button>
                  </el-tooltip>
                </el-col>
              </el-row>
            </template>
          </el-table-column>
        </template>
        <template #empty>
          <el-empty description="暂无数据"></el-empty>
        </template>
      </el-table>
      <div class="jc-between mt-15">
        <div class="ai-center">
          <el-checkbox
            v-model="checkedAll"
            @change="handleChange"
            class="mr-10"
            label="全选"
          ></el-checkbox>
          <el-select v-model="batchType" :disabled="!multipleSelection.length" filterable clearable>
            <el-option :value="1" label="显示"></el-option>
            <el-option :value="2" label="隐藏"></el-option>
            <el-option :value="3" label="删除"></el-option>
          </el-select>
        </div>
        <Pagination
          v-if="pagination.total > 0"
          :total="pagination.total"
          @change="handlePaginationChange"
        />
      </div>
    </div>
    <CustomColumnDialog ref="customColumnDialog" v-model:data="customColumns" />
    <ApplyListDialog ref="applyListDialog" />
    <JobDetail ref="jobDetail" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import Filter from '/@/views/job/query/components/filter.vue'
import ApplyListDialog from './components/applyListDialog.vue'
import CustomColumnDialog from '/@/components/business/customColumnDialog.vue'
import Pagination from '/@/components/base/paging.vue'
import JobDetail from '/@/components/job/jobDetailDialog.vue'
import isMiniappChange from '/@/components/base/select/isMiniappChange.vue'

import { getTableStagingField } from '/@/api/config'
import { jobReleaseAgain, jobOffline, changeJobShow } from '/@/api/job'
import { getJobList, deleteJob } from '/@/api/cmsJob'

interface JobListResponse {
  list: any[]
  page: {
    count: number
  }
}

const router = useRouter()
const customColumnDialog = ref()
const applyListDialog = ref()
const table = ref()
const jobDetail = ref()

const loading = ref(false)
const formData = reactive({
  page: 1,
  limit: 20,
  isCooperation: 2
})

const pagination = reactive({
  total: 0,
  limit: 20,
  page: 1
})

const batchType = ref('')
const isSimple = ref(true)
const checkedAll = ref(false)
const multipleSelection = ref([])
const list = ref<any[]>([])

const customColumns = ref([
  {
    k: 1,
    v: '职位ID',
    name: 'id',
    select: true,
    default: true
  },
  {
    k: 2,
    v: '职位名称',
    name: 'name',
    select: true,
    default: true
  },
  {
    k: 3,
    v: '基本信息',
    name: 'basicInformation',
    select: true,
    default: true
  },
  {
    k: 4,
    v: '所属单位',
    name: 'company',
    select: true,
    default: true
  },
  {
    k: 6,
    v: '点击',
    name: 'click',
    select: true,
    default: true
  },
  {
    k: 7,
    v: '投递次数',
    name: 'jobApplyNum',
    select: true,
    default: true
  },
  {
    k: 8,
    v: '招聘状态',
    name: 'natureType',
    select: true,
    default: true
  },
  {
    k: 9,
    v: '审核状态',
    name: 'auditStatus',
    select: true,
    default: true
  },
  {
    k: 10,
    v: '刷新时间',
    name: 'releaseTime',
    select: true,
    default: true
  },
  {
    k: 11,
    v: '发布人',
    name: 'creator',
    select: false,
    default: true
  },
  {
    k: 12,
    v: '显示状态',
    name: 'isShow',
    select: false,
    default: false
  },
  {
    k: 13,
    v: '招聘人数',
    name: 'employment',
    select: false,
    default: false
  },
  {
    k: 14,
    v: '用人部门',
    name: 'department',
    select: false,
    default: false
  },
  {
    k: 15,
    v: '发布时间',
    name: 'addTime',
    select: false,
    default: false
  },
  {
    k: 16,
    v: '操作',
    name: 'operation',
    select: true,
    default: true,
    disabled: true
  },
  {
    k: 17,
    v: '是否小程序',
    name: 'isMiniappTxt',
    select: false,
    default: false
  }
])

const getList = async () => {
  loading.value = true
  let data: JobListResponse
  if (isSimple.value) {
    data = await getJobList(formData)
  } else {
    data = await getJobList(formData)
  }
  list.value = data.list
  pagination.total = Number(data.page.count)
  batchType.value = ''
  loading.value = false
}

onActivated(() => {
  getList()
})

onMounted(() => {
  getTableStagingField('cmsJobList').then((resp: any) => {
    if (!resp.value) return
    const value = resp.value.split(',')
    customColumns.value = customColumns.value.map((item: any) => {
      return {
        ...item,
        select: value.includes(item.name)
      }
    })
  })
})

const handleSortable = ({ prop, order }) => {
  Reflect.deleteProperty(formData, 'sortClick')
  Reflect.deleteProperty(formData, 'sortJobApplyAmount')
  Reflect.deleteProperty(formData, 'sortRefreshTime')
  Reflect.deleteProperty(formData, 'sortAmount')
  if (order === 'ascending') {
    formData[prop] = 2
  } else if (order === 'descending') {
    formData[prop] = 1
  }
  getList()
}

const handleSearch = (filter: any) => {
  Object.assign(formData, filter)
  getList()
}

const handlePaginationChange = (data: any) => {
  formData.page = data.page
  formData.limit = data.limit
  getList()
}

const handleOpenCustomColumn = () => {
  customColumnDialog.value.open('cmsJobList')
}

const openApplyListDialog = (id: string, amount: any) => {
  if (Number(amount) === 0) return
  applyListDialog.value.open(id)
}

const handleAdd = () => {
  router.push({
    path: `/cms/jobAdd`
  })
}

const handleEdit = (id: string) => {
  router.push({
    path: `/cms/jobEdit/${id}`
  })
}

const pushAudit = (id: string) => {
  router.push({
    path: `/cms/jobAudit/${id}`
  })
}

const handleSelectionChange = (val) => {
  multipleSelection.value = val
  checkedAll.value = val.length === list.value.length
}

const handleChange = () => {
  table.value.toggleAllSelection()
}

const handleReleaseAgain = (id: string) => {
  ElMessageBox({
    title: '提示',
    message: '确认再次发布吗？',
    showCancelButton: true,
    beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        jobReleaseAgain({ id })
          .then(() => {
            instance.confirmButtonLoading = false
            done()
            getList()
          })
          .catch(() => {
            instance.confirmButtonLoading = false
          })
      } else {
        done()
      }
    }
  })
}

const handleOffline = (id: string) => {
  ElMessageBox({
    title: '提示',
    message: '确认下线该职位吗？',
    showCancelButton: true,
    beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        jobOffline({ id })
          .then(() => {
            instance.confirmButtonLoading = false
            done()
            getList()
          })
          .catch(() => {
            instance.confirmButtonLoading = false
          })
      } else {
        done()
      }
    }
  })
}

const handleChangeJobShow = (id: string, isShow: any) => {
  const typeText = isShow === 2 ? '隐藏' : '显示'
  const message = /,/.test(id) ? `确认批量${typeText}职位吗？` : `确定${typeText}该职位吗？`
  ElMessageBox({
    title: '提示',
    message,
    showCancelButton: true,
    beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        changeJobShow({ id, isShow })
          .then(() => {
            instance.confirmButtonLoading = false
            done()
            getList()
          })
          .catch(() => {
            instance.confirmButtonLoading = false
          })
      } else {
        done()
      }
    }
  })
}

const handleDelete = (id: string) => {
  const message = /,/.test(id) ? '确定批量删除职位吗？' : '确定删除该职位吗？'
  ElMessageBox({
    title: '提示',
    message,
    showCancelButton: true,
    beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        deleteJob({ id })
          .then(() => {
            instance.confirmButtonLoading = false
            done()
            getList()
          })
          .catch(() => {
            instance.confirmButtonLoading = false
          })
      } else {
        done()
      }
    }
  })
}

const handleJobDetail = (id: string) => {
  jobDetail.value.open(id)
}

watch(
  () => batchType.value,
  (val: any) => {
    const ids = multipleSelection.value.map((item: any) => item.id).join()
    switch (val) {
      case 1:
        handleChangeJobShow(ids, 1)
        break
      case 2:
        handleChangeJobShow(ids, 2)
        break
      case 3:
        handleDelete(ids)
        break
      default:
        break
    }
  }
)
</script>

<style scoped lang="scss">
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;

  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}

.btns {
  .el-button + .el-button {
    margin-left: 0;
  }
}
</style>
