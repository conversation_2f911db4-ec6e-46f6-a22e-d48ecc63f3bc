<template>
  <div class="box">
    <div id="top-container" class="pb-15">
      <Filter @search="handleSearch" @toggleShowMore="getTableHeight">
        <template #left>
          <el-button @click="handleAdd" type="primary" class="mr-15">+ 新增职位</el-button>
          <!-- <el-switch
            v-model="isSimple"
            size="large"
            active-text="简版(只支持部分搜索条件和显示结果)"
            inactive-text="全功能"
          /> -->
        </template>
      </Filter>

      <div class="jc-end mt-15 amount">
        <el-link :underline="false" type="primary" @click="handleOpenCustomColumn">选择列</el-link>
      </div>
    </div>

    <el-table
      ref="table"
      :data="list"
      border
      :max-height="maxTableHeight"
      v-loading="loading"
      @sort-change="handleSortTable"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" show-overflow-tooltip></el-table-column>
      <template v-for="item in customColumns">
        <el-table-column
          v-if="item.select"
          :key="item.key"
          :prop="item.prop"
          align="center"
          header-align="center"
          :label="item.label"
          :sortable="item.sortable"
          :min-width="setColumnMinWidth(item.key)"
          :fixed="item.key === 'operation' ? 'right' : false"
          :width="item.key === 'operation' ? '256px' : 'auto'"
        >
          <template v-if="item.headerSlot === 'offlineTime'" #header>
            <div class="ai-center inline-flex">
              {{ item.label }}
              <el-tooltip
                effect="dark"
                content="含已下线信息实际下线时间、在线信息预计下线时间、在线信息未配置下线时间情况"
                placement="top"
              >
                <el-icon><QuestionFilled color="#909399" /></el-icon>
              </el-tooltip>
            </div>
          </template>

          <template v-if="item.slot === 'name'" #default="{ row }">
            <el-link class="fw-normal" :underline="false" @click="handleJobDetail(row.jobId)"
              >{{ row.jobName }}
            </el-link>
          </template>

          <template v-else-if="item.slot === 'basicInformation'" #default="{ row }">
            {{ row.city }} | {{ row.educationTypeTitle }} | {{ row.experienceType }} |
            {{ row.wage }}
          </template>

          <template v-else-if="item.slot === 'announcementTitle'" #default="{ row }">
            <router-link
              :to="`/cms/announcementDetail/${row.announcementId}/${row.announcementStatus}`"
              >{{ row.announcementTitle }}
            </router-link>
          </template>

          <template v-else-if="item.slot === 'company'" #default="{ row }">
            <router-link :to="`/company/details?id=${row.companyId}`"
              >{{ row.companyName }}
            </router-link>
          </template>

          <template v-else-if="item.slot === 'applyTotal'" #default="{ row }">
            <el-link
              v-if="row.applyTotal !== 0"
              @click="openApplyListDialog(row.jobId)"
              :underline="false"
              class="fw-normal"
              >{{ row.applyTotal }}
            </el-link>
            <span v-else>{{ row.applyTotal }}</span>
          </template>

          <template v-else-if="item.slot === 'contact'" #default="{ row }">
            <el-popover placement="top-start">
              <template #reference>
                {{ row.jobContact?.contact }}
              </template>
              <div>
                <div>职位联系人</div>
                <div class="flex mt-5">
                  <span>{{ row.jobContact?.companyMemberType === '0' ? '主' : '子' }}</span>
                  <span>{{ row.jobContact?.contact }} / {{ row.jobContact?.department }}</span>
                </div>
                <div>{{ row.jobContact?.email }} {{ row.jobContact?.mobile }}</div>
              </div>
            </el-popover>
          </template>

          <template v-else-if="item.slot === 'isMiniappTxt'" #default="{ row }">
            <isMiniappChange
              :value="row.isMiniapp"
              v-model="row.isMiniapp"
              type="job"
              :id="row.jobId"
            ></isMiniappChange>
          </template>

          <template v-else-if="item.slot === 'operation'" #default="{ row }">
            <div class="table-button-group">
              <template v-if="row.btnList.length < 7">
                <el-button
                  v-for="item in row.btnList"
                  :key="item.key"
                  size="small"
                  :disabled="item.disabled === 2"
                  :class="item.class"
                  @click="btnGroupEvent(item.key, row)"
                  >{{ item.label }}
                </el-button>
              </template>

              <template v-else>
                <el-button
                  v-for="item in row.btnList?.slice(0, 5)"
                  :key="item.key"
                  size="small"
                  :disabled="item.disabled === 2"
                  :class="item.class"
                  @click="btnGroupEvent(item.key, row)"
                  >{{ item.label }}
                </el-button>

                <template v-if="row.btnList?.slice(5).length">
                  <el-popover placement="left" width="auto" trigger="click">
                    <template #reference>
                      <el-button class="white" size="small">更多</el-button>
                    </template>

                    <div class="table-popover-button">
                      <el-button
                        v-for="item in row.btnList.slice(5)"
                        :key="item.key"
                        size="small"
                        @click="btnGroupEvent(item.key, row)"
                        :disabled="item.disabled === 2"
                        :class="item.class"
                        >{{ item.label }}
                      </el-button>
                    </div>
                  </el-popover>
                </template>
              </template>
            </div>
          </template>
        </el-table-column>
      </template>

      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>

    <div
      id="bottom-container"
      v-show="pagination.total > 0"
      class="pt-15 jc-between ai-center"
      style="flex-shrink: 0"
    >
      <div class="ai-center">
        <el-checkbox
          v-model="checkedAll"
          @change="handleChange"
          class="mr-10"
          label="全选"
        ></el-checkbox>
        <el-select v-model="batchType" :disabled="!multipleSelection.length" filterable clearable>
          <el-option :value="1" label="显示"></el-option>
          <el-option :value="2" label="隐藏"></el-option>
          <el-option :value="3" label="删除"></el-option>
        </el-select>
      </div>
      <Pagination
        v-if="pagination.total > 0"
        :total="pagination.total"
        :page="formData.page"
        @change="handlePaginationChange"
      />
    </div>
  </div>
  <CustomColumnDialog ref="customColumnDialog" v-model:data="customColumns" />
  <ApplyListDialog ref="applyListDialog" />
  <JobDetail ref="jobDetail" />
  <EstablishmentDialog
    v-model:visible="establishmentVisible"
    :job-id="establishmentJobId"
    @update="getList"
  />
</template>

<script setup lang="ts">
import { QuestionFilled } from '@element-plus/icons-vue'
import { ref, reactive, onBeforeMount, watch, onActivated, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import Filter from '/@/components/form/jobFilter.vue'
import ApplyListDialog from './components/applyListDialog.vue'
import CustomColumnDialog from '/@/components/business/customColumnDialog-V2.vue'
import Pagination from '/@/components/base/paging.vue'
import JobDetail from '/@/components/job/jobDetailDialog.vue'
import isMiniappChange from '/@/components/base/select/isMiniappChange.vue'
import EstablishmentDialog from '/@/components/job/establishmentDialog.vue'

import { getTableStagingField } from '/@/api/config'
import {
  jobReleaseAgain,
  jobOffline,
  changeJobShow,
  batchChangeJobShow,
  batchChangeJobHide,
  jobRefresh,
  jobDelete,
  batchJobDelete
} from '/@/api/job'
import { getUnCooperationJobList } from '/@/api/cmsJob'

interface JobListResponse {
  list: any[]
  page: {
    count: number
  }
}

const router = useRouter()
const customColumnDialog = ref()
const applyListDialog = ref()
const table = ref()
const jobDetail = ref()
const maxTableHeight = ref(450)

const loading = ref(false)
const isFirst = ref(true)
const sortQuery = ref({})
const formData = reactive({
  page: 1,
  limit: 20
})

const pagination = reactive({
  total: 0,
  limit: 20,
  page: 1
})

const batchType = ref('')
const isSimple = ref(true)
const checkedAll = ref(false)
const multipleSelection = ref([])
const list = ref<any[]>([])
const establishmentJobId = ref('')
const establishmentVisible = ref(false)
// 操作类型：1单个，2批量
const currentOperationType = ref(1)

const customColumns = ref([
  {
    prop: 'jobUuid',
    key: 'id',
    label: '职位ID',
    select: true,
    default: true
  },
  {
    prop: 'jobName',
    key: 'name',
    label: '职位名称',
    slot: 'name',
    select: true,
    default: true
  },
  {
    prop: 'basicInformation',
    key: 'basicInformation',
    label: '基本信息',
    slot: 'basicInformation',
    select: true,
    default: true
  },
  {
    prop: 'announcementTitle',
    key: 'announcementTitle',
    label: '关联公告',
    slot: 'announcementTitle',
    select: true,
    default: true
  },
  {
    prop: 'company',
    key: 'company',
    label: '所属单位',
    slot: 'company',
    select: true,
    default: true
  },
  {
    prop: 'click',
    key: 'click',
    label: '点击量',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'applyTotal',
    key: 'applyTotal',
    label: '投递次数',
    sortable: 'custom',
    slot: 'applyTotal',
    select: true,
    default: true
  },
  {
    prop: 'deliveryWay',
    key: 'deliveryWay',
    label: '投递方式',
    select: true,
    default: true
  },
  {
    prop: 'majorText',
    key: 'majorId',
    label: '学科专业',
    select: false,
    default: false
  },
  {
    prop: 'status',
    key: 'status',
    label: '招聘状态',
    select: true,
    default: true
  },
  {
    prop: 'amount',
    key: 'amount',
    label: '招聘人数',
    sortable: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'creator',
    key: 'creator',
    label: '创建人',
    select: false,
    default: false
  },
  {
    prop: 'auditAdminName',
    key: 'contactSynergy',
    label: '审核人',
    select: false,
    default: false
  },
  {
    prop: 'firstReleaseTimeDate',
    key: 'firstReleaseTimeStart',
    label: '初始发布时间',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'publishTimeDate',
    key: 'releaseTime',
    label: '发布时间',
    select: true,
    default: true
  },

  {
    prop: 'realRefreshTimeDate',
    key: 'refreshTime',
    label: '刷新时间',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'addTimeDate',
    key: 'addTime',
    label: '创建时间',
    sortable: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'offlineTimeDate',
    key: 'periodDate',
    label: '下线时间',
    sortable: 'custom',
    headerSlot: 'offlineTime',
    select: false,
    default: false
  },
  {
    prop: 'auditStatus',
    key: 'auditStatus',
    label: '审核状态',
    select: false,
    default: false
  },
  {
    prop: 'isShow',
    key: 'isShow',
    label: '显示状态',
    select: false,
    default: false
  },
  {
    prop: 'department',
    key: 'department',
    label: '用人部门',
    select: false,
    default: false
  },
  {
    prop: 'publishMode',
    key: 'isArticle',
    label: '发布模式',
    slot: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'isMiniappTxt',
    key: 'isMiniappTxt',
    label: '是否小程序',
    slot: 'isMiniappTxt',
    select: false,
    default: false
  },
  {
    prop: 'operation',
    key: 'operation',
    label: '操作',
    slot: 'operation',
    disabled: true,
    select: true,
    default: true
  }
])
const getTableHeight = async () => {
  await nextTick(() => {})
  const topHeight = document.getElementById('top-container')?.clientHeight || 0
  const height = Number(
    document.getElementById('layout-main')?.getAttribute('data-view-height') || 0
  )
  const bottomHeight = document.getElementById('bottom-container')?.clientHeight || 0
  // .box 内边框
  const padding = 40
  maxTableHeight.value = height - topHeight - bottomHeight - padding
}

const getList = async (sort = {}) => {
  loading.value = true
  let data: JobListResponse
  if (isSimple.value) {
    data = await getUnCooperationJobList({ ...formData, ...sortQuery.value })
  } else {
    data = await getUnCooperationJobList({ ...formData, ...sortQuery.value })
  }
  list.value = data.list
  pagination.total = Number(data.page.count)
  batchType.value = ''
  loading.value = false

  getTableHeight()
}

onActivated(() => {
  // if (isFirst.value) return
  // 移除自动重新加载数据，保持keep-alive缓存效果
  // getList()
})

onBeforeMount(async () => {
  await getList()
  isFirst.value = false

  getTableStagingField('cmsJobList').then((resp: any) => {
    if (!resp.value) return
    const value = resp.value.split(',')
    customColumns.value = customColumns.value.map((item: any) => {
      return {
        ...item,
        select: value.includes(item.key)
      }
    })
  })
})

const handleSortTable = ({ prop, order }) => {
  // 倒序：descending， 正序：ascending
  // 1:倒序；2:正序
  const sortMap = {
    click: 'sortClick',
    applyTotal: 'sortApplyTotal',
    amount: 'sortAmount',
    firstReleaseTimeDate: 'sortFirstReleaseTime',
    realRefreshTimeDate: 'sortRealRefreshTime',
    addTimeDate: 'sortAddTime',
    offlineTimeDate: 'sortOfflineTime'
  }

  const key = sortMap[prop]
  const sort = order === 'ascending' ? 2 : 1

  sortQuery.value = order ? { [key]: sort } : {}
  getList()
}

const handleSearch = (filter: any) => {
  Object.assign(formData, filter)
  formData.page = 1
  getList()
}

const handlePaginationChange = (data: any) => {
  formData.page = data.page
  formData.limit = data.limit
  getList()
}

const handleOpenCustomColumn = () => {
  customColumnDialog.value.open('cmsJobList')
}

const openApplyListDialog = (id: string) => {
  // if (Number(amount) === 0) return
  // applyListDialog.value.open(id)
  router.push({
    path: '/job/business',
    query: { jobId: id }
  })
}

const handleAdd = () => {
  router.push({
    path: `/cms/jobAdd`
  })
}

const handleEdit = (id: string) => {
  router.push({
    path: `/cms/jobEdit/${id}`
  })
}

const pushAudit = (id: string) => {
  router.push({
    path: `/cms/jobAudit/${id}`
  })
}

const handleSelectionChange = (val) => {
  multipleSelection.value = val
  checkedAll.value = val.length === list.value.length
}

const handleChange = () => {
  table.value.toggleAllSelection()
}

const handleReleaseAgain = (id: string) => {
  ElMessageBox({
    title: '提示',
    message: '确认再次发布吗？',
    showCancelButton: true,
    beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        jobReleaseAgain({ jobId: id })
          .then(() => {
            instance.confirmButtonLoading = false
            done()
            getList()
          })
          .catch(() => {
            instance.confirmButtonLoading = false
          })
      } else {
        done()
      }
    }
  })
}

const handleOffline = (id: string) => {
  ElMessageBox({
    title: '提示',
    message: '确认下线该职位吗？',
    showCancelButton: true,
    beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        jobOffline({ jobId: id, isCooperation: 2 })
          .then(() => {
            instance.confirmButtonLoading = false
            done()
            getList()
          })
          .catch(() => {
            instance.confirmButtonLoading = false
          })
      } else {
        done()
      }
    }
  })
}

const handleChangeJobShow = (id: string, isShow: any) => {
  const typeText = isShow === 2 ? '隐藏' : '显示'
  const message =
    currentOperationType.value === 2 ? `确认批量${typeText}职位吗？` : `确定${typeText}该职位吗？`
  ElMessageBox({
    title: '提示',
    message,
    showCancelButton: true,
    async beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        try {
          if (currentOperationType.value === 1) {
            await changeJobShow({ jobId: id, isShow })
          } else {
            const api = isShow === 1 ? batchChangeJobShow : batchChangeJobHide
            await api({ jobId: id })
          }

          instance.confirmButtonLoading = false
          done()
          getList()
        } catch (error) {
          instance.confirmButtonLoading = false
        }
      } else {
        done()
      }
    }
  })
}

const handleDelete = (id: string) => {
  const message =
    currentOperationType.value === 2
      ? '删除后无法恢复，确定批量删除吗？'
      : '删除后无法恢复，确定删除吗？'
  ElMessageBox({
    title: '提示',
    message,
    showCancelButton: true,
    async beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        try {
          const api = currentOperationType.value === 2 ? batchJobDelete : jobDelete
          await api({ jobId: id })
          instance.confirmButtonLoading = false
          done()
          getList()
        } catch (error) {
          instance.confirmButtonLoading = false
        }
      } else {
        done()
      }
    }
  })
}

const handleJobDetail = (id: string) => {
  jobDetail.value.open(id)
}
const messageBox = (title, message, callback) => {
  ElMessageBox({
    title,
    message,
    showCancelButton: true,
    beforeClose(action, instance, done) {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        callback()
          .then(() => {
            instance.confirmButtonLoading = false
            done()
            getList()
          })
          .catch(() => {
            instance.confirmButtonLoading = false
          })
      } else {
        done()
      }
    }
  })
}

const handleRefresh = (id: string) => {
  messageBox('提示', '是否刷新该职位？', async () => {
    await jobRefresh({ jobId: id })
  })
}

watch(
  () => batchType.value,
  (val: any) => {
    const ids = multipleSelection.value.map((item: any) => item.jobId).join()
    currentOperationType.value = 2
    switch (val) {
      case 1:
        handleChangeJobShow(ids, 1)
        break
      case 2:
        handleChangeJobShow(ids, 2)
        break
      case 3:
        handleDelete(ids)
        break
      default:
        break
    }
  }
)

const setColumnMinWidth = (key: string) => {
  let minWidth = 90

  switch (key) {
    case 'announcementTitle':
      minWidth = 210
      break
    case 'company':
      minWidth = 210
      break
    case 'applyTotal':
      minWidth = 110
      break
    case 'amount':
      minWidth = 110
      break
    case 'majorId':
      minWidth = 210
      break
    case 'click':
      minWidth = 100
      break
    case 'firstReleaseTimeStart':
      minWidth = 140
      break
    case 'refreshTime':
      minWidth = 110
      break
    case 'addTime':
      minWidth = 110
      break
    case 'periodDate':
      minWidth = 120
      break
    case 'contact':
      minWidth = 110
      break
    case 'contactSynergyNum':
      minWidth = 110
      break
    case 'contactSynergyInfo':
      minWidth = 110
      break
    case 'isMiniappTxt':
      minWidth = 110
      break
    case 'operation':
      minWidth = 210
      break

    default:
      minWidth = 90
      break
  }

  return minWidth
}

const btnGroupEvent = async (key: string, row: any) => {
  currentOperationType.value = 1
  const { jobId } = row
  switch (key) {
    // 刷新
    case 'refresh':
      await handleRefresh(jobId)
      break
    // 审核
    case 'audit':
      await pushAudit(jobId)
      break
    // 编辑
    case 'edit':
      await handleEdit(jobId)
      break
    // 下线
    case 'offline':
      await handleOffline(jobId)
      break
    // 再发布
    case 'republish':
      await handleReleaseAgain(jobId)
      break
    // 显示
    case 'show':
      await handleChangeJobShow(jobId, 1)
      break
    // 隐藏
    case 'hide':
      await handleChangeJobShow(jobId, 2)
      break
    // 删除
    case 'delete':
      await handleDelete(jobId)
      break
    // 编制设置
    case 'establishment':
      establishmentJobId.value = jobId
      establishmentVisible.value = true
      break

    default:
      break
  }
}
</script>

<script lang="ts">
export default {
  name: 'cmsJobList'
}
</script>

<style scoped lang="scss">
:deep() {
  @import '/@/theme/tableScrollBar.scss';
}

.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;

  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}

.amount {
  margin: 20px 0 0px;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
}
</style>
