<template>
  <div class="main" v-loading="loading">
    <el-form :model="form" label-width="80px" label-position="right" :rules="rules" ref="fromData">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card class="left-card left-card-n1">
            <!-- <template #header>
              <div class="card-header">
                <span>公告信息</span>
              </div>
            </template> -->
            <div class="flex">
              <el-form-item label-width="100px" class="flex-1" label="公告标题" prop="title">
                <el-input v-model="form.title"></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                label-width="100px"
                class="flex-1 flex-center-y"
                label="所属栏目"
                prop="homeColumnId"
              >
                <Colunm
                  class="home-colunm"
                  v-model="form.homeColumnId"
                  :columnList="columnList"
                ></Colunm>
              </el-form-item>
              <el-form-item
                label-width="100px"
                class="flex-1 flex-center-y"
                label="所属副栏目"
                prop="homeSubColumnIds"
              >
                <SubColumn v-model="form.homeSubColumnIds" :columnList="columnList"></SubColumn>
              </el-form-item>
              <el-form-item
                label-width="100px"
                class="flex-1 flex-center-y"
                label="调用副栏目"
                v-if="form.announcementId"
              >
                <el-input disabled v-model="columnTxt"></el-input>
              </el-form-item>
            </div>

            <div class="flex">
              <el-form-item label-width="100px" class="flex-1" label="公告简标题" prop="subTitle">
                <el-input v-model="form.subTitle" />
              </el-form-item>
            </div>

            <div class="flex">
              <el-form-item
                class="flex-1"
                label-width="100px"
                label="公告亮点描述"
                prop="highlightsDescribe"
              >
                <el-input
                  placeholder="请输入公告亮点/推荐理由（如福利待遇、是否有编）"
                  v-model="form.highlightsDescribe"
                />
              </el-form-item>
            </div>

            <div class="flex">
              <el-form-item class="flex-1" label-width="100px" label="公告详情" prop="content">
                <WangEditor
                  class="w100"
                  ref="editorRef"
                  v-model="form.content"
                  showCustomUploadFile
                />
              </el-form-item>
            </div>
            <div class="flex jc-end" v-show="!isEdit">
              <el-button class="button submit" type="primary" @click="testHtml">识别</el-button>
            </div>
          </el-card>
          <el-card class="job-card mt20 left-card-n2">
            <template #header>
              <div class="card-header jc">
                <span class="fs-16 fw-bold">职位附件</span>
                <el-button class="button" type="primary" @click="openJobFileUpload"
                  >+上传附件</el-button
                >
              </div>
            </template>
            <JobFileUpload ref="jobFile" v-model="fileList" />
          </el-card>
          <el-card class="job-card">
            <template #header>
              <div class="card-header">
                <span class="fs-16 fw-bold">职位信息</span>
                <div style="display: flex; align-items: center">
                  <el-upload
                    v-if="form.companyId"
                    ref="batchUploadRef"
                    action="/announcement/upload-excel"
                    :show-file-list="false"
                    :on-success="batchUpload"
                  >
                    <el-button class="button" type="primary">+批量导入</el-button>
                  </el-upload>

                  <el-button v-else class="button" type="primary" @click="batchUploadMessage"
                    >+批量导入</el-button
                  >
                  <el-button class="button ml-15" type="primary" @click="openJobAdd"
                    >+添加职位</el-button
                  >
                </div>
              </div>
            </template>
            <el-table :data="jobList" align="left" border size="default">
              <!-- <el-table-column prop="id" label="职位编号" /> -->
              <el-table-column fixed width="300px" min-width="90px" prop="name" label="职位名称">
                <template #default="{ row, $index }">
                  <el-link type="primary" :underline="false" @click="editJob(row, $index)">
                    {{ row.name }}
                  </el-link>
                </template>
              </el-table-column>

              <el-table-column
                label="基本信息"
                prop="information"
                width="300px"
                min-width="90px"
                class-name="information"
              >
                <template #default="{ row }">
                  <el-tooltip
                    popper-class="information-class"
                    class="box-item"
                    :content="row.information"
                    placement="top-start"
                  >
                    {{ row.information }}
                  </el-tooltip>
                </template>
              </el-table-column>

              <el-table-column prop="department" min-width="90px" label="用人部门" />
              <el-table-column
                min-width="110px"
                prop="jobContact?.contact"
                label="职位联系人"
                v-if="hasAccount && isCooperation"
              >
                <template #default="{ row }">
                  <el-popover placement="top-start">
                    <template #reference>
                      <div>
                        <span class="mr-5">{{ row.jobContact?.contact }}</span>
                        <span>{{ row.jobContact?.companyMemberType === '0' ? '主' : '' }}</span>
                      </div>
                    </template>
                    <div>
                      <h4>职位联系人</h4>
                      <div>
                        <span class="mr-5">
                          {{ row.jobContact?.companyMemberType === '0' ? '主' : '子' }}
                        </span>

                        <span>{{ row.jobContact?.contact }}</span>
                        <span v-if="row.jobContact?.department">
                          /{{ row.jobContact?.department }}
                        </span>
                      </div>
                      <div>
                        <span>{{ row.jobContact?.email }}</span>
                        <span>{{ row.jobContact?.mobile }}</span>
                      </div>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column
                min-width="110px"
                prop="jobContactSynergyNum"
                label="协同子账号"
                v-if="hasAccount && isCooperation"
              >
                <template #default="{ row }">
                  <div v-if="row.jobContactSynergyNum === 0">{{ row.jobContactSynergyNum }}</div>
                  <el-popover v-else placement="top-start" :width="200">
                    <template #reference>
                      {{ row.jobContactSynergyNum }}
                    </template>
                    <div>协同子账号</div>
                    <div v-for="item in row.jobContactSynergy" :key="item.id">
                      <div class="flex mt-5">
                        <span class="color-danger mr-5" v-if="item.isContact === 1">联</span>
                        <div>{{ item.contact }} / {{ item.department }}</div>
                      </div>
                      <div>{{ item.email }} {{ item.mobile }}</div>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column prop="statusTxt" min-width="90px" label="招聘状态" />
              <el-table-column prop="auditStatusTxt" min-width="90px" label="审核状态" />
              <el-table-column prop="operation" label="操作" width="200px" fixed="right">
                <template #default="{ row, $index }">
                  <el-row>
                    <el-col :span="8">
                      <el-link
                        type="primary"
                        :underline="false"
                        @click="editJob(row, $index)"
                        :disabled="row.status === '0'"
                        >编辑</el-link
                      >
                    </el-col>

                    <el-col :span="8" v-if="row.canDel">
                      <el-link
                        type="primary"
                        :underline="false"
                        @click="deleteJob(row)"
                        :disabled="row.isApply"
                        >删除</el-link
                      >
                    </el-col>
                    <el-col :span="8">
                      <el-link type="primary" :underline="false" @click="copyJob(row)"
                        >复制</el-link
                      >
                    </el-col>
                  </el-row>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <template #header>
              <div class="card-header">
                <span class="fs-16 fw-bold">公告属性</span>
              </div>
            </template>
            <div class="flex">
              <el-form-item label="官网公告调用属性" prop="comboAttribute">
                <AttributeCheck
                  :checkBoxList="notOverseasAttributeList"
                  v-model="form.comboAttribute"
                  v-model:date="attributeData"
                  @update:date="handleDate"
                ></AttributeCheck>
              </el-form-item>
            </div>

            <div class="flex">
              <el-form-item label="高才海外调用属性" prop="overseasAttribute">
                <AttributeCheck
                  :checkBoxList="overseasAttributeList"
                  v-model="form.overseasAttribute"
                  v-model:date="attributeData"
                  @update:date="handleDate"
                  :date-type="'globalAttributeTimeDate'"
                ></AttributeCheck>
              </el-form-item>
            </div>
            <el-divider border-style="dashed" style="margin-top: 0px" />
            <div class="flex">
              <el-form-item class="flex-1" label="截止日期" prop="periodDate">
                <el-date-picker
                  class="w100"
                  v-model="form.periodDate"
                  type="date"
                  placeholder="请选择截止日期,不选代表详见正文"
                  value-format="YYYY-MM-DD"
                  :disabled-date="handleDisableDate"
                >
                </el-date-picker>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item class="flex-1" label="合作类型">
                <el-radio
                  v-model="form.isCooperation"
                  :disabled="status === '1' || isJobLength"
                  label="2"
                  @change="changeCoopertaion"
                  >非合作单位</el-radio
                >
                <el-radio
                  v-model="form.isCooperation"
                  :disabled="status === '1' || isJobLength"
                  label="1"
                  @change="changeCoopertaion"
                  >合作单位</el-radio
                >
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item class="flex-1" label="选择单位" prop="companyTxt">
                <el-select
                  v-model="form.companyTxt"
                  filterable
                  remote
                  clearable
                  placeholder="请输入单位"
                  :remote-method="remoteMethod"
                  @change="handleChange"
                  :disabled="status === '1' || isJobLength"
                >
                  <el-option
                    v-for="item in companyTypeList"
                    :key="item.id"
                    :label="item.fullName"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
                <!-- <el-button type="primary" link v-if="addCompany === '2'" @click="addWaitCooperation"
                  >添加单位</el-button
                > -->
              </el-form-item>
            </div>

            <el-form-item class="flex" label="单位属性">
              <el-input disabled class="flex-1" v-model="typeTxt" />
              <el-input disabled class="flex-1 ml-10" v-model="natureTxt" />
            </el-form-item>
            <el-divider border-style="dashed" />
            <el-form-item label="投递配置" v-if="isCooperation">
              <el-input disabled v-model="companyDeliveryTypeTxt" />
            </el-form-item>
            <ApplyMethods
              v-model="applyMethodsData"
              :isCooperation="isCooperation"
              :accountType="companyDeliveryType"
            />

            <template v-if="!isCooperation">
              <el-form-item label="地址隐藏">
                <el-radio-group v-model="form.addressHideStatus">
                  <el-radio label="1">隐藏</el-radio>
                  <el-radio label="2">展示</el-radio>
                </el-radio-group>
                <el-tooltip content='选择"隐藏"，前端公告详情页默认不展示投递邮箱地址'>
                  <i class="el-icon el-icon-question" style="margin-left: 20px" />
                </el-tooltip>
              </el-form-item>
            </template>

            <el-form-item label="附件提示">
              <el-checkbox
                v-model="form.isAttachmentNotice"
                size="large"
                true-label="1"
                false-label="2"
              />
              &nbsp;&nbsp;1（提示）
              <el-tooltip class="box-item" effect="dark" placement="bottom">
                <template #content>
                  若勾选1（提示），求职者在投递该公告下职位时，将展示提示文案：<br />系统校验到该职位所关联的公告正文提示报名需提交附件材料，请确认是否已上传应聘材料。 </template
                ><i class="el-icon el-icon-question"></i
              ></el-tooltip>
            </el-form-item>

            <el-divider border-style="dashed" />

            <!-- <p>高级设置</p>
            <br />
            <el-upload
              class="avatar-uploader"
              action="/upload/image"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <p>缩略图：</p>
              <img v-if="imageUrl" :src="imageUrl" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <p class="logo" v-if="!imageUrl">支持JPG/PNG图片格式，文件小于5M</p>
            </el-upload>
            <br />
            <div class="flex">
              <el-form-item class="flex-1" label="公告摘要" prop="seoDescription">
                <el-input
                  v-model="form.seoDescription"
                  autosize
                  type="textarea"
                  resize="none"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item class="flex-1" label="关键词" prop="seoKeywords">
                <el-input
                  v-model="form.seoKeywords"
                  autosize
                  type="textarea"
                  resize="none"
                ></el-input>
              </el-form-item>
            </div> -->
            <div class="flex">
              <el-form-item class="flex-1 fw-bold" label="页面模板" prop="templateId">
                <el-select v-model="form.templateId" filterable clearable>
                  <el-option
                    v-for="item in templateList"
                    :key="item.k"
                    :value="item.k"
                    :label="item.v"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>

            <div class="flex" v-if="showBackgroundUpload">
              <el-form-item label="背景图">
                <el-radio-group v-model="form.backgroundImgFileType">
                  <el-radio
                    v-for="item in backgroundImgFileTypeList"
                    :key="item.k"
                    :label="item.k"
                    >{{ item.v }}</el-radio
                  >
                </el-radio-group>
                <template v-if="form.backgroundImgFileType === '3'">
                  <CutOutUploadImg
                    v-model:file-id="form[templateMapKey.formImgKey]"
                    :uploadText="uploadBackgroundImgTips"
                    width="88px"
                    height="auto"
                    :width-size="cutBackgroundSize.width"
                    :height-size="cutBackgroundSize.height"
                    v-model:full-url="reviewData[templateMapKey.reviewImgKey]"
                  />
                </template>
              </el-form-item>
            </div>
            <p class="fs-14 fw-bold">活动设置</p>
            <br />
            <div class="flex">
              <el-form-item class="flex-1" label="关联活动" prop="activityAnnouncement">
                <el-select v-model="form.activityAnnouncement" multiple filterable clearable>
                  <el-option
                    v-for="item in activityList"
                    :key="item.k"
                    :value="item.k"
                    :label="item.v"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item class="flex-1" label="招聘岗位" prop="activityJobContent">
                <el-input
                  v-model="form.activityJobContent"
                  :rows="2"
                  maxlength="500"
                  type="textarea"
                  resize="none"
                ></el-input>
              </el-form-item>
            </div>
            <el-affix position="bottom" :offset="54">
              <el-form-item>
                <div class="flex button-box">
                  <el-button @click="submitAnnounce('2')" type="primary">发布</el-button>
                  <el-button v-if="status !== '1'" @click="submitAnnounce('1')">保存</el-button>
                </div>
              </el-form-item>
            </el-affix>
          </el-card>
        </el-col>
      </el-row>
    </el-form>

    <AddPosition
      :isCooperation="isCooperation"
      ref="showJobAdd"
      :deliveryType="companyDeliveryType"
      :jobList="jobList"
      @jobTempData="getJobTempData"
      :subAccountConfig="subAccountConfig"
    ></AddPosition>

    <el-dialog v-model="dialogAddVisible" title="添加单位">
      <el-form :model="addForm">
        <el-form-item label="单位名称">
          <el-input v-model="addForm.fullName" autocomplete="off" style="width: 400px"></el-input>
        </el-form-item>
        <el-form-item label="单位属性">
          <el-select v-model="addForm.type" placeholder="请选择单位类型">
            <el-option
              v-for="item in typeList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
          <el-select v-model="addForm.nature" placeholder="请选择单位性质">
            <el-option
              v-for="item in companyNatureList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option
          ></el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogAddVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCompany">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch, computed, getCurrentInstance } from 'vue'

import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import AddPosition from './component/addPosition.vue'
import {
  getAddParams,
  getAnnouncementEditInit,
  getCompanyTypeList,
  identityEditor,
  announcementAdd,
  announcementEdit,
  temporaryJobCopy,
  temporaryJobDelete,
  jobBatchAdd,
  getAnnouncementTitleOnly,
  hwActivityList
} from '/@/api/announcement'
import { addUnit, getUnitList } from '/@/api/unitManage'
import Colunm from '/@/components/base/colunm.vue'
import SubColumn from '/@/components/base/subColumn.vue'
import AttributeCheck from '/@/components/base/attributeCheck.vue'
import { formatDate } from '/@/utils/formatTime'
import JobFileUpload from './component/jobFileUpload.vue'
import WangEditor from '/@/components/wangEditor/index.vue'
import ApplyMethods from '/@/components/business/applyMethods.vue'
import CutOutUploadImg from '/@/components/upload/cutOutUploadImg.vue'

// 定义名字
defineOptions({
  name: 'cmsAnnouncementAdd'
})

// 变量声明区域
const { proxy } = getCurrentInstance() as any
const fileList = ref([])
const loading = ref(false)
const form = reactive<any>({
  title: '',
  homeColumnId: '',
  homeSubColumnIds: '',
  periodDate: '',
  content: '',
  isCooperation: '2',
  coverThumb: '',
  seoDescription: '',
  seoKeywords: '',
  companyId: '',
  templateId: '',
  jobIds: [],
  subTitle: '',
  highlightsDescribe: '',
  backgroundImgFileType: '1',
  backgroundImgFileId: '',
  backgroundImgFileId2: '',
  backgroundImgFileId3: '',
  submitType: '',
  announcementId: '',
  extraNotifyAddress: '',
  applyType: '',
  applyAddress: '',
  addressHideStatus: '2',
  deliveryWay: [],
  comboAttribute: [],
  overseasAttribute: [],
  activityJobContent: '',
  activityAnnouncement: []
})
const isEdit = ref(false)
const jobList = ref<any[]>([])
const companyTypeList = ref<any[]>([])
const companyDeliveryType = ref<any>(null)
const companyDeliveryTypeTxt = ref('')
const status = ref('')
const articleId = ref('')
const natureTxt = ref('')
const typeTxt = ref('')
const imageUrl = ref('')
const dialogAddVisible = ref(false)
const addCompany = ref('1')
const companyNatureList = ref<any[]>([])
const typeList = ref<any[]>([])
const columnList = ref<any[]>([])
const subAccountConfig = ref<any>({})
const attributeData = ref({
  indexTopEndTime: '',
  columnTopEndTime: '',
  doctorPushEndTime: '',
  overseasIndexTopEndTime: '',
  overseasColumnTopEndTime: ''
})
const backgroundImgFileTypeList = ref<any[]>([])
const templateList = ref<any[]>([])
const activityList = ref<any[]>([])
const overseasAttributeList = ref<any[]>([])
const notOverseasAttributeList = ref<any[]>([])
const columnTxt = ref('')
// template 相关
const templateData = {
  'template-2': {
    width: 1920,
    height: 234,
    size: 5,
    formImgKey: 'backgroundImgFileId',
    reviewImgKey: 'backgroundImg'
  },
  'template-4': {
    width: 1920,
    height: 400,
    size: 5,
    formImgKey: 'backgroundImgFileId2',
    reviewImgKey: 'backgroundImg2'
  },
  'template-5': {
    width: 460,
    height: 276,
    size: 5,
    formImgKey: 'backgroundImgFileId3',
    reviewImgKey: 'backgroundImg3'
  }
}
const currentTemplateData = computed(() => {
  return templateData[`template-${form.templateId}`] || {}
})
const templateMapKey = computed(() => {
  const { formImgKey, reviewImgKey } = currentTemplateData.value
  return { formImgKey, reviewImgKey }
})
const uploadBackgroundImgTips = computed(() => {
  const { width, height, size } = currentTemplateData.value
  return `建议上传尺寸：${width}px*${height}px，${size}M以内`
})
const cutBackgroundSize = computed(() => {
  const { width, height } = currentTemplateData.value
  return { width, height }
})
const reviewData = reactive({
  backgroundImg: '',
  backgroundImg2: '',
  backgroundImg3: ''
})
const addForm = reactive({
  fullName: '',
  type: '',
  nature: ''
})
const applyMethodsData = computed({
  get() {
    const { applyType, applyAddress, extraNotifyAddress, deliveryWay } = form
    return {
      applyType: applyType || '',
      applyAddress: applyAddress || '',
      extraNotifyAddress: extraNotifyAddress || '',
      deliveryWay: Array.isArray(deliveryWay) ? deliveryWay : []
    }
  },
  set(val: any) {
    Object.keys(val).forEach((key) => {
      form[key] = val[key]
    })
  }
})
const rules = reactive({
  title: [
    {
      required: true,
      message: '请输入公告标题',
      trigger: 'blur'
    }
  ],
  homeColumnId: [
    {
      required: true,
      message: '请选择所属栏目',
      trigger: 'change'
    }
  ],
  content: [
    {
      required: true,
      message: '请输入公告详情',
      trigger: 'blur'
    }
  ],
  companyTxt: [
    {
      required: true,
      message: '请选择单位',
      trigger: 'blur'
    }
  ],
  isCooperation: [
    {
      required: true,
      message: '请选择合作类型',
      trigger: 'change'
    }
  ],
  companyId: [
    {
      required: true,
      message: '请选择单位',
      trigger: 'blur'
    }
  ]
})
const fromData = ref()
const showJobAdd = ref()
const editorRef = ref()
const jobFile = ref()
const route = useRoute()
const router = useRouter()
const isCooperation = computed(() => form.isCooperation === '1')
const isJobLength = computed(() => !!jobList.value.length)
const hasAccount = computed(() => subAccountConfig.value?.total !== '0')
const showBackgroundUpload = computed(() => {
  // 展示背景设置项
  const id = [2, 4, 5]
  return id.includes(Number(form.templateId))
})

const handleDate = (obj: any) => {
  Object.assign(attributeData.value, obj)
}

const getData = async () => {
  const resp = await getAddParams()
  // 只赋值已声明变量
  Object.keys(resp).forEach((key) => {
    if (key in form) form[key] = resp[key]
    if (key === 'companyTypeList') companyTypeList.value = resp[key]
    if (key === 'companyNatureList') companyNatureList.value = resp[key]
    if (key === 'columnList') columnList.value = resp[key]
    if (key === 'notOverseasAttributeList') notOverseasAttributeList.value = resp[key]
    if (key === 'overseasAttributeList') overseasAttributeList.value = resp[key]
    if (key === 'templateList') templateList.value = resp[key]
    if (key === 'backgroundImgFileTypeList') backgroundImgFileTypeList.value = resp[key]
  })
  // 编辑公告
  if (route.params.id) {
    const {
      announcementInfo,
      announcementInfo: { backgroundImg, backgroundImg2, backgroundImg3 },
      announcementJobList,
      fileList: fileListResp
    } = await getAnnouncementEditInit({ id: route.params.id })
    Object.assign(form, announcementInfo)
    isEdit.value = true
    fileList.value = fileListResp
    articleId.value = announcementInfo.articleId
    form.announcementId = announcementInfo.announcementId
    imageUrl.value = announcementInfo.coverThumb
    jobList.value = announcementJobList
    natureTxt.value = announcementInfo.companyNatureTxt
    typeTxt.value = announcementInfo.companyTypeTxt
    companyDeliveryType.value = Number(announcementInfo.companyDeliveryType)
    companyDeliveryTypeTxt.value = announcementInfo.companyDeliveryTypeTxt
    columnTxt.value = announcementInfo.columnTxt
    form.companyTxt = announcementInfo.companyName
    status.value = announcementInfo.status
    form.deliveryWay = announcementInfo.deliveryWay ? announcementInfo.deliveryWay?.split(',') : []
    form.activityAnnouncement = announcementInfo.activityAnnouncement
      ? announcementInfo.activityAnnouncement?.split(',')
      : []
    reviewData.backgroundImg = backgroundImg
    reviewData.backgroundImg2 = backgroundImg2
    reviewData.backgroundImg3 = backgroundImg3
    Object.keys(attributeData.value).forEach((key) => {
      attributeData.value[key] = announcementInfo[key]
    })

    loading.value = false
  } else {
    // 新增
    const { companyTypeList: cList, companyNatureList: nList } = await getUnitList()
    typeList.value = cList
    companyNatureList.value = nList
    loading.value = false
  }
}

const getHwActivityList = async () => {
  const res = await hwActivityList({})
  activityList.value = res || []
}

function remoteMethod(params: any) {
  if (params !== '') {
    setTimeout(async () => {
      const { list } = await getCompanyTypeList({
        type: form.isCooperation,
        fullName: params
      })
      companyTypeList.value = list
    }, 1000)
  }
  watch(
    () => companyTypeList.value,
    () => {
      if (companyTypeList.value.length === 0 && !isCooperation.value && params !== '') {
        addCompany.value = '2'
      } else {
        addCompany.value = '1'
      }
    },
    { immediate: true }
  )
}

function handleChange(val: string) {
  form.companyId = val
  const res = companyTypeList.value.filter((item: any) => item.id.indexOf(val) > -1)
  if (res.length > 0) {
    natureTxt.value = res[0].natureTxt
    typeTxt.value = res[0].typeTxt
    companyDeliveryTypeTxt.value = res[0].deliveryTypeTxt
    companyDeliveryType.value = Number(res[0].deliveryType)
    subAccountConfig.value = res[0].subAccountConfig
    form.deliveryWay = ['66']
  } else if (val === '') {
    natureTxt.value = ''
    typeTxt.value = ''
    companyDeliveryTypeTxt.value = ''
    companyDeliveryType.value = null
  }
}

onMounted(() => {
  loading.value = true
  getData()
  getHwActivityList()
})
watch(
  () => form.isCooperation,
  async () => {
    const { list } = await getCompanyTypeList({ type: form.isCooperation })
    companyTypeList.value = list
  },
  { immediate: true, deep: true }
)

const reset = () => {
  Object.assign(form, {
    applyType: '',
    applyAddress: '',
    extraNotifyAddress: '',
    deliveryWay: [],
    isCooperation: '2',
    addressHideStatus: '2',
    content: ''
  })
  Object.keys(attributeData.value).forEach((key) => {
    attributeData.value[key] = ''
  })
  jobList.value = []
  status.value = ''
  natureTxt.value = ''
  typeTxt.value = ''
  companyDeliveryTypeTxt.value = ''
  form.companyId = ''
  companyDeliveryType.value = null
  editorRef.value?.clearEditor()
  fromData.value?.resetFields()
  fileList.value = []
  jobFile.value?.clear()
}

async function announcementSubmit(type: String) {
  const postData = {
    ...form,
    ...attributeData.value,
    fileIds: fileList.value?.map((item: any) => item.id).join(),
    deliveryWay: form.deliveryWay?.join(),
    activityAnnouncement: form.activityAnnouncement?.join()
  }
  const { id } = route.params.id
    ? await announcementEdit(postData)
    : await announcementAdd(postData)
  if (type === '1') {
    proxy.mittBus.emit('closeCurrentViewTag')
    router.replace({ path: `/cms/announcementEdit/${id}` })
  } else {
    if (route.params.id) {
      proxy.mittBus.emit('closeCurrentViewTag')
      return
    }

    ElMessageBox.alert('提交成功', {
      confirmButtonText: '我知道了',
      callback: () => {
        reset()
        try {
          fromData.value?.resetFields()
        } catch (err) {
          return null
        }
        return true
      }
    })
  }
}

function submitAnnounce(type: String) {
  const { backgroundImgFileType } = form
  const { formImgKey } = templateMapKey.value
  const current = formatDate(new Date(), 'YYYY-mm-dd')
  // console.log(jobList.value)
  form.jobIds = JSON.stringify(
    jobList.value.map((item: any) => {
      return {
        id: item.id,
        isTemp: item.isTemp,
        jobId: item.jobId
      }
    })
  )
  // console.log(form.jobIds)
  if (form.jobIds.length === 0) {
    ElMessage.error('职位不能为空')
    return
  }
  if (form.periodDate !== '' && current > form.periodDate) {
    ElMessage.error('截止日期不能早于当前')
    return
  }
  const isRequiredBgImg =
    showBackgroundUpload.value &&
    backgroundImgFileType === '3' &&
    (!form[formImgKey] || form[formImgKey] === '0')
  if (isRequiredBgImg) {
    ElMessage.error('请上传自定义背景图')
    return
  }
  if (route.params.id) {
    form.announcementId = route.params.id
  }
  form.submitType = type
  fromData.value.validate(async (valide: Boolean) => {
    if (valide) {
      const { titleOnly, uid } = await getAnnouncementTitleOnly({
        title: form.title,
        id: articleId.value
      })
      if (titleOnly) {
        ElMessageBox.confirm(`标题与公告：${uid} 重复，确定要提交吗？`, '提示')
          .then(() => {
            announcementSubmit(type)
          })
          .catch(() => {})
      } else {
        announcementSubmit(type)
      }
    }
  })
}

const testHtml = async () => {
  try {
    const res = await identityEditor({ content: form.content })
    if (isJobLength.value && form.companyId && res.companyId !== form.companyId) {
      ElMessage.error('识别失败！单位信息发生变化')
      return
    }
    Object.assign(form, res, { jobIds: [] })
    form.deliveryWay = res.deliveryWay ? res.deliveryWay.split(',') : []
    editorRef.value?.updateEditor(res.content)
    natureTxt.value = res.companyNatureTxt
    typeTxt.value = res.companyTypeTxt
    companyDeliveryTypeTxt.value = res.companyDeliveryTypeTxt
    companyDeliveryType.value = Number(res.companyDeliveryType)
    // isAttachmentNotice.value = res.isAttachmentNotice
  } catch {
    //
  }
}

const openJobAdd = () => {
  if (form.companyId === '') {
    ElMessage.error('请先选择单位')
  } else {
    showJobAdd.value?.open(form.periodDate, form.companyId)
  }
}

// function addWaitCooperation() {
//   dialogAddVisible.value = true
// }

// 略缩图上传
// const beforeAvatarUpload = (file: any) => {
//   const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
//   const isLt5M = file.size / 1024 / 1024 < 5
//   if (!isJPG) {
//     ElMessage.error('上传图片只能是 JPG或PNG 格式!')
//   }
//   if (!isLt5M) {
//     ElMessage.error('图片上传不能超过5M')
//   }
//   return isJPG && isLt5M
// }

// const handleAvatarSuccess = (res: any, file: any) => {
//   form.coverThumb = res.data.fullUrl
//   imageUrl.value = URL.createObjectURL(file.raw)
// }

// 新增没有的非合作单位
const submitCompany = async () => {
  await addUnit(addForm)
  dialogAddVisible.value = false
  const { list } = await getCompanyTypeList({ type: form.isCooperation })
  companyTypeList.value = list
}

/*
  1、新增 ：子组件传数据直接unshift()
  2、修改 ：子组件传一个index，判断是否有index，有就是修改splice()
  3、批量 ：子组件传一个batch是否为true判断是否为批量，unshift()进jobList
*/
const getJobTempData = (val: any) => {
  const { res, batch, index } = val
  // console.log(res, batch, index)
  if (batch) {
    jobList.value.unshift(...res)
  } else if (typeof index === 'number') {
    jobList.value.splice(index, 1, val)
  } else {
    jobList.value.unshift(val)
  }
}

const batchUpload = (file: any) => {
  const { url } = file.data
  jobBatchAdd({
    filePath: url,
    announcementId: form.announcementId,
    companyId: form.companyId
  })
    .then((res) => {
      getJobTempData({ res, batch: true })
    })
    .catch((res: any) => {
      ElMessage.closeAll()
      ElMessage({
        message: res,
        type: 'error',
        duration: 10000,
        showClose: true
      })
    })
}

// 此删除仅在前端页面做删除，到最后发布发请求把页面有的jobId传过去，后端做删除处理
const deleteJob = (val: any) => {
  ElMessageBox.confirm(`删除后无法恢复，确定删除？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(async () => {
    await temporaryJobDelete({ id: val.id, isTemp: val.isTemp, jobId: val.jobId })
    jobList.value = jobList.value.filter((item: any) => item.id !== val.id)
  })
}

/* open里需要传几个参数给子组件
1、date：有效期，在子组件做职位有效期不能早于或晚于公告有效期判断
2、announcementCompanyId：单位id，去拿职位模板用
3、id：通过审核的正式职位id
4、jobTemplateId：临时职位id，用于未发布临时职位编辑修改
5、isTemp：是否临时职位
6、announceId：公告id，传给后端，有就是编辑，没有就是新增
7、index：传给子组件，index===number就是修改职位
8、jobId：职位id
*/
const editJob = (val: any, index: number) => {
  showJobAdd.value?.open('', form.companyId, val.id, val.isTemp, route.params.id, index, val.jobId)
}
const copyJob = async (val: any) => {
  const jobTempData = await temporaryJobCopy({
    id: val.id,
    isTemp: val.isTemp,
    jobId: val.jobId
  })
  jobList.value.unshift(jobTempData)
}

// 公告有效期不能早于当前日也不能晚于两年时间
const handleDisableDate = (time: Date) => {
  const oneDayInMs = 24 * 60 * 60 * 1000
  const twoYearsInMs = 365 * 2 * oneDayInMs
  const now = Date.now()

  return time.getTime() < now - oneDayInMs || time.getTime() > now + twoYearsInMs
}

// const realtionChange = (val: any) => {
//   if (!val) {
//     state.form.relationCompanyIds = []
//   }
// }
// const offline = async (val: any, actionType: any, status: any) => {
//   await announcementJobOnline({ id: val.id, actionType })
//   let index = 0
//   const res = state.jobList.filter((item: any, ind: number) => {
//     if (item.id === val.id) {
//       index = ind
//       return true
//     }
//     return false
//   })[0]
//   const data = { ...res, status }
//   state.jobList.splice(index, 1, data)
// }

function changeCoopertaion(val: string) {
  if (val) {
    form.companyId = ''
    form.companyTxt = ''
    natureTxt.value = ''
    typeTxt.value = ''
    companyDeliveryTypeTxt.value = ''
    companyDeliveryType.value = null
  }
}
const openJobFileUpload = () => {
  jobFile.value?.openJobFileUpload()
}

// 暴露 batchUploadMessage 和 handleDisableDate
function batchUploadMessage() {
  ElMessage.error('请先选择单位')
}
</script>

<style lang="scss" scoped>
.main {
  :deep(.information) {
    .cell {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      max-height: calc(2 * 1.5);
      line-height: 1.5;
      word-break: break-word;
      white-space: normal;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  :deep(.home-colunm) {
    width: 100%;
    .el-cascader {
      width: 100%;
    }
  }

  :deep(.left-card-n1) {
    .el-card__body {
      padding-top: 12px;
      padding-bottom: 10px;
    }
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  :deep(.left-card-n2) {
    .el-card__header {
      padding-top: 10px;
      padding-bottom: 10px;
    }
    .el-card__body {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  :deep(.el-form-item__error) {
    font-size: 14px;
    line-height: 1.5;
    margin-top: 0px;
    padding: 2px 0;
  }
}

/* 只在当前页面覆盖layout-scrollbar的padding */
:deep(.layout-container) {
  .layout-scrollbar {
    padding: 0 !important;
  }
}

.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
:deep(.el-upload--text) {
  display: flex;
  align-items: center;
  justify-content: space-around;
  .avatar-uploader-icon {
    width: 100px;
    height: 100px;
  }
  .logo {
    font-size: 12px;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
:deep(.el-dialog__footer) {
  text-align: center;
}
.el-checkbox.el-checkbox--large {
  height: auto;
}
:deep(.el-form-item__label) {
  height: auto;
  text-align: right;
}

.mt20,
.job-card {
  margin-top: 15px !important;
}

.job-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .jc {
    justify-content: normal;
    button {
      margin-left: 20px;
    }
  }
}
</style>

<style lang="scss">
.information-class {
  width: 300px;
}
</style>
