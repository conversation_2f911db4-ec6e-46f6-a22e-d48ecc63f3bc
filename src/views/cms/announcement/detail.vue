<template>
  <div>
    <div class="box" v-loading="loading">
      <div class="floor">
        <el-button id="btnInfo">公告详情</el-button>
        <el-button id="jobList" @click="toJobList" v-if="editorType !== '1'">职位列表</el-button>
        <el-button id="auditBtn">审核处理</el-button>
      </div>
      <div class="title fw-bold mb-10 color-dark">标题 : {{ baseInfo.title }}</div>
      <div class="title fw-bold mb-10 color-dark" id="info">详情</div>
      <!-- 没有审核通过历史的公告详情-单个审核 -->
      <WithoutEditor
        :fileList="fileList"
        :baseInfo="baseInfo"
        :tableData="jobList"
        v-if="isEditorType"
      />
      <!-- 1、有审核通过历史的公告详情-仅修改公告详情 -->
      <EditAnnouncementDetail
        v-if="editorType === '1'"
        :baseInfo="baseInfo"
        :editorType="editorType"
      />
      <!-- 2、有审核通过历史的公告详情-仅新增职位-->
      <AddJobEditor v-if="editorType === '2'" :baseInfo="baseInfo" />
      <!-- 3、仅修改职位 -->
      <EditAnnounceJobDetails
        :baseInfo="baseInfo"
        :editorType="editorType"
        v-if="editorType === '3'"
      />
      <!-- 4、修改职位+新增职位-->
      <AddEditJob v-if="editorType === '4'" :baseInfo="baseInfo" />
      <!-- 5、修改公告+修改职位 -->
      <EditAnnoncementJob v-if="editorType === '5'" :baseInfo="baseInfo" />
      <!-- 6、修改公告+新增职位 -->
      <EditAnnounceAddJob v-if="editorType === '6'" :baseInfo="baseInfo" />
      <!-- 7、修改公告+修改职位+新增职位 -->
      <div v-if="editorType === '7'">
        <EditAnnoncementJob :baseInfo="baseInfo" />
        <AddJobEditor :baseInfo="baseInfo" />
      </div>

      <!-- 审核处理意见 -->
      <div class="pb-20">
        <div class="title fw-bold mb-10 color-dark" id="audit">审核处理意见</div>
        <el-input
          type="textarea"
          rows="5"
          resize="none"
          v-model="opinion"
          placeholder="审核拒绝须明确拒绝原因"
        ></el-input>
      </div>
      <!-- 合作单位的所属栏目 -->
      <div class="is-cooperation" v-if="historyStatus === 2">
        <el-form :model="form" label-width="130px">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="所属栏目">
                <Colunm v-model="form.homeColumnId" :columnList="columnList" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属副栏目">
                <SubColumn v-model="form.homeSubColumnIds" :columnList="columnList" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="官网公告调用属性">
            <AttributeCheck
              :checkBoxList="notOverseasAttributeList"
              v-model="form.comboAttribute"
              v-model:date="attributeData"
            />
          </el-form-item>

          <el-form-item class="flex-1" label="高才海外调用属性" prop="overseasAttribute">
            <AttributeCheck
              :checkBoxList="overseasAttributeList"
              v-model="form.overseasAttribute"
              v-model:date="attributeData"
              :date-type="'globalAttributeTimeDate'"
            ></AttributeCheck>
          </el-form-item>

          <el-form-item label="页面模板">
            <el-select v-model="form.templateId" filterable clearable>
              <el-option
                v-for="item in templateList"
                :key="item.k"
                :value="item.k"
                :label="item.v"
              ></el-option>
            </el-select>
          </el-form-item>

          <div class="background-from-item" v-if="showBackgroundUpload">
            <el-form-item label="背景图">
              <el-radio-group v-model="attributeData.backgroundImgFileType">
                <el-radio v-for="item in backgroundImgFileTypeList" :key="item.k" :label="item.k">{{
                  item.v
                }}</el-radio>
              </el-radio-group>
              <br />
              <template v-if="attributeData.backgroundImgFileType === '3'">
                <CutOutUploadImg
                  v-model:file-id="attributeData[templateMapKey.formImgKey]"
                  :uploadText="uploadBackgroundImgTips"
                  width="88px"
                  height="auto"
                  :width-size="cutBackgroundSize.width"
                  :height-size="cutBackgroundSize.height"
                  v-model:full-url="reviewData[templateMapKey.reviewImgKey]"
                />
              </template>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <!-- 审核按钮 -->
      <div class="mt-15">
        <el-button :disabled="disabled" type="primary" @click="auditHandle('1')"
          >审核通过</el-button
        >
        <el-button :disabled="disabled" type="primary" @click="auditHandle('2')"
          >审核拒绝</el-button
        >
        <el-button
          v-if="isCooperation === 2"
          :disabled="disabled"
          type="primary"
          @click="auditEditHandle('2')"
          >审核拒绝并编辑</el-button
        >
      </div>

      <!-- 审核处理历史 -->
      <div class="history" v-show="historyList.length !== 0">
        <div>
          <el-button @click="showRecord = !showRecord">审核处理历史</el-button>
        </div>
        <div v-show="showRecord">
          <el-table border :data="historyList" class="mt-15">
            <el-table-column prop="handlerName" label="审核人"></el-table-column>
            <el-table-column prop="addTime" label="审核时间" min-width="120px"></el-table-column>
            <el-table-column prop="auditStatusTitle" label="审核状态"></el-table-column>
            <el-table-column
              prop="opinion"
              min-width="150px"
              label="处理意见"
              show-overflow-tooltip
            ></el-table-column>
            <template #empty>
              <el-empty description="暂无记录" :image-size="100"></el-empty>
            </template>
          </el-table>
          <Pagination
            v-if="historyList.length"
            @change="handlePaginationChange"
            class="mt-15"
            :total="pagination.total"
          />
        </div>
      </div>

      <!-- 添加浮窗提示 -->
      <div v-if="showBatchAuditTip" class="batch-audit-tip">
        <div class="tip-content">
          <span>剩余待审核: </span>
          <span class="count">{{ remainingCount }}</span>
          <span>条</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { getCurrentInstance, ref, onMounted, nextTick, computed } from 'vue'
import Pagination from '/@/components/base/paging.vue'
import { useRoute, useRouter } from 'vue-router'
import WithoutEditor from './component/withoutEditor.vue'
import EditAnnouncementDetail from './component/editAnnouncementDetail.vue'
import AddJobEditor from './component/addJobEditor.vue'
import AddEditJob from './component/addEditJob.vue'
import EditAnnoncementJob from './component/editAnnoncementJob.vue'
import EditAnnounceAddJob from './component/editAnnounceAddJob.vue'
import { announcementAuditHandle, getAddParams, getAuditDetail } from '/@/api/announcement'
import Colunm from '/@/components/base/colunm.vue'
import SubColumn from '/@/components/base/subColumn.vue'
import EditAnnounceJobDetails from './component/editAnnounceJobDetails.vue'
import AttributeCheck from '/@/components/base/attributeCheck.vue'
import CutOutUploadImg from '/@/components/upload/cutOutUploadImg.vue'

interface BaseInfo {
  id: string
  title: string
  editorType: string
  authJobList?: any[]
  modifyBeforeList?: any[]
  [key: string]: any
}

interface ColumnItem {
  id: string | number
  name: string
  [key: string]: any
}

interface AttributeItem {
  id: string | number
  name: string
  [key: string]: any
}

interface TemplateItem {
  k: string
  v: string
}

interface BackgroundImgType {
  k: string
  v: string
}

defineOptions({ name: 'announcementAuditDetail' })

const loading = ref(false)
const disabled = ref(false)
const showRecord = ref(false)
const pagination = ref({
  total: 0,
  page: 1,
  limit: 20
})
const opinion = ref('')
const jobIds = ref('')
const editorType = ref('')
const isCooperation = ref(0)
const historyStatus = ref(0)
const jobList = ref<any[]>([])
const baseInfo = ref<BaseInfo>({} as BaseInfo)
const columnList = ref<ColumnItem[]>([])
const notOverseasAttributeList = ref<AttributeItem[]>([])
const overseasAttributeList = ref<AttributeItem[]>([])
const templateList = ref<TemplateItem[]>([])
const modifyBeforeList = ref([])
const form = ref({
  homeColumnId: '',
  homeSubColumnIds: '',
  templateId: '',
  comboAttribute: [],
  overseasAttribute: []
})
const historyList = ref([])
const fileList = ref([])
const attributeData = ref({
  indexTopEndTime: '',
  columnTopEndTime: '',
  doctorPushEndTime: '',
  overseasIndexTopEndTime: '',
  overseasColumnTopEndTime: '',
  backgroundImgFileType: '1',
  backgroundImgFileId: '', // 背景图文件
  backgroundImgFileId2: '', // 高级模版2背景图片
  backgroundImgFileId3: '' // 高级模版3背景图片
})
const backgroundImgFileTypeList = ref<BackgroundImgType[]>([])
const reviewData = ref({
  backgroundImg: '',
  backgroundImg2: '',
  backgroundImg3: ''
})

const showBackgroundUpload = computed(() => {
  // 展示背景设置项
  const id = [2, 4, 5]
  const { templateId } = form.value
  return id.includes(Number(templateId))
})

// 页面模板映射表
const templateData = {
  'template-2': {
    width: 1920,
    height: 234,
    size: 5,
    formImgKey: 'backgroundImgFileId',
    reviewImgKey: 'backgroundImg'
  },
  'template-4': {
    width: 1920,
    height: 400,
    size: 5,
    formImgKey: 'backgroundImgFileId2',
    reviewImgKey: 'backgroundImg2'
  },
  'template-5': {
    width: 460,
    height: 276,
    size: 5,
    formImgKey: 'backgroundImgFileId3',
    reviewImgKey: 'backgroundImg3'
  }
}

const currentTemplateData = computed(() => {
  const { templateId } = form.value
  return templateData[`template-${templateId}`] || {}
})

const templateMapKey = computed(() => {
  const { formImgKey, reviewImgKey } = currentTemplateData.value
  return { formImgKey, reviewImgKey }
})

const uploadBackgroundImgTips = computed(() => {
  const { width, height, size } = currentTemplateData.value
  return `建议上传尺寸：${width}px*${height}px，${size}M以内`
})

const cutBackgroundSize = computed(() => {
  const { width, height } = currentTemplateData.value
  return { width, height }
})

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance() as any

const nextIds = route.query.nextIds as string

const remainingCount = computed(() => {
  if (!nextIds) return 0
  return nextIds.split(',').length
})

const showBatchAuditTip = computed(() => {
  return nextIds
})

const isEditorType = computed(() => editorType.value === '9')

const getList = async () => {
  const {
    baseInfo: baseInfoData,
    handleLogList,
    historyStatus: historyStatusData,
    isCooperation: isCooperationData,
    jobList: jobListData,
    fileList: fileListData
  } = await getAuditDetail({ id: route.params.id })
  historyList.value = handleLogList.list
  baseInfo.value = baseInfoData
  fileList.value = fileListData
  jobList.value = baseInfoData.authJobList ? baseInfoData.authJobList : jobListData
  jobIds.value = jobList.value.length ? jobList.value.map((item: any) => item.id).join() : ''
  historyStatus.value = historyStatusData
  editorType.value = baseInfoData.editorType
  isCooperation.value = isCooperationData
  modifyBeforeList.value = baseInfoData.modifyBeforeList ? baseInfoData.modifyBeforeList : []
  pagination.value.total = Number(handleLogList.page.total)
  const { backgroundImg: bgImg, backgroundImg2, backgroundImg3 } = baseInfoData
  reviewData.value = { backgroundImg: bgImg, backgroundImg2, backgroundImg3 }
  nextTick(() => {
    Object.keys(form.value).forEach((key) => {
      form.value[key] = baseInfoData[key]
    })
    Object.keys(attributeData.value).forEach((key) => {
      attributeData.value[key] = baseInfoData[key]
    })
  })
  if (historyStatus.value === 2) {
    const resp = await getAddParams()
    if (resp) {
      Object.keys(resp).forEach((key) => {
        if (key === 'columnList') {
          columnList.value = resp[key]
        } else if (key === 'notOverseasAttributeList') {
          notOverseasAttributeList.value = resp[key]
        } else if (key === 'overseasAttributeList') {
          overseasAttributeList.value = resp[key]
        } else if (key === 'templateList') {
          templateList.value = resp[key]
        } else if (key === 'backgroundImgFileTypeList') {
          backgroundImgFileTypeList.value = resp[key]
        } else {
          proxy[key] = resp[key]
        }
      })
    }
  }
}
getList()

const handlePaginationChange = (data: any) => {
  pagination.value.limit = data.limit
  pagination.value.page = data.page
  getList()
}

const auditHandle = async (auditStatus: string) => {
  const audit = {
    id: baseInfo.value.id,
    auditStatus,
    editorType: editorType.value,
    opinion: opinion.value,
    jobIds: jobIds.value,
    ...form.value,
    ...attributeData.value
  }
  const { formImgKey } = templateMapKey.value
  const { backgroundImgFileType } = audit
  const isRequiredBgImg =
    showBackgroundUpload.value &&
    backgroundImgFileType === '3' &&
    (!audit[formImgKey] || audit[formImgKey] === '0')
  // 背景图
  if (isRequiredBgImg) {
    ElMessage.error('请上传自定义背景图')
    return
  }
  await announcementAuditHandle(audit)
  proxy.mittBus.emit('closeCurrentViewTag')
  // 如果是从批量审核过来的，则跳转到下一个审核页面
  if (nextIds) {
    // 去掉当前的id
    const nextIdsArray = nextIds.split(',')
    const nextId = nextIdsArray[0]
    // 去掉当前的id
    nextIdsArray.splice(nextIdsArray.indexOf(nextId), 1)
    const newNextIds = nextIdsArray.join(',')
    if (newNextIds.length) {
      router.push({
        path: `/cms/announcementAuditDetail/${nextId}`,
        query: { nextIds: newNextIds }
      })
    } else {
      router.push({
        path: `/cms/announcementAuditDetail/${nextId}`
      })
    }
    return
  }
  if (isCooperation.value === 1) {
    router.replace({ name: 'announcementAuditList' })
  } else {
    router.replace({ name: 'cmsAnnouncementAuditList' })
  }
}
const auditEditHandle = async (auditStatus: string) => {
  await auditHandle(auditStatus)
  proxy.mittBus.emit('closeCurrentViewTag')
  router.replace({ path: `/cms/announcementEdit/${baseInfo.value.id}` })
}
// 加载的时候再拿
onMounted(() => {
  const btnInfo = document.querySelector('#btnInfo')
  const audit = document.querySelector('#auditBtn')
  btnInfo?.addEventListener(
    'click',
    () => {
      document.querySelector('#info')?.scrollIntoView()
    },
    false
  )
  audit?.addEventListener(
    'click',
    () => {
      document.querySelector('#audit')?.scrollIntoView()
    },
    false
  )
})
const toJobList = () => {
  document.querySelector('#job')?.scrollIntoView()
}
</script>

<style scoped lang="scss">
.title {
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px var(--el-border-color) var(--el-border-style);
  margin: 20px 0;
  padding: 10px 5px;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  a {
    text-decoration: none;
    color: var(--color-primary);
  }

  .history {
    margin-top: 20px;
  }
  .floor {
    display: flex;
    flex-direction: column;
    position: fixed;
    right: 34px;
    z-index: 100;
    .el-button {
      margin: 5px 0;
    }
  }
}
:deep() {
  .background-from-item {
    .el-form-item__content {
      display: block;
    }

    .banner-img {
      max-width: 600px;
    }
  }
}

.batch-audit-tip {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;

  .tip-content {
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 12px 20px;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.3s ease;

    .count {
      color: #409eff;
      font-weight: bold;
      font-size: 16px;
      margin: 0 4px;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
