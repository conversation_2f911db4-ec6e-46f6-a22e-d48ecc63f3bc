<template>
  <!-- 修改公告+修改职位 -->
  <div class="pb-10 border-bottom">
    <div class="fw-bold mb-10 color-dark">
      修改类型：{{ baseInfo.editorTypeTxt
      }}<router-link
        class="ml-10 bg-primary td-none"
        :to="`/cms/announcementDetail/${baseInfo.id}/${baseInfo.status}`"
        >预览原公告</router-link
      >
    </div>
    <div class="announcement">
      <div class="fw-bold mb-10 color-dark">修改前：</div>
      <div class="detail-main" v-html="baseInfo.announcementHandleBefore"></div>
      <div class="flex mb-10" v-if="baseInfo.fileHandleBefore?.length">
        <JobAttachment :fileList="baseInfo.fileHandleBefore" />
      </div>
      <div class="fw-bold mb-10 color-dark">修改后：</div>
      <div class="detail-main" v-html="baseInfo.announcementHandleAfter"></div>
      <div class="flex mb-10" v-if="baseInfo.fileHandleAfter?.length">
        <JobAttachment :fileList="baseInfo.fileHandleAfter" />
      </div>
    </div>
    <div class="job-list" v-for="(item, index) in baseInfo.modifyBeforeList" :key="index">
      <div class="fw-bold mb-10 color-dark">职位名称：{{ item.jobHandleBefore.name }}</div>
      <div class="fw-bold mb-10 color-dark">修改前：</div>
      <div class="before">
        <div class="span-3 mr-10" v-show="item.jobHandleBefore.duty">
          岗位职责：{{ item.jobHandleBefore.duty }}
        </div>
        <div class="span-3 mr-10" v-show="item.jobHandleBefore.requirement">
          任职要求：{{ item.jobHandleBefore.requirement }}
        </div>
        <div class="span-3" v-show="item.jobHandleBefore.remark">
          其他说明：{{ item.jobHandleBefore.remark }}
        </div>
      </div>
      <div class="fw-bold mb-10 color-dark">修改后：</div>
      <div class="after">
        <div class="span-3 mr-10" v-show="item.jobHandleAfter.duty">
          岗位职责：{{ item.jobHandleAfter.duty }}
        </div>
        <div class="span-3 mr-10" v-show="item.jobHandleAfter.requirement">
          任职要求：{{ item.jobHandleAfter.requirement }}
        </div>
        <div class="span-3" v-show="item.jobHandleAfter.remark">
          其他说明：{{ item.jobHandleAfter.remark }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import JobAttachment from './jobAttachment.vue'

defineOptions({ name: 'editAnnoncementJob' })

defineProps({
  baseInfo: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style lang="scss" scoped>
.announcement,
.job-list {
  border-bottom: 1px solid rgba($color: #000000, $alpha: 0.3);
  margin-bottom: 10px;
  .detail-main {
    :deep() {
      p {
        color: rgba(51, 51, 51, 0.8);
        font-size: 14px;
        line-height: 2;
      }

      img {
        display: block;
        margin: 20px auto;
        max-width: 100%;
      }

      table {
        margin: 20px auto;
        width: 100%;
        border-collapse: collapse;

        th {
          background-color: #fafafc;
        }

        th,
        td {
          padding: 10px 0;
          text-align: center;
          border: 1px solid #ccc;
        }
      }
    }
  }
}
.after,
.before {
  div {
    margin: 5px 0;
  }
}
</style>
