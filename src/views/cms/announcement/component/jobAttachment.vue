<template>
  <div>
    <div class="fw-bold mb-10 mt-10 color-dark">职位附件</div>
    <div class="file-list">
      <a
        :href="item.path"
        :download="item.name"
        target="_blank"
        v-for="item in fileList"
        :key="item.id"
        >{{ item.name }}</a
      >
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'jobAttachment',
  props: {
    fileList: {
      type: Array,
      default: () => []
    }
  }
})
</script>

<style lang="scss" scoped>
@use '/src/theme/app' as *;
.file-list {
  a {
    margin: 15px 0 0 16px;
    text-decoration: none;
    color: #409eff;
    @include utils-ellipsis-lines(1, 1.5);
  }
}
</style>
