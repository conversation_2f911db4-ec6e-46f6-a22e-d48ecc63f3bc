<template>
  <div>
    <el-drawer v-model="showJobAdd" direction="rtl" size="80%" :before-close="closeJobAdd">
      <template #header>
        <p class="fs-16 fw-bold color-black">{{ title }}</p>
      </template>
      <div class="mb-14 ml-24" v-if="!isEdit">
        添加方式：
        <el-radio-group v-model="addType">
          <el-radio :label="1">单个新增职位</el-radio>
          <el-radio :label="2">批量新增职位</el-radio>
        </el-radio-group>
      </div>
      <div class="single">
        <div class="pb-18 ml-24" v-if="!isEdit && addType == 1">
          职位模板：
          <el-select
            placeholder="请输入现有职位名称/编号，选择所要复用的职位模板"
            style="width: 400px"
            clearable
            remote
            filterable
            :remote-method="filterJobTemplate"
            v-model="singleForm.jobTemplateId"
            @change="changeTemplate"
          >
            <el-option
              v-for="item in templateList"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </div>
        <el-form
          label-width="100px"
          label-position="right"
          :model="singleForm"
          :rules="rules"
          ref="form"
        >
          <block v-if="addType === 1">
            <div class="title line-1 mb-20 ml-5 fs-16 fw-bold pl-15">基本信息</div>
            <div>
              <el-row :gutter="10">
                <el-col class="" :span="12">
                  <el-form-item label="职位名称" prop="name">
                    <el-input
                      v-model="singleForm.name"
                      show-word-limit
                      clearable
                      placeholder="请填写职位名称"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col class="" :span="12">
                  <el-form-item label="职位类型" prop="jobCategoryId">
                    <JobCategory v-model="singleForm.jobCategoryId" :multiple="false" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col class="" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="学历要求" prop="educationType">
                    <el-row :gutter="10">
                      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                        <Education v-model="singleForm.educationType" />
                      </el-col>
                      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                        <el-form-item :label-width="0" prop="code">
                          <el-input
                            type="input"
                            v-model="singleForm.code"
                            clearable
                            placeholder="职业代码"
                          ></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col class="" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="需求专业" prop="majorId">
                    <div class="flex">
                      <div class="flex-1 mr-10">
                        <MajorDialog
                          v-model="singleForm.majorId"
                          v-model:title="singleForm.majorTitle"
                        />
                      </div>
                      <div class="flex-1 mr-10">
                        <el-input
                          v-model="majorText"
                          placeholder="输入识别文案"
                          clearable
                          maxlength="2000"
                        />
                      </div>
                      <el-button type="primary" @click="recognition">识别</el-button>
                    </div>
                    <el-select class="mt-18" clearable remote filterable @change="changeMajor">
                      <el-option
                        v-for="item in jobList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col class="" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="工作性质" prop="natureType">
                    <WorkNature v-model="singleForm.natureType" />
                  </el-form-item>
                </el-col>
                <el-col class="" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <Salary
                    :data="{
                      wageType: singleForm.wageType,
                      wageId: singleForm.wageId,
                      minWage: singleForm.minWage,
                      maxWage: singleForm.maxWage,
                      isNegotiable: singleForm.isNegotiable
                    }"
                    @change="salarChage"
                  />
                </el-col>
              </el-row>
              <el-form-item label="其他要求">
                <div class="flex">
                  <div class="flex-1 mr-10">
                    <el-form-item label-width="0px" prop="experienceType">
                      <Experience
                        v-model="singleForm.experienceType"
                        placeholder="工作经验"
                        isLimit
                      />
                    </el-form-item>
                  </div>
                  <div class="flex-1 mr-10">
                    <el-form-item label-width="0px" prop="ageType">
                      <Age
                        el-type="input"
                        is-limit
                        v-model="singleForm.ageType"
                        placeholder="年龄"
                      />
                    </el-form-item>
                  </div>

                  <div class="flex-1 mr-10">
                    <el-form-item label-width="0px" prop="titleType">
                      <LevelTitle v-model="singleForm.titleType" isLimit placeholder="职称" />
                    </el-form-item>
                  </div>

                  <div class="flex-1 mr-10">
                    <el-form-item label-width="0px" prop="politicalType">
                      <Political
                        v-model="singleForm.politicalType"
                        placeholder="政治面貌"
                        isLimit
                      />
                    </el-form-item>
                  </div>
                  <div class="flex-1 mr-8">
                    <el-form-item label-width="0px" prop="abroadType">
                      <AbroadExperience
                        v-model="singleForm.abroadType"
                        placeholder="海外经验"
                        isLimit
                      />
                    </el-form-item>
                  </div>
                </div>
              </el-form-item>
            </div>
            <div class="title line-1 mb-20 ml-5 fs-16 fw-bold pl-15">职位详情</div>
            <div class="">
              <el-row :gutter="10">
                <el-col class="" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="招聘人数" prop="amount">
                    <el-input
                      v-model.trim="singleForm.amount"
                      placeholder="请输入具体招聘人数或者'若干'"
                      clearable
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col class="" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="工作地点" prop="provinceId">
                    <el-row :gutter="10">
                      <el-col :span="8">
                        <Region
                          v-model="regionValue"
                          v-model:province="singleForm.provinceId"
                          v-model:city="singleForm.cityId"
                        />
                      </el-col>
                      <el-col :span="16">
                        <el-form-item label-width="0px" prop="address">
                          <el-input
                            v-model="singleForm.address"
                            placeholder="请输入详细地址(非必填)"
                          ></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10">
                <el-col class="" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="用人部门" prop="department">
                    <el-input
                      v-model="singleForm.department"
                      clearable
                      placeholder="请输入用人部门"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col class="" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="职位福利" prop="welfareText">
                    <el-input
                      readonly
                      class="cursor-default"
                      v-model="welfareText"
                      @click="openDialogWelfare"
                      placeholder="请选择职位福利"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col class="" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="职位编制" prop="jobEstablishment">
                    <announcement-check-box
                      v-model="singleForm.establishmentType"
                      :check-box-list="establishmentTypeList"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col class="" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                  <el-form-item label="职位有效期" prop="periodDate">
                    <el-date-picker
                      class="w100"
                      v-model="singleForm.periodDate"
                      type="date"
                      placeholder="请选择职位有效期"
                      value-format="YYYY-MM-DD"
                      :disabled-date="handleDisabledDate"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="24">
                  <el-form-item label="岗位职责" prop="duty">
                    <el-input
                      v-model="singleForm.duty"
                      type="textarea"
                      :rows="4"
                      resize="none"
                      maxlength="2000"
                      placeholder="请填写岗位职责(0/2000)"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="24">
                  <el-form-item label="任职要求" prop="requirement">
                    <el-input
                      v-model="singleForm.requirement"
                      type="textarea"
                      :rows="4"
                      resize="none"
                      maxlength="2000"
                      placeholder="请填写任职要求(0/2000)"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="24">
                  <el-form-item label="其他说明" prop="remark">
                    <el-input
                      v-model="singleForm.remark"
                      type="textarea"
                      :rows="4"
                      resize="none"
                      maxlength="2000"
                      placeholder="请填写其他说明(0/2000)"
                    ></el-input>
                    <span class="line-1 color-danger fs-12">
                      *职位详情请勿输入单位邮箱，移动电话，性别歧视字眼及其他外链。
                    </span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div class="title line-1 mb-20 ml-5 fs-16 fw-bold pl-15">报名方式</div>
            <div class="pb-1">
              <ApplyMethods
                :isCooperation="isCooperation"
                :accountType="deliveryType"
                v-model="applyMethodsData"
                :isCooperationJob="false"
              />
            </div>
            <block v-if="isCooperation">
              <div class="title line-1 mb-20 ml-5 fs-16 fw-bold pl-15">投递限制</div>
              <div class="">
                <el-row :gutter="10">
                  <el-col :span="24">
                    <DeliveryLimitType v-model="singleForm.deliveryLimitType" />
                  </el-col>
                </el-row>
              </div>
              <div class="title line-1 mb-20 ml-5 fs-16 fw-bold pl-15">其他信息</div>
              <div class="">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <JobCooperate
                      v-if="showJobCooperate"
                      :companyId="singleForm.companyId"
                      v-model="synergyData"
                      identifyShow
                      :email="singleForm.applyAddress"
                      :hasAccount="hasAccount"
                    />
                  </el-col>
                </el-row>
              </div>
            </block>
          </block>
          <div class="batch" v-else>
            <div class="title line-1 mb-20 ml-5 fs-16 fw-bold pl-15">批量发布职位</div>

            <div class="pl-20">
              <el-form-item class="mb-0" :inline-message="true" label="选择文件" prop="filePath">
                <div class="flex">
                  <el-upload
                    v-if="showUpload"
                    class="upload-demo"
                    ref="upload"
                    action="/announcement/upload-excel"
                    :limit="1"
                    :on-success="handleSuccess"
                    :on-remove="handleRemove"
                  >
                    <el-button type="primary">选择文件</el-button>
                  </el-upload>
                  <p class="upload" @click="downloadFile">下载职位上传模板</p>
                </div>
              </el-form-item>
              <el-form-item>
                <span class="color-danger fs-12">
                  *职位详情请勿输入单位邮箱，移动电话，性别歧视字眼及其他外链。
                </span>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <template #footer>
        <div class="flex jc-center">
          <el-button class="btn" @click="closeJobAdd">取消</el-button>
          <el-button class="btn" type="primary" @click="submit">确定</el-button>
        </div>
      </template>
    </el-drawer>
    <DialogWelfare
      ref="dialogWelfare"
      title="职位福利"
      @confirm="handleWelfare"
      :memberId="singleForm.companyId"
    ></DialogWelfare>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import DialogWelfare from '/@/components/base/welfare.vue'
import JobCategory from '/@/components/base/select/jobCategory.vue'
import Education from '/@/components/base/select/education.vue'
import MajorDialog from './majorDialog.vue'
import WorkNature from '/@/components/base/select/workNature.vue'
import Salary from '/@/components/business/salary.vue'
import Experience from '/@/components/base/select/experience.vue'
import Age from '/@/components/base/select/age.vue'
import LevelTitle from '/@/components/base/select/levelTitle.vue'
import Political from '/@/components/base/select/political.vue'
import AbroadExperience from '/@/components/base/select/abroadExperience.vue'
import Region from '/@/components/base/select/region.vue'
import { getJobTemplateDetails } from '/@/api/job'
import {
  getSearchJobList,
  getTemporaryJobEidt,
  jobBatchAdd,
  temporaryJobEdit,
  temporaryJobSave
} from '/@/api/announcement'
import { aiRecognition } from '/@/api/major'
import DeliveryLimitType from '../../job/components/deliveryLimitType.vue'
import ApplyMethods from '/@/components/business/applyMethods.vue'
import AnnouncementCheckBox from '/@/components/base/announcementCheckBox.vue'
import { getJobEstablishmentList } from '/@/api/cmsJob'
import JobCooperate from '/@/components/job/jobCooperate.vue'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  deliveryType: { type: Number, default: null },
  isCooperation: { type: Boolean, default: false },
  jobList: { type: Array, default: () => [] },
  subAccountConfig: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['update:modelValue', 'jobTempData'])
const route = useRoute()

const form = ref()
const dialogWelfare = ref()
const regionValue = ref<any[]>([])
const isEdit = ref(false)
const showJobAdd = ref(false)
const showJobCooperate = ref(false)
const addType = ref(1)
const showUpload = ref(true)
const establishmentTypeList = ref<any[]>([])
const singleForm = reactive({
  announcementId: '',
  jobTemplateId: '',
  establishmentType: '',
  name: '',
  code: '',
  jobCategoryId: '',
  educationType: '',
  majorId: [],
  majorTitle: '',
  natureType: '',
  wageType: '',
  isNegotiable: '2',
  wageId: '',
  minWage: '',
  maxWage: '',
  address: '',
  periodDate: '',
  experienceType: '',
  ageType: '',
  titleType: '',
  politicalType: '',
  abroadType: '',
  welfareTag: '',
  isTemp: 1,
  jobId: '',
  jobTempId: '',
  department: '',
  provinceId: '',
  cityId: '',
  deliveryLimitType: '',
  companyId: '',
  applyType: '',
  applyAddress: '',
  deliveryWay: [],
  extraNotifyAddress: '',
  jobContactId: '',
  jobContactSynergyIds: [],
  filePath: '',
  amount: '',
  duty: '',
  requirement: '',
  remark: ''
})
const applyMethodsData = computed({
  get() {
    const { applyType, applyAddress, extraNotifyAddress, deliveryWay } = singleForm
    return { applyType, applyAddress, extraNotifyAddress, deliveryWay }
  },
  set(val: any) {
    Object.keys(val).forEach((key) => {
      singleForm[key] = val[key]
    })
  }
})
const synergyData = computed({
  get() {
    return {
      jobContactId: singleForm.jobContactId || '',
      jobContactSynergyIds: singleForm.jobContactSynergyIds || []
    }
  },
  set(val: any) {
    if (val) {
      singleForm.jobContactId = val.jobContactId || ''
      singleForm.jobContactSynergyIds = val.jobContactSynergyIds || []
    }
  }
})
const hasAccount = computed(() => props.subAccountConfig?.total !== '0')
const rules = reactive({
  name: [{ required: true, message: '请填写职位名称', trigger: 'blur' }],
  jobCategoryId: [{ required: true, message: '请选择职位类型', trigger: 'change' }],
  educationType: [{ required: true, message: '请选择学历', trigger: 'change' }],
  amount: [
    { required: true, message: '请填写输入招聘人数', trigger: 'blur' },
    { pattern: /^([1-9]\d{0,3}|\u82e5\u5e72)$/, message: '请输入数字或者"若干"', trigger: 'blur' }
  ],
  provinceId: [{ required: true, message: '请选择工作地点', trigger: 'change' }],
  periodDate: [{ required: false, message: '请选择职位有效期', trigger: 'change' }],
  duty: [{ required: true, message: '请填写岗位职责', trigger: 'blur' }],
  requirement: [{ required: true, message: '请填写任职要求', trigger: 'blur' }],
  filePath: [{ required: true, message: '请上传文件', trigger: ['change', 'blur'] }]
})
const welfareArray = ref<any[]>([])
const welfareText = ref('')
const templateList = ref<any[]>([])
const title = ref('')
const index = ref<any>(null)
const date = ref('')
const majorText = ref('')

const getJobEstablishment = () => {
  getJobEstablishmentList().then((resp: any) => {
    establishmentTypeList.value = resp
  })
}
getJobEstablishment()

const getData = async (data: any) => {
  templateList.value = await getSearchJobList({
    name: data,
    companyId: singleForm.companyId
  })
}

const handleWelfare = (welfare: any) => {
  welfareArray.value = welfare
  welfareText.value = welfare.map((item: any) => item.v).join('，')
  singleForm.welfareTag = welfare.map((item: any) => item.k).join(',')
}

const open = async (
  dateParam: any,
  announceCompanyId: any,
  id: any,
  isTemp: number,
  announceId: any,
  indexParam: any = null,
  jobId: any = null
) => {
  date.value = dateParam
  singleForm.companyId = announceCompanyId
  templateList.value = await getSearchJobList({ name: '', companyId: announceCompanyId })
  if (typeof indexParam === 'number') {
    index.value = indexParam
  } else {
    index.value = null
  }
  if (id || id === 0) {
    title.value = '编辑职位'
    isEdit.value = true
    addType.value = 1
    const res = await getTemporaryJobEidt({ id, isTemp, jobId })
    Object.assign(singleForm, res)
    // Object.keys(singleForm).forEach((key) => {
    //   singleForm[key] = res[key]
    // })
    singleForm.majorId = res.majorId.split(',')
    singleForm.deliveryWay = res.deliveryWay ? res.deliveryWay?.split(',') : []
    regionValue.value = [res.provinceId, res.cityId]
    handleWelfare(res.welfareTage)
  } else {
    title.value = '添加职位'
    isEdit.value = false
    showUpload.value = true
    singleForm.announcementId = route.params.id as string
  }
  showJobAdd.value = true
  showJobCooperate.value = true
}

const openDialogWelfare = () => {
  dialogWelfare.value.openDialog(welfareArray.value)
}

const colse = () => {
  Object.keys(singleForm).forEach((key) => {
    singleForm[key] = Array.isArray(singleForm[key]) ? [] : ''
  })
  welfareText.value = ''
  showUpload.value = false
  welfareArray.value = []
  showJobAdd.value = false
  showJobCooperate.value = false
  singleForm.isNegotiable = '2'
  addType.value = 2
  applyMethodsData.value = {
    applyType: '',
    applyAddress: '',
    extraNotifyAddress: '',
    deliveryWay: []
  }
  synergyData.value = {
    jobContactId: '',
    jobContactSynergyIds: []
  }
  majorText.value = ''
  addType.value = 1
}

const closeJobAdd = () => {
  ElMessageBox.confirm('确定要关闭本页面吗？关闭后本页面内容将不做保存！', '提示')
    .then(() => {
      if (addType.value === 1) {
        form.value.resetFields()
        setTimeout(() => {
          form.value?.clearValidate()
        }, 100)
      }
      colse()
    })
    .catch(() => {})
}
const submit = async () => {
  if (addType.value === 1) {
    if (Number(singleForm.minWage) > Number(singleForm.maxWage)) {
      ElMessage.error('最低薪资大于最高薪资')
      return
    }
    form.value.validate(async (valide: boolean) => {
      const { provinceId, cityId } = singleForm
      if (valide) {
        const majorId = singleForm.majorId ? singleForm.majorId.join() : ''
        const postData = {
          ...singleForm,
          provinceId,
          cityId,
          majorId,
          deliveryWay: singleForm.deliveryWay?.join()
        }
        const jobTempData =
          typeof index.value === 'number'
            ? await temporaryJobEdit(postData)
            : await temporaryJobSave(postData)
        emit('jobTempData', { ...jobTempData, index: index.value })
        ElMessage.success('操作成功')
        form.value?.resetFields()
        colse()
        setTimeout(() => {
          form.value?.clearValidate()
        }, 100)
      } else {
        return false
      }
      return valide
    })
  } else {
    form.value.validate(async (valide: boolean) => {
      if (valide) {
        try {
          const res = await jobBatchAdd({
            filePath: singleForm.filePath,
            announcementId: singleForm.announcementId,
            companyId: singleForm.companyId
          })
          emit('jobTempData', { res, batch: true })
          colse()
        } catch (msg: any) {
          ElMessage.closeAll()
          ElMessage({
            message: msg,
            type: 'error',
            duration: 10000,
            showClose: true
          })
        }
      }
    })
  }
}

const filterJobTemplate = (query: any) => {
  getData(query)
}

const changeTemplate = async (val: any) => {
  showJobCooperate.value = false
  form.value?.clearValidate()
  const res = await getJobTemplateDetails({ id: val })
  Object.assign(singleForm, res)
  if (!props.isCooperation) {
    if ('deliveryWay' in singleForm) delete (singleForm as any).deliveryWay
  }
  singleForm.majorId = res.majorId.split(',')
  singleForm.deliveryWay = res.deliveryWay ? res.deliveryWay?.split(',') : []
  singleForm.announcementId = route.params.id as string
  singleForm.isTemp = 1
  showJobCooperate.value = true
  regionValue.value = [res.provinceId, res.cityId]
  handleWelfare(res.welfareTage)
}

const downloadFile = () => {
  window.location.href = '/static/template/job_template.xlsx'
}

const handleSuccess = (res: any) => {
  singleForm.filePath = res.data.url
}

const handleRemove = () => {
  singleForm.filePath = ''
}

const handleDisabledDate = (time: any) => {
  const current = new Date()
  const curYear = current.getFullYear()
  const curMonth = current.getMonth() + 1
  const curDate = current.getDate()
  const todayTimestamp = new Date(`${curYear}/${curMonth}/${curDate} 00:00:00`).getTime()
  const nextYearTimestamp = todayTimestamp + 365 * 24 * 60 * 60 * 1000
  const limitDateTimestamp = new Date(`${date.value?.replace('-', '/')} 00:00:00`).getTime()
  return (
    time.getTime() < todayTimestamp ||
    time.getTime() > nextYearTimestamp ||
    time.getTime() > limitDateTimestamp
  )
}

const changeMajor = (val: any) => {
  const job = props.jobList.find((item: any) => item.id === val) as any
  ElMessageBox.confirm(`确定复制(${job?.majorTitle})到该职位?`, '提示')
    .then(() => {
      if (!job) return
      const { majorId } = job
      singleForm.majorId = majorId.split(',')
      singleForm.majorTitle = job.majorTitle
    })
    .catch(() => {})
}

const salarChage = (data: any) => {
  Object.assign(singleForm, data)
}

const recognition = () => {
  aiRecognition(majorText.value).then((r) => {
    singleForm.majorId = r.majorIds
    singleForm.majorTitle = r.majorNames.join(',')
  })
}

watch(
  () => addType.value,
  (val) => {
    if (val === 1) {
      handleRemove()
    }
  }
)

defineExpose({ open })
</script>

<style lang="scss" scoped>
.title {
  margin-top: 15px;
  border-left: 2px solid var(--color-primary);
  overflow: hidden;
}
.upload {
  cursor: pointer;
  color: var(--color-primary);
  margin-left: 15px;
}
:deep(.el-form-item__content) {
  align-items: flex-start;
}
:deep(.el-drawer__header) {
  margin-bottom: 0;
}
.single {
  height: calc(100% - 100px);
  // overflow: auto;
}
:deep(.el-form-item__content) {
  display: block;
}
</style>
