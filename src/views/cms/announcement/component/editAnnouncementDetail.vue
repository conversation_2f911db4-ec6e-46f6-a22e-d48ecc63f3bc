<template>
  <div class="pb-10 border-bottom">
    <div class="fw-bold mb-10 color-dark">
      修改类型：{{ baseInfo.editorTypeTxt }}
      <router-link
        class="ml-10 bg-primary td-none"
        :to="`/cms/announcementDetail/${baseInfo.id}/${baseInfo.status}`"
        >预览原公告</router-link
      >
    </div>

    <div class="fw-bold mb-10 color-dark">修改前：</div>
    <div class="flex mb-10">
      <div class="detail-main" v-html="baseInfo.announcementHandleBefore"></div>
    </div>
    <div class="flex mb-10" v-if="baseInfo.fileHandleBefore?.length">
      <JobAttachment :fileList="baseInfo.fileHandleBefore" />
    </div>
    <div class="fw-bold mb-10 color-dark">修改后：</div>
    <div class="flex mb-10">
      <div class="detail-main" v-html="baseInfo.announcementHandleAfter"></div>
    </div>
    <div class="flex mb-10" v-if="baseInfo.fileHandleAfter?.length">
      <JobAttachment :fileList="baseInfo.fileHandleAfter" />
    </div>
  </div>
</template>

<script setup lang="ts">
import JobAttachment from './jobAttachment.vue'

defineOptions({ name: 'editAnnouncementDetail' })

defineProps({
  baseInfo: {
    type: Object,
    default: () => ({})
  },
  editorType: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
.detail-main {
  :deep() {
    p {
      color: rgba(51, 51, 51, 0.8);
      font-size: 14px;
      line-height: 2;
    }

    img {
      display: block;
      margin: 20px auto;
      max-width: 100%;
    }

    table {
      margin: 20px auto;
      width: 100%;
      border-collapse: collapse;

      th {
        background-color: #fafafc;
      }

      th,
      td {
        padding: 10px 0;
        text-align: center;
        border: 1px solid #ccc;
      }
    }
  }
}
</style>
