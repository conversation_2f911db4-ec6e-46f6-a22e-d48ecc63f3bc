<template>
  <div>
    <el-dialog v-model="batchAuditVisible" title="公告审核" width="500px">
      <el-form :model="form" :rules="rules">
        <el-form-item label="审核结果" prop="status">
          <el-radio v-model="form.status" label="1">通过</el-radio>
          <el-radio v-model="form.status" label="-1">拒绝</el-radio>
        </el-form-item>
        <el-form-item label="原因说明">
          <el-input v-model="form.opinion" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submit">确定</el-button>
          <el-button @click="batchAuditVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref } from 'vue'
import { announcementBatchAudit } from '/@/api/announcement'

defineOptions({ name: 'announcementAudit' })

const batchAuditVisible = ref(false)
const form = ref({
  ids: '',
  status: '',
  opinion: ''
})
const rules = {
  status: [{ required: true, message: '必填选项', trigger: 'change' }]
}

// eslint-disable-next-line no-unused-vars
const open = (id: any) => {
  form.value.ids = id
  batchAuditVisible.value = true
}

const submit = async () => {
  if (form.value.status === '-1' && form.value.opinion === '') {
    ElMessage.error('审核拒绝，原因不能为空')
    return
  }
  await announcementBatchAudit(form.value)
  batchAuditVisible.value = false
}
</script>

<style lang="scss" scoped></style>
