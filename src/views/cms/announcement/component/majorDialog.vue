<template>
  <div>
    <!-- <p>{{ checked }}</p> -->
    <el-input
      class="w100"
      @click="dialogVisible = true"
      readonly
      v-model="majorTitle"
      placeholder="请选择需求专业"
    ></el-input>
    <el-dialog v-model="dialogVisible" title="需求专业" fullscreen>
      <majorLevel2 v-model="checked" v-model:select="select"></majorLevel2>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue'
import majorLevel2 from '/@select/majorLevel2.vue'

export default defineComponent({
  components: { majorLevel2 },
  props: {
    // isLimit: {
    //   type: <PERSON>olean,
    //   default: () => false
    // },
    modelValue: {
      type: Array,
      default: () => []
    },
    title: {
      type: [String, Boolean],
      default: () => ''
    }
  },
  setup(props: any, { emit }) {
    const state = reactive({
      dialogVisible: false,
      majorTitle: '',
      select: [],
      checked: []
    })

    watch(
      () => props.modelValue,
      (value) => {
        state.checked = value
      },
      { deep: true, immediate: true }
    )
    watch(
      () => props.title,
      (value) => {
        state.majorTitle = value || '不限'
      },
      {
        immediate: true
      }
    )

    const handleConfirm = () => {
      const selectTitle = state.select?.map((item: any) => item.name).join()
      state.majorTitle = selectTitle || '不限'
      state.dialogVisible = false
      emit('update:modelValue', state.checked)
      emit('update:title', state.majorTitle)
    }
    return {
      handleConfirm,
      ...toRefs(state)
    }
  }
})
</script>
<style scoped lang="scss">
.left {
  text-align: right;
  border: 1px solid #ccc;
}
.checkbox {
  min-width: 200px;
}
:deep(.el-dialog.is-fullscreen) {
  display: flex;
  flex-direction: column;
  .el-dialog__body {
    flex: 1;
    overflow-y: auto;
  }
}
</style>
