<template>
  <div class="qiniu-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="图片管理" name="manage">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>七牛云图片管理</span>
            </div>
          </template>

          <!-- 输入框部分 -->
          <el-form :model="formData" class="input-section">
            <el-form-item>
              <div class="input-area">
                <el-input
                  v-model="formData.imageUrl"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入图片链接，多个链接请用换行或英文逗号分隔"
                  class="input-with-button"
                />
                <div class="button-group">
                  <el-button type="primary" @click="addImage">批量添加</el-button>
                  <el-button @click="clearInput">清空输入</el-button>
                </div>
              </div>
            </el-form-item>
          </el-form>

          <!-- 图片展示区域 -->
          <div class="image-list" v-if="imageList.length">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(item, index) in imageList" :key="index">
                <el-card class="image-card" shadow="hover">
                  <template #header>
                    <div class="card-header">
                      <el-button
                        class="remove-button"
                        type="danger"
                        :icon="Close"
                        circle
                        plain
                        size="small"
                        @click="removeFromList(index)"
                      />
                    </div>
                  </template>
                  <div class="image-wrapper">
                    <el-image
                      :src="item.url"
                      class="preview-image"
                      :preview-src-list="[item.url]"
                      :initial-index="0"
                      fit="contain"
                      loading="lazy"
                      @load="(e) => onImageLoad(e, index)"
                      preview-teleported
                    >
                      <template #placeholder>
                        <div class="image-placeholder">
                          <el-icon><Picture /></el-icon>
                          <span>加载中...</span>
                        </div>
                      </template>
                      <template #error>
                        <div class="image-placeholder">
                          <el-icon><PictureRounded /></el-icon>
                          <span>加载失败</span>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="image-info">
                    <div class="info-item">
                      <span class="label">尺寸：</span>
                      <span class="value">{{ item.dimensions || '加载中...' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">大小：</span>
                      <span class="value">{{
                        item.size === undefined ? '加载中...' : formatSize(item.size)
                      }}</span>
                    </div>
                    <div class="info-item url-item">
                      <span class="label">链接：</span>
                      <el-tooltip :content="item.url" placement="top" :show-after="500">
                        <span class="value url" @click="copyUrl(item.url)">
                          {{ truncateUrl(item.url) }}
                        </span>
                      </el-tooltip>
                      <el-button type="primary" link @click="copyUrl(item.url)"> 复制 </el-button>
                    </div>
                  </div>
                  <div class="image-actions">
                    <el-button type="danger" size="small" @click="deleteImage(index)"
                      >删除</el-button
                    >
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <el-empty v-else description="暂无图片" />
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="图片压缩上传" name="compress">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>图片压缩上传</span>
            </div>
          </template>
          <div class="compress-upload-section">
            <el-form :model="compressForm" class="input-section">
              <el-form-item>
                <div class="input-area">
                  <el-input
                    v-model="compressForm.imageUrl"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入图片链接，多个链接请用换行或英文逗号分隔"
                    class="input-with-button"
                  />
                  <div class="button-group">
                    <el-button type="primary" @click="fetchImagesFromUrls">批量读取</el-button>
                    <el-button @click="clearCompressInput">清空输入</el-button>
                  </div>
                </div>
              </el-form-item>
            </el-form>
            <div v-if="originalImages.length" class="preview-list">
              <h4>原图预览</h4>
              <el-row :gutter="20">
                <el-col :span="8" v-for="(img, idx) in originalImages" :key="idx">
                  <el-card>
                    <img :src="img.url" class="preview-image" />
                    <div class="image-info">
                      <div>链接：{{ img.originUrl }}</div>
                      <div>大小：{{ formatSize(img.size) }}</div>
                      <div>分辨率：{{ img.width }} × {{ img.height }}</div>
                    </div>
                    <div style="text-align: right; margin-top: 10px">
                      <el-button size="small" @click="compressAndPreview(img)">压缩</el-button>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
            <div v-if="compressedImages.length" class="preview-list">
              <h4>压缩后预览</h4>
              <el-row :gutter="20">
                <el-col :span="8" v-for="(img, idx) in compressedImages" :key="idx">
                  <el-card>
                    <img :src="img.url" class="preview-image" />
                    <div class="image-info">
                      <div>原链接：{{ img.originUrl }}</div>
                      <div>大小：{{ formatSize(img.size) }}</div>
                      <div>分辨率：{{ img.width }} × {{ img.height }}</div>
                    </div>
                    <div style="text-align: right; margin-top: 10px">
                      <el-button size="small" @click="openPreviewDialog(img)">预览</el-button>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>
          <el-dialog
            v-model="previewDialog.visible"
            title="压缩图片预览"
            width="500px"
            :close-on-click-modal="false"
          >
            <div v-if="previewDialog.img">
              <img
                :src="previewDialog.img.url"
                style="width: 100%; max-height: 300px; object-fit: contain"
              />
              <div class="image-info" style="margin-top: 15px">
                <div>原链接：{{ previewDialog.img.originUrl }}</div>
                <div>大小：{{ formatSize(previewDialog.img.size) }}</div>
                <div>分辨率：{{ previewDialog.img.width }} × {{ previewDialog.img.height }}</div>
              </div>
            </div>
            <template #footer>
              <el-button @click="previewDialog.visible = false">关闭</el-button>
              <el-button type="primary" @click="uploadSingleCompressedImage">上传</el-button>
            </template>
          </el-dialog>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
// 定义名字
import { onMounted, ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture, PictureRounded, Close } from '@element-plus/icons-vue'
import { deleteQiniuImage, reUploadQiniuImage } from '/@/api/system'

defineOptions({
  name: 'systemQiniu',
  inheritAttrs: false
})
// 表单数据
const formData = reactive({
  imageUrl: ''
})

// 获取图片文件大小（单位：字节）
const fetchImageSize = async (url: string): Promise<number | null> => {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    const size = response.headers.get('Content-Length')
    if (size) {
      return parseInt(size, 10)
    }
    return null
  } catch (e) {
    return null
  }
}

// 格式化文件大小
const formatSize = (size: number | null | undefined): string => {
  if (typeof size !== 'number' || isNaN(size)) return '未知'
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`
  return `${(size / 1024 / 1024).toFixed(2)} MB`
}

// 图片列表类型定义
interface ImageItem {
  url: string
  dimensions?: string
  size?: number | null
}

// 修改图片列表的类型
const imageList = ref<ImageItem[]>([])

// 检查图片是否可访问
const checkImageExists = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => resolve(true)
    img.onerror = () => resolve(false)
    img.src = url
  })
}

// 添加图片
const addImage = async () => {
  if (!formData.imageUrl.trim()) {
    ElMessage.warning('请输入图片链接')
    return
  }

  // 分割输入的链接（支持换行和逗号分隔）
  const urls = formData.imageUrl
    .split(/[\n,]/)
    .map((url) => url.trim())
    .filter((url) => url !== '')

  // 验证并添加每个URL
  let successCount = 0
  let failCount = 0
  let notFoundCount = 0

  // 使用 Promise.all 并行检查所有图片
  const checkResults = await Promise.all(
    urls.map(async (url) => {
      try {
        new URL(url) // 验证URL格式
        const exists = await checkImageExists(url)
        return { url, isValid: true, exists }
      } catch (e) {
        return { url, isValid: false, exists: false }
      }
    })
  )

  // 处理检查结果
  checkResults.forEach(({ url, isValid, exists }) => {
    if (!isValid) {
      failCount++
      console.error(`无效的URL格式: ${url}`)
    } else if (!exists) {
      notFoundCount++
      console.error(`图片不存在(404): ${url}`)
    } else {
      imageList.value.push({ url })
      successCount++
    }
  })

  // 显示添加结果
  if (successCount > 0) {
    ElMessage.success(`成功添加 ${successCount} 张图片`)
  }
  if (failCount > 0) {
    ElMessage.warning(`${failCount} 个链接格式无效`)
  }
  if (notFoundCount > 0) {
    ElMessage.warning(`${notFoundCount} 张图片无法访问(404)`)
  }

  // 清空输入框
  formData.imageUrl = ''
}

// 清空输入框
const clearInput = () => {
  formData.imageUrl = ''
}

// 删除图片
const deleteImage = (index: number) => {
  const imageUrl = imageList.value[index].url

  ElMessageBox.confirm(
    `<div class="delete-confirm-content">
      <p class="warning-text">警告：删除后图片将无法恢复！</p>
      <div class="preview-container">
        <img src="${imageUrl}" class="preview-image" />
      </div>
      <p class="image-url">图片地址：${imageUrl}</p>
    </div>`,
    '确认删除',
    {
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
      customClass: 'delete-confirm-dialog'
    }
  )
    .then(() => {
      deleteQiniuImage({
        url: imageUrl
      }).then(() => {
        imageList.value.splice(index, 1)
      })
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

// 处理图片加载完成事件
const onImageLoad = async (e: Event, index: number) => {
  const img = e.target as HTMLImageElement
  imageList.value[index].dimensions = `${img.naturalWidth} × ${img.naturalHeight}`
  // 异步获取图片大小
  imageList.value[index].size = undefined // 标记为加载中
  const size = await fetchImageSize(img.src)
  imageList.value[index].size = size
}

// 截断URL显示
const truncateUrl = (url: string) => {
  if (url.length > 50) {
    return `${url.substring(0, 47)}...`
  }
  return url
}

// 复制URL到剪贴板
const copyUrl = (url: string) => {
  navigator.clipboard
    .writeText(url)
    .then(() => {
      ElMessage.success('链接已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}

// 从列表中移除图片（不删除七牛云上的图片）
const removeFromList = (index: number) => {
  imageList.value.splice(index, 1)
  ElMessage.success('已从列表中移除')
}

// 初始化加载
onMounted(() => {
  // 这里可以添加初始化逻辑，比如从本地存储加载已保存的图片列表
})

// 图片压缩上传Tab相关
const compressForm = reactive({
  imageUrl: ''
})

interface RemoteImage {
  url: string // DataURL
  originUrl: string // 原始链接
  size: number
  width: number
  height: number
}
const originalImages = ref<RemoteImage[]>([])
const compressedImages = ref<RemoteImage[]>([])

const clearCompressInput = () => {
  compressForm.imageUrl = ''
}

const fetchImagesFromUrls = async () => {
  if (!compressForm.imageUrl.trim()) {
    ElMessage.warning('请输入图片链接')
    return
  }
  const urls = compressForm.imageUrl
    .split(/[\n,]/)
    .map((url) => url.trim())
    .filter((url) => url !== '')
  let successCount = 0
  let failCount = 0
  for (const url of urls) {
    let tryUrl = url
    let triedHttps = false
    if (url.startsWith('http://')) {
      tryUrl = url.replace(/^http:\/\//, 'https://')
      triedHttps = true
    }
    try {
      // 优先尝试https
      const res = await fetch(tryUrl)
      if (!res.ok) throw new Error('图片无法访问')
      const blob = await res.blob()
      const dataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => resolve(reader.result as string)
        reader.onerror = reject
        reader.readAsDataURL(blob)
      })
      const img = new window.Image()
      await new Promise<void>((resolve, reject) => {
        img.onload = () => resolve()
        img.onerror = reject
        img.src = dataUrl
      })
      originalImages.value.push({
        url: dataUrl,
        originUrl: url,
        size: blob.size,
        width: img.width,
        height: img.height
      })
      successCount++
    } catch (e) {
      // 如果尝试了https失败，且原始url是http，提示用户
      if (triedHttps) {
        failCount++
        ElMessage.warning(`图片 ${url} 不支持HTTPS，无法在当前页面加载`)
      } else {
        failCount++
        console.error(`图片读取失败: ${url}`)
      }
    }
  }
  if (successCount > 0) {
    ElMessage.success(`成功读取 ${successCount} 张图片`)
  }
  if (failCount > 0) {
    ElMessage.warning(`${failCount} 张图片读取失败`)
  }
  compressForm.imageUrl = ''
}

// 压缩图片函数（canvas，默认jpeg，质量0.7）
const compressImage = (img: RemoteImage, quality = 0.7): Promise<RemoteImage> => {
  return new Promise((resolve, reject) => {
    const image = new window.Image()
    image.onload = () => {
      const canvas = document.createElement('canvas')
      canvas.width = image.width
      canvas.height = image.height
      const ctx = canvas.getContext('2d')
      if (!ctx) return reject('canvas context error')
      ctx.drawImage(image, 0, 0, image.width, image.height)
      canvas.toBlob(
        (blob) => {
          if (!blob) return reject('compress error')
          const reader = new FileReader()
          reader.onload = () => {
            resolve({
              url: reader.result as string,
              originUrl: img.originUrl,
              size: blob.size,
              width: image.width,
              height: image.height
            })
          }
          reader.onerror = reject
          reader.readAsDataURL(blob)
        },
        'image/jpeg',
        quality
      )
    }
    image.onerror = reject
    image.src = img.url
  })
}

// 单张压缩并预览
const compressAndPreview = async (img: RemoteImage) => {
  try {
    const compressed = await compressImage(img)
    previewDialog.img = compressed
    previewDialog.visible = true
  } catch (e) {
    ElMessage.error('图片压缩失败')
  }
}

const previewDialog = reactive({
  visible: false,
  img: null as null | RemoteImage
})

const openPreviewDialog = (img: RemoteImage) => {
  previewDialog.img = img
  previewDialog.visible = true
}

const uploadSingleCompressedImage = async () => {
  if (!previewDialog.img) return
  reUploadQiniuImage({
    url: previewDialog.img.originUrl,
    base64: previewDialog.img.url
  }).then(() => {
    // 从列表中找到对应的图片并删除
    const idx = originalImages.value.findIndex((i) => i.originUrl === previewDialog.img?.originUrl)
    if (idx !== -1) {
      originalImages.value.splice(idx, 1)
    }
    previewDialog.visible = false
  })
}

const activeTab = ref('manage')

defineExpose({
  previewDialog,
  openPreviewDialog,
  uploadSingleCompressedImage
})
</script>

<style lang="scss" scoped>
.qiniu-container {
  padding: 20px;

  .box-card {
    width: 100%;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .input-section {
    margin-bottom: 20px;

    .input-area {
      .input-with-button {
        width: 100%;
        margin-bottom: 10px;
      }

      .button-group {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
      }
    }
  }

  .image-list {
    .image-card {
      margin-bottom: 20px;
      position: relative;

      .card-header {
        padding: 0;
        margin: 0;
        height: 0;
        border: none;

        .remove-button {
          position: absolute;
          right: 10px;
          top: 10px;
          z-index: 1;
          opacity: 0;
          transition: opacity 0.3s;
        }
      }

      &:hover {
        .remove-button {
          opacity: 1;
        }
      }

      .image-wrapper {
        width: 100%;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
        border-radius: 4px;
        overflow: hidden;
        cursor: zoom-in;

        .preview-image {
          width: 100%;
          height: 100%;
        }

        .image-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #909399;
          font-size: 14px;

          .el-icon {
            font-size: 30px;
            margin-bottom: 8px;
          }
        }
      }

      .image-info {
        margin: 15px 0;
        font-size: 14px;

        .info-item {
          margin-bottom: 8px;
          display: flex;
          align-items: flex-start;

          &.url-item {
            align-items: center;
          }

          .label {
            color: #606266;
            margin-right: 8px;
            flex-shrink: 0;
          }

          .value {
            color: #333;
            word-break: break-all;

            &.url {
              color: #409eff;
              cursor: pointer;
              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }

      .image-actions {
        display: flex;
        justify-content: center;
        margin-top: 10px;
      }
    }
  }

  .compress-upload-section {
    margin-top: 20px;
    .preview-list {
      margin-top: 20px;
      .preview-image {
        width: 100%;
        height: 180px;
        object-fit: contain;
        background: #f5f7fa;
        border-radius: 4px;
        margin-bottom: 10px;
      }
      .image-info {
        font-size: 13px;
        color: #606266;
        margin-bottom: 5px;
      }
    }
  }
}

// 自定义预览样式
:deep(.el-image-viewer__wrapper) {
  .el-image-viewer__btn {
    opacity: 0.7;
    &:hover {
      opacity: 1;
    }
  }

  .el-image-viewer__actions {
    opacity: 0.7;
    &:hover {
      opacity: 1;
    }
  }
}
</style>

<style lang="scss">
// 全局样式，用于自定义确认框样式
.delete-confirm-dialog {
  .el-message-box__content {
    padding: 20px;
  }

  .delete-confirm-content {
    text-align: center;

    .warning-text {
      color: #f56c6c;
      font-weight: bold;
      margin-bottom: 15px;
      font-size: 16px;
    }

    .preview-container {
      margin: 15px 0;
      border: 1px solid #dcdfe6;
      padding: 10px;
      border-radius: 4px;

      .preview-image {
        max-width: 100%;
        max-height: 200px;
        object-fit: contain;
      }
    }

    .image-url {
      color: #606266;
      font-size: 12px;
      word-break: break-all;
      margin-top: 10px;
    }
  }
}
</style>
