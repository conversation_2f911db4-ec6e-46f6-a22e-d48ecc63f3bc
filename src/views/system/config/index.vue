<template>
  <div class="main">
    <div class="box">
      <el-form ref="form2" label-width="100px" :model="formData" @submit.native.prevent>
        <div class="flex">
          <el-form-item class="span-4" label="关键字" prop="name">
            <el-input
              clearable
              @keyup.native.enter="handleSearch"
              v-model="formData.name"
              placeholder="输入关键字"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
      <el-row style="margin-bottom: 20px">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button type="default" @click="resetForm">重置</el-button>
      </el-row>
      <el-row style="margin-bottom: 20px">
        <el-button type="primary" @click="handleAdd">+新增配置</el-button>
      </el-row>
      <el-table
        :data="list"
        border
        size="small"
        align="center"
        v-loading="tableLoading"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="name" label="name" />
        <el-table-column prop="value" label="value">
          <template #default="{ row }">
            <el-tooltip
              :content="formatValue(row.value)"
              placement="top"
              :popper-class="isJSON(row.value) ? 'json-tooltip' : ''"
              :raw-content="true"
            >
              <div class="value-cell">{{ row.value }}</div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="remark" label="备注" />

        <el-table-column label="操作" fixed="right" width="240px">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              class="button-margin"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="paging">
        <Paging :total="pagination.total"></Paging>
      </div>
      <SalesmanChangeDialog @update="getTableList()" ref="salesmanChangeDialog" />
      <el-dialog
        :title="dialogType === 'add' ? '新增配置' : '编辑配置'"
        v-model="dialogVisible"
        width="500px"
        @close="resetDialog"
      >
        <el-form
          ref="dialogForm"
          :model="dialogData"
          :rules="dialogRules"
          label-width="80px"
          @submit.prevent
        >
          <el-form-item label="名称" prop="name" v-if="dialogType === 'add'">
            <el-input v-model="dialogData.name" placeholder="请输入配置名称"></el-input>
          </el-form-item>
          <el-form-item label="值" prop="value">
            <el-input
              v-model="dialogData.value"
              type="textarea"
              :rows="4"
              placeholder="请输入配置值"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="dialogData.remark"
              type="textarea"
              :rows="2"
              placeholder="请输入备注信息"
            ></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 定义名字
import { onMounted, ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { getConfigList, addConfig } from '/@/api/system'
import Paging from '/@/components/base/paging.vue'

defineOptions({
  name: 'systemConfig',
  inheritAttrs: false
})

// 表单数据
const formData = reactive({
  name: ''
})

// 表格数据
const list = ref([])
const tableLoading = ref(false)
const pagination = reactive({
  total: 0
})
// 弹窗数据
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const submitLoading = ref(false)
const dialogForm = ref()

const dialogData = reactive({
  id: '',
  name: '',
  value: '',
  remark: ''
})

const dialogRules = {
  name: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
  value: [{ required: true, message: '请输入配置值', trigger: 'blur' }]
}

// 方法定义
const handleSearch = () => {
  getTableList()
}

const resetForm = () => {
  Object.keys(formData).forEach((key) => {
    if (Array.isArray(formData[key])) {
      formData[key] = []
    } else {
      formData[key] = ''
    }
  })
}

const handleSortChange = (column: any) => {
  console.log('排序变化', column)
  getTableList()
}

const getTableList = () => {
  tableLoading.value = true
  getConfigList({
    ...formData
  })
    .then((res) => {
      list.value = res.list || []
      pagination.total = res.page?.count || 0
      tableLoading.value = false
    })
    .catch(() => {
      tableLoading.value = false
    })
}

// 判断字符串是否为JSON
const isJSON = (str: string) => {
  if (typeof str !== 'string') return false
  try {
    JSON.parse(str)
    return true
  } catch (e) {
    return false
  }
}

// 格式化value显示
const formatValue = (value: string) => {
  if (!value) return ''
  if (isJSON(value)) {
    try {
      const formatted = JSON.stringify(JSON.parse(value), null, 2)
      return `<pre style="margin: 0; white-space: pre-wrap;">${formatted}</pre>`
    } catch (e) {
      return value
    }
  }
  return value
}

// 打开新增弹窗
const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
}

// 打开编辑弹窗
const handleEdit = (row: any) => {
  dialogType.value = 'edit'
  dialogData.id = row.id
  dialogData.name = row.name
  dialogData.value = row.value
  dialogData.remark = row.remark
  dialogVisible.value = true
}

// 重置弹窗数据
const resetDialog = () => {
  dialogData.id = ''
  dialogData.name = ''
  dialogData.value = ''
  dialogData.remark = ''
  if (dialogForm.value) {
    dialogForm.value.resetFields()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!dialogForm.value) return

  await dialogForm.value.validate()

  try {
    submitLoading.value = true
    if (dialogType.value === 'add') {
      await addConfig({
        name: dialogData.name,
        value: dialogData.value,
        remark: dialogData.remark
      })
    } else {
      await addConfig({
        id: dialogData.id,
        value: dialogData.value,
        remark: dialogData.remark
      })
    }

    dialogVisible.value = false
    getTableList()
  } catch (error: any) {
  } finally {
    submitLoading.value = false
  }
}

// 初始化加载
onMounted(() => {
  getTableList()
})
</script>
<style lang="scss" scoped>
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}
.form-box el-form-item el-input {
  width: 230px;
}
.button-margin {
  margin: 5px; /* 下边距 5px */
}
.label-margin {
  margin: 5px;
}
.paging {
  margin-top: 30px;
}
.btns {
  .el-button + .el-button {
    margin-left: 0;
  }
}
.value-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

:deep(.json-tooltip) {
  max-width: 600px;

  .el-tooltip__content {
    text-align: left;
    font-family: monospace;
    background: #f8f9fa;
    color: #333;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
