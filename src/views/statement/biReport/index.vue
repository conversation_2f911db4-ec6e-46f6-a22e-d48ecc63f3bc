<template>
  <div class="main">
    <iframe
      title="高校人才网数据看板"
      width="100%"
      height="1060px"
      :src="url"
      frameborder="0"
      allowFullScreen="true"
    ></iframe>
  </div>
</template>

<script lang="ts" setup>
// 定义名字
import { onMounted, ref } from 'vue'
import { getBi } from '/@/api/system'

defineOptions({
  name: 'biReport',
  inheritAttrs: false
})

// url
const url = ref('')
// 初始化加载
onMounted(() => {
  getBi().then((res) => {
    url.value = res.url
  })
})
</script>
