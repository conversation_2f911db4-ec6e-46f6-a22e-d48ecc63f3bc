<template>
  <div class="box">
    <div id="top-container" class="pb-15">
      <!-- 搜索表单区域 -->
      <el-form ref="form2" label-width="100px" :model="searchForm">
        <div class="flex">
          <el-form-item class="span-4" label="角色名称" prop="name">
            <el-input
              clearable
              @keyup.enter="search"
              v-model="searchForm.name"
              placeholder="请输入角色名称"
            ></el-input>
          </el-form-item>
          <el-form-item class="span-4" label="角色状态" prop="status">
            <el-select v-model="searchForm.status" placeholder="请选择" filterable clearable>
              <el-option v-for="item in statusList" :key="item.k" :label="item.v" :value="item.k">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="span-4" label="部门" prop="departmentId">
            <el-select v-model="searchForm.departmentId" placeholder="请选择" filterable clearable>
              <el-option
                v-for="item in departmentList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form>

      <!-- 操作按钮区域 -->
      <el-row style="margin-bottom: 20px">
        <el-button type="primary" class="button-margin" @click="search">搜索</el-button>
        <el-button type="default" class="button-margin" @click="resetSearch">重置</el-button>
        <el-button type="primary" class="button-margin" @click="showAddForm">+ 添加角色</el-button>
      </el-row>
    </div>

    <!-- 表格区域 -->
    <el-table :data="list" border :max-height="maxTableHeight" v-loading="tableLoading">
      <el-table-column align="center" prop="pDepartmentName" label="部门" />
      <el-table-column align="center" prop="departmentName" label="下属部门" />
      <el-table-column align="center" prop="positionName" label="角色名称" />
      <el-table-column align="center" prop="addTime" label="创建时间" />
      <el-table-column label="操作" fixed="right" width="200">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            class="button-margin"
            @click="showAddForm(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="primary"
            size="small"
            class="button-margin"
            @click="showPermissions(scope.row)"
          >
            权限
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 弹窗内容 -->
    <el-dialog v-model="addFormVisible" title="添加/修改角色">
      <el-form :model="addForm">
        <el-form-item label="名称" :label-width="120">
          <el-input v-model="addForm.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="部门" :label-width="120">
          <el-select v-model="addForm.departmentId" placeholder="请选择" filterable clearable>
            <el-option v-for="item in departmentList" :label="item.v" :value="item.k"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="key" :label-width="120">
          <el-input v-model="addForm.key" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" @click="addFormVisible = false">取消</el-button>
          <el-button type="primary" size="small" @click="submit" :loading="submitLoading"
            >确认</el-button
          >
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="permissionsVisable"
      title="权限操作"
      width="85%"
      top="5vh"
      :close-on-click-modal="false"
      :destroy-on-close="false"
      class="permission-dialog"
    >
      <adminRouteSetting
        :tableData="tableData"
        :id="positionId"
        :checkBoxList="checkBoxList"
        @cancel="closePositions"
      ></adminRouteSetting>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { getCurrentInstance, onMounted, reactive, toRefs, ref, nextTick } from 'vue'
import {
  addPosition,
  getPositionList,
  getPositionListParams,
  getPositionMenuList,
  getRouteList
} from '../../api/permissions'
import adminRouteSetting from './component/adminRouteSetting.vue'

export default {
  name: 'positionList',
  components: {
    adminRouteSetting
  },
  setup() {
    const { proxy } = getCurrentInstance() as any
    const table = ref()
    const maxTableHeight = ref(450)

    const state = reactive({
      searchForm: {
        name: '',
        status: '',
        departmentId: ''
      },
      list: [],
      departmentList: [{ k: '', v: '' }],
      statusList: [{ k: '', v: '' }],
      tableLoading: false,
      addForm: {
        id: '',
        name: '',
        departmentId: '',
        key: ''
      },
      addFormVisible: false,
      submitLoading: false,
      permissionsVisable: false,
      tableData: [],
      checkBoxList: {
        menu: {},
        route: {},
        action: {}
      },
      positionId: ''
    })

    // 计算表格高度
    const getTableHeight = async () => {
      await nextTick()
      const topHeight = document.getElementById('top-container')?.clientHeight || 0
      const height = Number(
        document.getElementById('layout-main')?.getAttribute('data-view-height') || 0
      )
      // .box 内边距
      const padding = 40
      maxTableHeight.value = height - topHeight - padding
    }

    // 搜索
    const search = () => {
      state.tableLoading = true
      getPositionList(state.searchForm).then((res: any) => {
        state.list = res
        state.tableLoading = false
        getTableHeight()
      })
    }

    // 重置
    const resetSearch = () => {
      state.searchForm = {
        name: '',
        status: '',
        departmentId: ''
      }
      search()
    }

    const showAddForm = (row: any) => {
      if (row) {
        state.addForm.id = row.id
        state.addForm.name = row.positionName
        state.addForm.departmentId = row.departmentId
        state.addForm.key = row.key
      } else {
        state.addForm = {
          id: '',
          name: '',
          departmentId: '',
          key: ''
        }
      }
      state.addFormVisible = true
    }

    onMounted(() => {
      getPositionListParams().then((res: any) => {
        state.statusList = res.statusList
        state.departmentList = res.departmentList
      })

      search()
      getTableHeight()
    })

    const submit = () => {
      state.submitLoading = true
      addPosition(state.addForm)
        .then(() => {
          state.submitLoading = false
          resetSearch()
          state.addFormVisible = false
        })
        .catch((err) => {
          state.submitLoading = false
        })
    }

    const showPermissions = (row: any) => {
      getPositionMenuList(row.id).then((res: any) => {
        state.permissionsVisable = true
        state.tableData = res
        res.forEach((element: any) => {
          if (element.route.isSet == 1) {
            state.checkBoxList.route[`route_${element.route.id}`] = true
          } else {
            state.checkBoxList.route[`route_${element.route.id}`] = false
          }
          if (element.action) {
            element.action.forEach((action: any) => {
              if (action.isSet == 1) {
                state.checkBoxList.action[`action_${action.id}`] = true
              } else {
                state.checkBoxList.action[`action_${action.id}`] = false
              }
            })
          }
        })
        state.positionId = row.id
      })
    }

    const closePositions = () => {
      state.permissionsVisable = false
    }

    return {
      ...toRefs(state),
      table,
      maxTableHeight,
      search,
      resetSearch,
      showAddForm,
      showPermissions,
      submit,
      closePositions,
      getTableHeight
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
}

.button-margin {
  margin: 5px;
}

.flex {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .span-4 {
    flex: 0 0 calc(33.33% - 14px);
  }
}

:deep(.el-form-item__content) {
  width: 100%;
  .el-input,
  .el-select {
    width: 100%;
  }
}

// 权限弹窗样式
:deep(.permission-dialog) {
  .el-dialog__body {
    padding: 0;
    max-height: 80vh;
    overflow: hidden;
  }
}
</style>
