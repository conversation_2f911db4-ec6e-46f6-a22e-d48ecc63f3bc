<template>
  <div class="permission-setting-container">
    <!-- 搜索框区域 -->
    <div class="search-section">
      <div style="margin-bottom: 8px">
        <label style="font-size: 14px; color: #333">搜索权限：</label>
      </div>
      <el-input
        v-model="searchKeyword"
        placeholder="请输入权限名称进行搜索..."
        clearable
        class="search-input"
        style="width: 400px"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <!-- 调试信息 -->
      <div v-if="searchKeyword" style="margin-top: 8px; color: #666; font-size: 12px">
        当前搜索: "{{ searchKeyword }}" (共找到 {{ filteredTableData.length }} 条结果)
      </div>
    </div>

    <!-- 权限表格区域 -->
    <div class="table-section">
      <el-table :data="groupedTableData" border max-height="65vh" class="permission-table">
        <el-table-column label="一级菜单" width="150" align="center">
          <template #default="scope">
            <div class="menu-cell">
              <el-checkbox
                :model-value="getMenuCheckStatus(scope.row.menuId)"
                :indeterminate="getMenuIndeterminate(scope.row.menuId)"
                @change="handleMenuChange(scope.row.menuId, $event)"
                class="menu-checkbox"
              >
                <span class="menu-name">{{ scope.row.menuName }}</span>
              </el-checkbox>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="二级菜单及权限" min-width="500">
          <template #default="scope">
            <div class="routes-container">
              <div
                v-for="route in scope.row.routes"
                :key="route.id"
                class="route-item"
                :class="{ 'has-actions': route.actions && route.actions.length > 0 }"
                @mouseenter="handleRouteHover(route, true, $event)"
                @mouseleave="handleRouteHover(route, false, $event)"
              >
                <el-checkbox
                  v-model="checkBoxList.route['route_' + route.id]"
                  @change="handleRouteChange(scope.row.menuId)"
                  class="route-checkbox"
                >
                  <span class="route-name">{{ route.name }}</span>
                </el-checkbox>
                <span v-if="route.actions && route.actions.length > 0" class="action-indicator">
                  ({{ getSelectedActionCount(route.actions) }}/{{ route.actions.length }})
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 悬停显示的权限操作面板 -->
      <div
        v-if="hoveredRoute && hoveredRoute.actions && hoveredRoute.actions.length > 0"
        class="action-hover-panel"
        :style="actionPanelStyle"
        @mouseenter="handlePanelMouseEnter"
        @mouseleave="handlePanelMouseLeave"
      >
        <div class="panel-header">
          <span class="panel-title">{{ hoveredRoute.name }} - 权限操作</span>
          <!-- 调试信息，可以临时显示定位 -->
          <span class="debug-info" style="font-size: 10px; color: #999; margin-left: 10px">
            {{ actionPanelStyle.left }}, {{ actionPanelStyle.top }}
          </span>
        </div>
        <div class="panel-content">
          <div v-for="action in hoveredRoute.actions" :key="action.id" class="action-item">
            <el-checkbox
              v-model="checkBoxList.action['action_' + action.id]"
              @change="handleActionChange(hoveredRoute.menuId)"
              class="action-checkbox"
            >
              <span class="action-label">{{ action.name }}</span>
            </el-checkbox>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="button-section">
      <el-button @click="cancel" size="default">取消</el-button>
      <el-button @click="submit" type="primary" size="default">确认</el-button>
    </div>
  </div>
</template>

<script lang="ts">
import { toRaw, reactive, computed, watch, toRefs } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { setPositionMenuList } from '/@/api/permissions'

export default {
  name: 'adminRouteSetting',
  components: {
    Search
  },
  props: {
    id: {
      type: String,
      default: () => ''
    },
    tableData: {
      type: Array,
      default: () => []
    },
    checkBoxList: {
      type: Object,
      default: () => {}
    }
  },
  setup(props: any, { emit }: any) {
    // 响应式数据
    const state = reactive({
      searchKeyword: '', // 搜索关键词
      menuGroups: new Map(), // 存储菜单分组信息
      hoveredRoute: null as any, // 当前悬停的路由数据
      actionPanelStyle: {} as any, // 悬停面板的样式
      keepPanelVisible: false, // 保持面板可见
      panelHideTimer: null as any, // 面板隐藏定时器
      panelShowTimer: null as any // 面板显示定时器
    })

    // 计算过滤后的表格数据
    const filteredTableData = computed(() => {
      if (!state.searchKeyword.trim()) {
        return props.tableData
      }

      const keyword = state.searchKeyword.toLowerCase()
      const filtered = props.tableData.filter((item: any) => {
        // 搜索一级菜单名称
        const menuMatch = item.menu.name.toLowerCase().includes(keyword)
        // 搜索二级菜单名称
        const routeMatch = item.route.name.toLowerCase().includes(keyword)
        // 搜索权限操作名称
        const actionMatch = item.action?.some((action: any) =>
          action.name.toLowerCase().includes(keyword)
        )

        return menuMatch || routeMatch || actionMatch
      })

      return filtered
    })

    // 计算分组的表格数据（一级菜单为行，二级菜单为列）
    const groupedTableData = computed(() => {
      const groupedData: any[] = []
      const menuMap = new Map()

      // 按一级菜单分组
      filteredTableData.value.forEach((item: any) => {
        const menuId = item.menu.id
        if (!menuMap.has(menuId)) {
          menuMap.set(menuId, {
            menuId,
            menuName: item.menu.name,
            routes: []
          })
        }

        const menuGroup = menuMap.get(menuId)
        menuGroup.routes.push({
          id: item.route.id,
          name: item.route.name,
          actions: item.action || [],
          menuId
        })
      })

      // 转换为数组
      menuMap.forEach((value) => {
        groupedData.push(value)
      })

      return groupedData
    })

    // 初始化菜单分组信息
    const initMenuGroups = () => {
      const groups = new Map()
      props.tableData.forEach((item: any) => {
        const menuId = item.menu.id
        if (!groups.has(menuId)) {
          groups.set(menuId, {
            menuName: item.menu.name,
            routes: [],
            actions: []
          })
        }

        const group = groups.get(menuId)

        // 避免重复添加相同的路由
        const existingRoute = group.routes.find((route: any) => route.id === item.route.id)
        if (!existingRoute) {
          group.routes.push({
            id: item.route.id,
            name: item.route.name
          })
        }

        // 添加权限操作，避免重复
        if (item.action) {
          item.action.forEach((action: any) => {
            const existingAction = group.actions.find((act: any) => act.id === action.id)
            if (!existingAction) {
              group.actions.push({
                id: action.id,
                name: action.name,
                routeId: item.route.id
              })
            }
          })
        }
      })
      state.menuGroups = groups
    }

    // 监听 tableData 变化，重新初始化菜单分组
    watch(
      () => props.tableData,
      () => {
        initMenuGroups()
      },
      { immediate: true }
    )

    // 获取一级菜单的选中状态
    const getMenuCheckStatus = (menuId: string) => {
      const group = state.menuGroups.get(menuId)
      if (!group) return false

      // 检查该菜单下所有二级菜单是否都被选中
      const allRoutesChecked = group.routes.every(
        (route: any) => props.checkBoxList.route[`route_${route.id}`]
      )

      return allRoutesChecked && group.routes.length > 0
    }

    // 获取一级菜单的半选中状态
    const getMenuIndeterminate = (menuId: string) => {
      const group = state.menuGroups.get(menuId)
      if (!group) return false

      const checkedRoutes = group.routes.filter(
        (route: any) => props.checkBoxList.route[`route_${route.id}`]
      )

      return checkedRoutes.length > 0 && checkedRoutes.length < group.routes.length
    }

    // 处理一级菜单选择变化
    const handleMenuChange = (menuId: string, checked: boolean) => {
      const group = state.menuGroups.get(menuId)
      if (!group) return

      // 设置该菜单下所有二级菜单的选中状态
      group.routes.forEach((route: any) => {
        props.checkBoxList.route[`route_${route.id}`] = checked
      })

      // 设置该菜单下所有权限操作的选中状态
      group.actions.forEach((action: any) => {
        props.checkBoxList.action[`action_${action.id}`] = checked
      })
    }

    // 处理二级菜单选择变化
    const handleRouteChange = (menuId: string) => {
      // 当二级菜单状态改变时，检查是否需要更新一级菜单的状态
      // 这里不自动操作权限，让用户手动选择权限操作
    }

    // 处理权限操作选择变化
    const handleActionChange = (menuId: string) => {
      // 当权限操作状态改变时，可以在这里添加相关逻辑
      // 保持当前逻辑，不自动选中二级菜单，让用户手动控制
    }

    // 搜索处理函数
    const handleSearch = () => {
      // 搜索逻辑已在 computed 中处理，这里可以添加额外的搜索相关逻辑
    }

    // 处理二级菜单悬停事件
    const handleRouteHover = (route: any, isEnter: boolean, event?: MouseEvent) => {
      if (isEnter && route.actions && route.actions.length > 0) {
        // 清除隐藏定时器
        if (state.panelHideTimer) {
          clearTimeout(state.panelHideTimer)
          state.panelHideTimer = null
        }

        // 如果已经是当前悬停的路由，不重复设置
        if (state.hoveredRoute === route) return

        // 清除之前的显示定时器
        if (state.panelShowTimer) {
          clearTimeout(state.panelShowTimer)
        }

        // 延迟显示面板，避免快速移动时的闪烁
        state.panelShowTimer = setTimeout(() => {
          state.hoveredRoute = route
          state.keepPanelVisible = false

          // 计算面板位置，基于二级菜单项位置
          if (event) {
            const target = event.target as HTMLElement
            const routeItem = target.closest('.route-item') as HTMLElement

            if (routeItem) {
              const rect = routeItem.getBoundingClientRect()
              const viewportWidth = window.innerWidth
              const viewportHeight = window.innerHeight
              const panelWidth = 350
              const panelHeight = Math.min(250, route.actions.length * 30 + 60)

              let left: number, top: number

              // 计算最佳位置
              const spaceRight = viewportWidth - rect.right
              const spaceLeft = rect.left
              const spaceBottom = viewportHeight - rect.bottom
              const spaceTop = rect.top

              if (spaceRight >= panelWidth + 10) {
                // 右侧显示
                left = rect.right + 8
                top = rect.top - 5
              } else if (spaceLeft >= panelWidth + 10) {
                // 左侧显示
                left = rect.left - panelWidth - 8
                top = rect.top - 5
              } else if (spaceBottom >= panelHeight + 10) {
                // 下方显示
                left = Math.max(10, rect.left)
                top = rect.bottom + 8
              } else if (spaceTop >= panelHeight + 10) {
                // 上方显示
                left = Math.max(10, rect.left)
                top = rect.top - panelHeight - 8
              } else {
                // 默认右侧，即使空间不够
                left = rect.right + 8
                top = rect.top - 5
              }

              // 最终边界检查
              left = Math.max(5, Math.min(left, viewportWidth - panelWidth - 5))
              top = Math.max(5, Math.min(top, viewportHeight - panelHeight - 5))

              state.actionPanelStyle = {
                position: 'fixed',
                left: `${Math.round(left)}px`,
                top: `${Math.round(top)}px`,
                zIndex: 9999
              }
            }
          }
        }, 100) // 减少延迟到100ms
      } else if (!isEnter) {
        // 清除显示定时器
        if (state.panelShowTimer) {
          clearTimeout(state.panelShowTimer)
          state.panelShowTimer = null
        }

        // 只有在面板不需要保持可见时才隐藏
        if (!state.keepPanelVisible) {
          // 延迟隐藏面板，给用户时间移动到面板上
          state.panelHideTimer = setTimeout(() => {
            // 再次检查是否需要保持可见
            if (!state.keepPanelVisible) {
              state.hoveredRoute = null
            }
          }, 300) // 增加延迟时间，给用户更多时间移动到面板
        }
      }
    }

    // 处理面板鼠标进入事件
    const handlePanelMouseEnter = () => {
      // 清除所有隐藏定时器
      if (state.panelHideTimer) {
        clearTimeout(state.panelHideTimer)
        state.panelHideTimer = null
      }
      state.keepPanelVisible = true
    }

    // 处理面板鼠标离开事件
    const handlePanelMouseLeave = () => {
      state.keepPanelVisible = false
      // 延迟隐藏面板
      state.panelHideTimer = setTimeout(() => {
        state.hoveredRoute = null
      }, 200)
    }

    // 隐藏面板（带延迟）
    const hidePanelWithDelay = () => {
      state.keepPanelVisible = false
      state.panelHideTimer = setTimeout(() => {
        state.hoveredRoute = null
      }, 200)
    }

    // 获取选中的权限操作数量
    const getSelectedActionCount = (actions: any[]) => {
      return actions.filter((action) => props.checkBoxList.action[`action_${action.id}`]).length
    }

    // 获取菜单下的路由数量
    const getMenuRouteCount = (menuId: string) => {
      const group = state.menuGroups.get(menuId)
      return group ? group.routes.length : 0
    }

    // 取消操作
    const cancel = () => {
      emit('cancel')
    }

    // 提交操作
    const submit = () => {
      const checkBoxList = toRaw(props.checkBoxList)
      const menuList: string[] = []
      const actionList: string[] = []

      // 收集选中的二级菜单
      for (const key in checkBoxList.route) {
        if (checkBoxList.route[key]) {
          menuList.push(key.replace('route_', ''))
        }
      }

      // 收集选中的权限操作
      for (const key in checkBoxList.action) {
        if (checkBoxList.action[key]) {
          actionList.push(key.replace('action_', ''))
        }
      }

      // 提交到后端
      setPositionMenuList({
        id: props.id,
        menuList,
        actionList
      }).then(() => {
        emit('cancel')
      })
    }

    return {
      ...toRefs(state),
      filteredTableData,
      groupedTableData,
      getMenuCheckStatus,
      getMenuIndeterminate,
      handleMenuChange,
      handleRouteChange,
      handleActionChange,
      handleSearch,
      handleRouteHover,
      handlePanelMouseEnter,
      handlePanelMouseLeave,
      hidePanelWithDelay,
      getSelectedActionCount,
      getMenuRouteCount,
      cancel,
      submit
    }
  }
}
</script>

<style scoped lang="scss">
.permission-setting-container {
  padding: 16px 20px;
  background: #fff;
  min-height: 75vh;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .search-section {
    margin-bottom: 16px;
    flex-shrink: 0;
    position: relative;
    z-index: 10;

    .search-input {
      width: 350px;

      :deep(.el-input__wrapper) {
        height: 36px;
      }

      :deep(.el-input__inner) {
        height: 36px;
        font-size: 13px;
        pointer-events: auto;
        user-select: text;
      }
    }
  }

  .table-section {
    margin-bottom: 16px;
    flex: 1;
    overflow: hidden;

    .permission-table {
      height: 100%;
      font-size: 13px;

      :deep(.el-table__header) {
        background-color: #f8f9fa;

        th {
          background-color: #f8f9fa !important;
          color: #333;
          font-weight: 600;
          padding: 8px 6px;
          font-size: 13px;
          height: 40px;
        }
      }

      :deep(.el-table__body) {
        tr:hover {
          background-color: #f5f7fa;
        }

        td {
          padding: 6px 6px;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }

    // 一级菜单单元格样式
    .menu-cell {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px;

      .menu-checkbox {
        font-weight: 600;
        color: #333;

        .menu-name {
          font-size: 13px;
          font-weight: 600;
          color: #1890ff;
        }

        :deep(.el-checkbox__input) {
          margin-right: 8px;
        }
      }
    }

    // 二级菜单容器样式
    .routes-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 8px 12px;
      min-height: 40px;
      align-items: center;

      .route-item {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.15s ease;
        margin-bottom: 4px;
        position: relative;

        // 包含权限操作的二级菜单项样式
        &.has-actions {
          background-color: #e6f7ff;
          border-color: #91d5ff;

          .route-name {
            color: #1890ff;
            font-weight: 500;
          }

          .action-indicator {
            background-color: #1890ff;
            color: #fff;
          }
        }

        &:hover {
          background-color: #bae7ff;
          border-color: #1890ff;
          box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
          z-index: 1;

          &.has-actions {
            background-color: #91d5ff;
          }
        }

        .route-checkbox {
          margin-right: 0;

          .route-name {
            font-size: 12px;
            color: #555;
            margin-right: 4px;
          }

          :deep(.el-checkbox__input) {
            margin-right: 6px;
          }
        }

        .action-indicator {
          font-size: 10px;
          color: #999;
          background-color: #fff;
          padding: 1px 4px;
          border-radius: 2px;
          margin-left: 4px;
        }
      }
    }

    // 悬停面板样式
    .action-hover-panel {
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      width: 350px;
      z-index: 9999;
      animation: fadeInScale 0.1s ease-out;
      pointer-events: auto;

      .panel-header {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        background-color: #fafafa;
        border-radius: 8px 8px 0 0;

        .panel-title {
          font-size: 13px;
          font-weight: 600;
          color: #333;
        }
      }

      .panel-content {
        padding: 12px 16px;
        max-height: 300px;
        overflow-y: auto;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        // 确保滚动条不影响交互
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }

        .action-item {
          display: inline-flex;
          align-items: center;
          margin-bottom: 6px;

          .action-checkbox {
            margin-right: 0;

            :deep(.el-checkbox__input) {
              margin-right: 6px;
            }

            .action-label {
              font-size: 12px;
              color: #555;
              padding: 4px 8px;
              background-color: #f8f9fa;
              border-radius: 4px;
              border: 1px solid #e9ecef;
              transition: all 0.2s ease;
              display: inline-block;
              min-width: 60px;
              text-align: center;
              line-height: 1.3;
              white-space: nowrap;
            }

            &.is-checked {
              .action-label {
                background-color: #e6f7ff;
                border-color: #1890ff;
                color: #1890ff;
                font-weight: 500;
              }
            }

            &:hover {
              .action-label {
                border-color: #40a9ff;
                background-color: #f0f8ff;
              }
            }
          }
        }
      }
    }
  }

  .button-section {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 0 0 0;
    border-top: 1px solid #f0f0f0;
    flex-shrink: 0;
    background: #fff;

    .el-button {
      min-width: 70px;
      height: 32px;
      font-size: 13px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .permission-setting-container {
    padding: 12px;

    .search-section {
      .search-input {
        width: 100%;
      }
    }

    .table-section {
      .routes-container {
        .route-item {
          .action-indicator {
            font-size: 9px;
            padding: 0 3px;
          }
        }
      }
    }
  }
}

// 优化复选框样式
:deep(.el-checkbox) {
  .el-checkbox__input {
    &.is-indeterminate {
      .el-checkbox__inner {
        background-color: #1890ff;
        border-color: #1890ff;

        &::before {
          content: '';
          position: absolute;
          display: block;
          background-color: #fff;
          height: 2px;
          transform: scale(0.5);
          left: 0;
          right: 0;
          top: 5px;
        }
      }
    }
  }
}
</style>
