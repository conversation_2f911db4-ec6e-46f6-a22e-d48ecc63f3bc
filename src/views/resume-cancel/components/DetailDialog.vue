<template>
  <el-dialog
    v-model="visible"
    title="注销申请详情"
    width="80%"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="loading">
      <el-descriptions v-if="detail" :column="2" border>
        <!-- 基础信息 -->
        <el-descriptions-item label="注销日志ID">{{ detail.id }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ detail.memberId }}</el-descriptions-item>
        <el-descriptions-item label="简历ID">{{ detail.resumeId }}</el-descriptions-item>
        <el-descriptions-item label="操作管理员ID">{{
          detail.adminId || '系统自动'
        }}</el-descriptions-item>

        <!-- 用户信息 -->
        <el-descriptions-item label="用户姓名">{{ detail.name }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ detail.username }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ detail.mobile }}</el-descriptions-item>
        <el-descriptions-item label="手机区号">{{ detail.mobileCode }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ detail.email }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ detail.ip }}</el-descriptions-item>

        <!-- 注销信息 -->
        <el-descriptions-item label="注销状态">
          <el-tag :type="getStatusTagType(detail.status)" size="small">
            {{ detail.statusText }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="注销原因类型">{{
          detail.cancelReasonTypeText
        }}</el-descriptions-item>
        <el-descriptions-item label="注销原因详情" :span="2">
          {{ detail.cancelReasonDetail || '无' }}
        </el-descriptions-item>

        <!-- 时间信息 -->
        <el-descriptions-item label="申请时间">{{ detail.applyTimeFormat }}</el-descriptions-item>
        <el-descriptions-item label="冷静期结束时间">{{
          detail.cooldownEndTimeFormat
        }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{
          detail.completeTimeFormat || '未完成'
        }}</el-descriptions-item>
        <el-descriptions-item label="撤回时间">{{
          detail.withdrawTimeFormat || '未撤回'
        }}</el-descriptions-item>
        <el-descriptions-item label="剩余冷静期天数">
          <span :class="{ 'text-red-500': detail.remainingCooldownDays <= 0 }">
            {{ detail.remainingCooldownDays }}天
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="操作类型">{{ detail.operationTypeText }}</el-descriptions-item>

        <!-- 短信状态 -->
        <el-descriptions-item label="短信状态">{{ detail.smsStatusText }}</el-descriptions-item>
        <el-descriptions-item label="操作管理员">{{
          detail.adminName || '无'
        }}</el-descriptions-item>

        <!-- 用户状态 -->
        <el-descriptions-item label="用户状态">{{ detail.memberStatus }}</el-descriptions-item>
        <el-descriptions-item label="用户注销状态">{{
          detail.memberCancelStatus
        }}</el-descriptions-item>
      </el-descriptions>

      <!-- 简历设置信息 -->
      <el-card class="mt-20" header="简历设置信息" v-if="detail && detail.resumeSettingParsed">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="职位订阅">
            {{ detail.resumeSettingParsed.jobSubscribe ? '开启' : '关闭' }}
          </el-descriptions-item>
          <el-descriptions-item label="消息通知">
            {{ detail.resumeSettingParsed.messageNotify ? '开启' : '关闭' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 简历数据快照 -->
      <el-card class="mt-20" header="简历数据快照" v-if="detail && detail.resumeDataParsed">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">{{
            detail.resumeDataParsed.name || '无'
          }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{
            detail.resumeDataParsed.mobile || '无'
          }}</el-descriptions-item>
          <!-- 可以根据实际简历数据结构添加更多字段 -->
        </el-descriptions>

        <!-- 原始JSON数据展示（可折叠） -->
        <el-collapse class="mt-15">
          <el-collapse-item title="查看完整简历数据JSON" name="resumeJson">
            <pre class="json-display">{{ JSON.stringify(detail.resumeDataParsed, null, 2) }}</pre>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button v-if="detail && detail.status === 1" type="danger" @click="handleManualCancel">
          人工执行注销
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getResumeCancelDetail, manualCancelResume } from '/@/api/resumeCancel'

defineOptions({ name: 'DetailDialog' })

const emit = defineEmits(['refresh'])

const visible = ref(false)
const loading = ref(false)
const detail = ref(null)
const currentId = ref(0)

// 打开弹窗
const open = async (id: number) => {
  currentId.value = id
  visible.value = true
  await getDetail(id)
}

// 获取详情数据
const getDetail = async (id: number) => {
  loading.value = true
  try {
    const data = await getResumeCancelDetail({ cancelLogId: id })
    detail.value = data
  } catch (error) {
    console.error('获取注销申请详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 人工执行注销
const handleManualCancel = () => {
  ElMessageBox.confirm('确定要执行注销操作吗？此操作不可撤销！', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await manualCancelResume({ cancelLogId: currentId.value })
      ElMessage.success('注销操作执行成功')
      visible.value = false
      emit('refresh') // 通知父组件刷新列表
    } catch (error) {
      console.error('人工执行注销失败:', error)
    }
  })
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'warning' // 申请中
    case 2:
      return 'info' // 已撤回
    case 3:
      return 'success' // 已完成
    default:
      return ''
  }
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.mt-20 {
  margin-top: 20px;
}

.mt-15 {
  margin-top: 15px;
}

.text-red-500 {
  color: #ef4444;
}

.json-display {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.dialog-footer {
  text-align: right;
}
</style>
