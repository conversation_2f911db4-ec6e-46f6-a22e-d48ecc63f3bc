<template>
  <div class="filter-container-template">
    <el-form class="filter-grid-6" ref="form" :model="formData">
      <el-form-item label="注销日志ID" prop="cancelLogId">
        <el-input
          v-model="formData.cancelLogId"
          placeholder="请填写注销日志ID"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="formData.mobile"
          placeholder="请填写手机号"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="用户姓名" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请填写用户姓名"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="formData.email"
          placeholder="请填写邮箱"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="注销状态" prop="status">
        <el-select v-model="formData.status" placeholder="不限" clearable>
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label-width="10px">
        <div class="nowrap">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetField">重置</el-button>
          <el-link :underline="false" class="ml-10" @click="showMore = !showMore">
            {{ showMore ? '收起' : '展开' }}
          </el-link>
        </div>
      </el-form-item>

      <template v-if="showMore">
        <el-form-item label="注销原因类型" prop="cancelReasonType">
          <el-select v-model="formData.cancelReasonType" placeholder="不限" clearable>
            <el-option
              v-for="item in cancelReasonOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信状态" prop="smsStatus">
          <el-select v-model="formData.smsStatus" placeholder="不限" clearable>
            <el-option
              v-for="item in smsStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作管理员ID" prop="adminId">
          <el-input
            v-model="formData.adminId"
            placeholder="请填写管理员ID"
            @keyup.enter="handleSearch"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="IP地址" prop="ip">
          <el-input
            v-model="formData.ip"
            placeholder="请填写IP地址"
            @keyup.enter="handleSearch"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="申请时间" prop="applyTimeStart">
          <DatePickerRange
            v-model:start="formData.applyTimeStart"
            v-model:end="formData.applyTimeEnd"
          />
        </el-form-item>
        <el-form-item label="冷静期结束时间" prop="cooldownEndTimeStart">
          <DatePickerRange
            v-model:start="formData.cooldownEndTimeStart"
            v-model:end="formData.cooldownEndTimeEnd"
          />
        </el-form-item>
        <el-form-item label="完成时间" prop="completeTimeStart">
          <DatePickerRange
            v-model:start="formData.completeTimeStart"
            v-model:end="formData.completeTimeEnd"
          />
        </el-form-item>
        <el-form-item label="排序字段" prop="sortField">
          <el-select v-model="formData.sortField" placeholder="不限" clearable>
            <el-option label="申请时间" value="apply_time"></el-option>
            <el-option label="冷静期结束时间" value="cooldown_end_time"></el-option>
            <el-option label="完成时间" value="complete_time"></el-option>
            <el-option label="状态" value="status"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序方向" prop="sortOrder">
          <el-select v-model="formData.sortOrder" placeholder="不限" clearable>
            <el-option label="升序" value="ASC"></el-option>
            <el-option label="降序" value="DESC"></el-option>
          </el-select>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import { getResumeCancelFilterOptions } from '/@/api/resumeCancel'

defineOptions({ name: 'ResumeCancelFilter' })

const emit = defineEmits(['search', 'toggleShowMore'])

const form = ref()
const showMore = ref(false)

// 筛选选项
const statusOptions = ref([])
const cancelReasonOptions = ref([])
const smsStatusOptions = ref([])

// 表单数据
const formData = reactive({
  cancelLogId: '',
  mobile: '',
  name: '',
  email: '',
  status: '',
  cancelReasonType: '',
  smsStatus: '',
  applyTimeStart: '',
  applyTimeEnd: '',
  cooldownEndTimeStart: '',
  cooldownEndTimeEnd: '',
  completeTimeStart: '',
  completeTimeEnd: '',
  adminId: '',
  ip: '',
  sortField: '',
  sortOrder: '',
  page: 1,
  pageSize: 20
})

// 获取筛选选项
const getFilterOptions = async () => {
  try {
    const data = await getResumeCancelFilterOptions()
    statusOptions.value = data.statusOptions || []
    cancelReasonOptions.value = data.cancelReasonOptions || []
    smsStatusOptions.value = data.smsStatusOptions || []
  } catch (error) {
    console.error('获取筛选选项失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  emit('search', { ...formData })
}

// 重置
const handleResetField = () => {
  form.value.resetFields()
  Object.assign(formData, {
    cancelLogId: '',
    mobile: '',
    name: '',
    email: '',
    status: '',
    cancelReasonType: '',
    smsStatus: '',
    applyTimeStart: '',
    applyTimeEnd: '',
    cooldownEndTimeStart: '',
    cooldownEndTimeEnd: '',
    completeTimeStart: '',
    completeTimeEnd: '',
    adminId: '',
    ip: '',
    sortField: '',
    sortOrder: '',
    page: 1,
    pageSize: 20
  })
  handleSearch()
}

// 监听展开收起
const toggleShowMore = () => {
  emit('toggleShowMore')
}

// 初始化
onMounted(() => {
  getFilterOptions()
})
</script>

<style lang="scss" scoped>
.filter-container-template {
  position: relative;
}

.filter-grid-6 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;

  .el-form-item {
    margin-bottom: 15px;
  }
}

.nowrap {
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 10px;
}

.ml-10 {
  margin-left: 10px;
}
</style>
