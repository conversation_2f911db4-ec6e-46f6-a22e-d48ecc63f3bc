<template>
  <el-dialog
    v-model="visible"
    title="主动进入冷静期"
    width="600px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" v-loading="loading">
      <el-form-item label="简历ID" prop="resumeId">
        <el-input v-model="formData.resumeId" placeholder="请输入简历ID" type="number" clearable />
        <div class="form-tip">请输入需要进入冷静期的用户简历ID</div>
      </el-form-item>

      <el-form-item label="注销原因类型" prop="cancelReasonType">
        <el-select
          v-model="formData.cancelReasonType"
          placeholder="请选择注销原因类型"
          style="width: 100%"
          @change="handleReasonTypeChange"
        >
          <el-option
            v-for="item in reasonTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="注销原因详情" prop="cancelReasonDetail">
        <el-input
          v-model="formData.cancelReasonDetail"
          type="textarea"
          :rows="3"
          :placeholder="
            formData.cancelReasonType === 99
              ? '请详细说明注销原因（必填）'
              : '请详细说明注销原因（选填）'
          "
          maxlength="500"
          show-word-limit
        />
        <div class="form-tip">
          {{
            formData.cancelReasonType === 99
              ? '选择"其他原因"时必须填写详细说明'
              : '可选填写更详细的注销原因说明'
          }}
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确认进入冷静期
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { enterCooldown } from '/@/api/resumeCancel'

defineOptions({ name: 'EnterCooldownDialog' })

const emit = defineEmits(['refresh'])

const visible = ref(false)
const loading = ref(false)
const formRef = ref()

// 注销原因类型选项
const reasonTypeOptions = [
  { value: 1, label: '已找到新工作，以后不再打算找工作' },
  { value: 2, label: '换手机号了，重新注册' },
  { value: 3, label: '注册企业账号，误操作为个人账号' },
  { value: 4, label: '已有多个账号，想注销一个' },
  { value: 5, label: '担心隐私泄露' },
  { value: 6, label: '不想接收到邀约邮件' },
  { value: 99, label: '其他原因' }
]

// 表单数据
const formData = reactive({
  resumeId: '',
  cancelReasonType: '',
  cancelReasonDetail: ''
})

// 表单验证规则
const rules = {
  resumeId: [
    { required: true, message: '请输入简历ID', trigger: 'blur' },
    { pattern: /^\d+$/, message: '简历ID必须为数字', trigger: 'blur' }
  ],
  cancelReasonType: [{ required: true, message: '请选择注销原因类型', trigger: 'change' }],
  cancelReasonDetail: [
    {
      validator: (rule: any, value: string, callback: Function) => {
        // 如果选择的是"其他原因"(99)，则必填
        if (formData.cancelReasonType === 99) {
          if (!value || value.trim() === '') {
            callback(new Error('选择"其他原因"时必须填写详细说明'))
            return
          }
          if (value.trim().length < 5) {
            callback(new Error('注销原因详情至少需要5个字符'))
            return
          }
        }
        // 如果填写了内容，检查长度
        if (value && value.trim().length > 500) {
          callback(new Error('注销原因详情不能超过500个字符'))
          return
        }
        callback()
      },
      trigger: 'blur'
    }
  ]
}

// 打开对话框
const open = () => {
  visible.value = true
  resetForm()
}

// 重置表单
const resetForm = () => {
  formData.resumeId = ''
  formData.cancelReasonType = ''
  formData.cancelReasonDetail = ''
  formRef.value?.clearValidate()
}

// 注销原因类型变化处理
const handleReasonTypeChange = (value: number) => {
  // 重新验证注销原因详情字段，因为验证规则依赖于原因类型
  formRef.value?.validateField('cancelReasonDetail')
}

// 取消操作
const handleCancel = () => {
  visible.value = false
}

// 确认操作
const handleConfirm = () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (!valid) return

    // 显示确认对话框
    try {
      await ElMessageBox.confirm(
        '确定要让该用户进入冷静期吗？\n\n注意：\n• 用户将立即进入7天冷静期\n• 系统将执行预处理操作（如退出人才库等）\n• 此操作将记录操作日志\n\n请谨慎操作！',
        '确认进入冷静期',
        {
          confirmButtonText: '确定进入',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )

      // 执行进入冷静期操作
      await submitEnterCooldown()
    } catch (error) {
      // 用户取消操作
    }
  })
}

// 提交进入冷静期请求
const submitEnterCooldown = async () => {
  loading.value = true
  try {
    const params = {
      resumeId: Number(formData.resumeId),
      cancelReasonType: formData.cancelReasonType,
      // 如果填写了注销原因详情，则包含在请求中（无论是否为"其他原因"）
      ...(formData.cancelReasonDetail &&
        formData.cancelReasonDetail.trim() && {
          cancelReasonDetail: formData.cancelReasonDetail.trim()
        })
    }

    const result = await enterCooldown(params)

    visible.value = false
    emit('refresh') // 通知父组件刷新列表
  } catch (error: any) {
    console.error('进入冷静期失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.dialog-footer {
  text-align: right;
}
</style>
