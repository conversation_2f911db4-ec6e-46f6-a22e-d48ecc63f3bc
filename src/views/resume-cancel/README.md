# 注销管理功能

## 功能概述

注销管理功能用于管理求职者的注销申请，包括查看注销申请列表、详情、统计数据以及人工执行注销操作。

## 文件结构

```
src/views/resume-cancel/
├── list.vue                    # 注销申请列表页面
├── components/
│   └── DetailDialog.vue        # 详情弹窗组件
└── README.md                   # 说明文档

src/api/resumeCancel.ts          # API接口文件
src/router/module/resumeCancel.ts # 路由配置文件
```

## 功能特性

### 1. 注销申请列表页面 (list.vue)

**搜索筛选功能：**
- 基础信息搜索：注销日志ID、手机号、用户姓名、邮箱、IP地址
- 状态筛选：注销状态、注销原因类型、短信发送状态、操作管理员ID
- 时间范围筛选：申请时间、冷静期结束时间、完成时间
- 排序设置：支持多字段排序

**数据展示功能：**
- 统计数据展示：总申请数、今日申请、本周申请、本月申请
- 列表数据展示：用户信息（脱敏）、注销状态、申请时间等
- 分页功能：支持分页浏览
- 导出功能：支持数据导出

**操作功能：**
- 查看详情：弹窗查看完整注销申请信息
- 人工执行注销：对申请中的记录执行注销操作

### 2. 详情弹窗组件 (DetailDialog.vue)

**信息展示：**
- 基础信息：注销日志ID、用户ID、简历ID等
- 用户信息：姓名、手机号、邮箱等（完整信息）
- 注销信息：状态、原因、时间等
- 简历设置信息：职位订阅、消息通知等设置
- 简历数据快照：完整的简历数据JSON

**操作功能：**
- 人工执行注销：在详情页面也可执行注销操作

### 3. API接口 (resumeCancel.ts)

实现了以下6个API接口：
- `getResumeCancelList` - 获取注销申请列表
- `getResumeCancelDetail` - 获取注销申请详情
- `getResumeCancelStatistics` - 获取统计数据
- `getResumeCancelFilterOptions` - 获取筛选选项
- `manualCancelResume` - 人工执行注销
- `exportResumeCancelList` - 导出注销申请列表

## 路由配置

路由路径：`/resume-cancel/list`
菜单名称：注销管理 > 注销申请列表

## 技术实现

### 1. 组件技术栈
- Vue 3 Composition API
- Element Plus UI组件库
- TypeScript类型支持
- SCSS样式预处理

### 2. 数据处理
- 响应式数据管理
- 参数自动过滤（空值不传递）
- 错误处理（使用项目公共错误处理机制）

### 3. UI设计
- 参考现有公告管理和职位管理的设计风格
- 保持界面风格一致性
- 响应式布局设计

## 使用说明

1. **访问页面**：通过左侧菜单"注销管理"进入注销申请列表页面

2. **搜索筛选**：使用顶部搜索表单进行条件筛选，支持多条件组合查询

3. **查看详情**：点击列表中的"查看详情"按钮，在弹窗中查看完整信息

4. **执行注销**：对于状态为"申请中"的记录，可以点击"人工执行注销"按钮进行手动处理

5. **导出数据**：点击"导出"按钮可以导出当前筛选条件下的数据

## 注意事项

1. 人工执行注销操作不可撤销，操作前会有确认提示
2. 导出功能为异步处理，完成后会通过企业微信通知
3. 敏感信息（手机号、邮箱）在列表中显示为脱敏格式，详情中显示完整信息
4. 剩余冷静期天数为0或负数时会用红色标识
