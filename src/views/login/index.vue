<template>
  <div class="login-container">
    <!-- 左侧品牌展示区域 -->
    <div class="brand-section">
      <div class="decoration-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>

      <div class="brand-logo">
        <img
          src="https://img.gaoxiaojob.com/uploads/static/image/logo/logo_6.png"
          alt="高才信息科技 Logo"
          class="company-logo"
        />
      </div>

      <div class="brand-info">
        <h1 class="brand-title">{{ getThemeConfig.globalTitle }}</h1>
        <p class="brand-subtitle">{{ getThemeConfig.globalViceTitle }}</p>
        <p class="brand-description">致力于为高校人才提供优质的就业服务平台，连接人才与机遇</p>
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="form-section">
      <div class="form-container">
        <div class="form-header">
          <h2 class="form-title">欢迎回来</h2>
          <p class="form-subtitle">请使用您的账号登录</p>
        </div>

        <div class="form-content">
          <div v-if="loading" class="loading-container">
            <el-icon class="is-loading">
              <Loading />
            </el-icon>
            <p>正在加载登录方式...</p>
          </div>
          <Account v-else-if="tabsActiveName === 'account'" />
          <Scan v-else-if="tabsActiveName === 'scan'" />
        </div>

        <div class="form-footer">
          <div class="copyright">
            <p>© 2025 广州高才信息科技有限公司</p>
            <p>保留所有权利</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { toRefs, reactive, computed, getCurrentInstance, onMounted } from 'vue'
import Account from '/@/views/login/component/account.vue'
import Scan from '/@/views/login/component/scan.vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { initBackEndControlRoutes } from '/@/router/backEnd'
import { useStore } from '/@/store/index'
import { useRoute, useRouter } from 'vue-router'
import { Session, Local } from '/@/utils/storage'
import { getDefaultLoginType } from '/@/api/system'

export default {
  name: 'login',
  components: { Account, Scan, Loading },
  setup() {
    const { proxy } = getCurrentInstance() as any
    const store = useStore()
    const route = useRoute()
    const router = useRouter()
    const state = reactive({
      tabsActiveName: 'account',
      loading: false
    })
    // 获取布局配置信息
    const getThemeConfig = computed(() => {
      return store.state.themeConfig.themeConfig
    })
    // 切换登录方式
    // const onTabsClick = (tab: any) => {
    //   state.tabsActiveName = tab.name
    // }

    // 获取默认登录方式
    const fetchDefaultLoginType = async () => {
      try {
        state.loading = true
        const res = await getDefaultLoginType()
        if (res && res.defaultLoginType) {
          state.tabsActiveName = res.defaultLoginType
        }
      } catch (error) {
        console.warn('获取默认登录方式失败，使用默认配置:', error)
      } finally {
        state.loading = false
      }
    }

    // 登录成功后的跳转
    const afterLogininSuccess = () => {
      // 登录成功，跳到转首页
      // 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
      // 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
      if (route.query?.redirect) {
        router.push({
          path: <string>route.query?.redirect,
          query:
            Object.keys(<Object>route.query?.params).length > 0
              ? JSON.parse(<string>route.query?.params)
              : ''
        })
      } else {
        router.push('/')
      }
      // 登录成功提示
      setTimeout(() => {
        const loginText = '登录成功'
        ElMessage.success(`${loginText}`)
        // 修复防止退出登录再进入界面时，需要刷新样式才生效的问题，初始化布局样式等(登录的时候触发，目前方案)
        proxy.mittBus.emit('onLoginClick')
        proxy.mittBus.off('loginSuccess')
      }, 300)
    }

    proxy.mittBus.on('loginSuccess', async (rs: any) => {
      let defaultAuthPageList: Array<string> = []
      let defaultAuthBtnList: Array<string> = []
      // admin 页面权限标识，对应路由 meta.auth，用于控制路由的显示/隐藏
      const adminAuthPageList: Array<string> = ['admin']
      // admin 按钮权限标识
      const adminAuthBtnList: Array<string> = ['btn.add', 'btn.del', 'btn.edit', 'btn.link']
      // 不同用户模拟不同的用户权限
      defaultAuthPageList = adminAuthPageList
      defaultAuthBtnList = adminAuthBtnList
      // 用户信息模拟数据
      const userInfos = {
        account: rs.username || rs.name,
        photo: rs.photo,
        status: rs.status,
        email: rs.email,
        mobile: rs.mobile,
        time: new Date().getTime(),
        authPageList: defaultAuthPageList,
        authBtnList: defaultAuthBtnList
      }
      // 存储 token 到浏览器缓存
      Local.set('token', rs.token)
      // 存储用户信息到浏览器缓存
      Local.set('userInfo', userInfos)
      // 1、请注意执行顺序(存储用户信息到vuex)
      store.dispatch('userInfos/setUserInfos', userInfos)
      // 模拟后端控制路由，isRequestRoutes 为 true，则开启后端控制路由
      // 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
      await initBackEndControlRoutes()
      // 执行完 initBackEndControlRoutes，再执行 afterLogininSuccess
      afterLogininSuccess()
    })

    // 组件挂载时获取默认登录方式
    onMounted(() => {
      fetchDefaultLoginType()
    })

    return {
      // onTabsClick,
      getThemeConfig,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

// 左侧品牌展示区域
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #ff9a41 0%, #ff6b35 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  padding: 60px 40px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  .brand-logo {
    margin-bottom: 30px;
    position: relative;
    z-index: 1;

    .company-logo {
      width: 120px;
      height: 120px;
      border-radius: 24px;
      object-fit: cover;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
      border: 4px solid rgba(255, 255, 255, 0.2);

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
      }
    }
  }

  .brand-info {
    position: relative;
    z-index: 1;

    .brand-title {
      font-size: 3rem;
      font-weight: 800;
      margin-bottom: 20px;
      text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      letter-spacing: -0.5px;
      line-height: 1.1;
    }

    .brand-subtitle {
      font-size: 1.3rem;
      opacity: 0.9;
      font-weight: 300;
      line-height: 1.5;
      margin-bottom: 30px;
    }

    .brand-description {
      font-size: 1rem;
      opacity: 0.8;
      font-weight: 300;
      line-height: 1.6;
      max-width: 400px;
      margin: 0 auto;
    }
  }

  // 装饰性几何图形
  .decoration-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 0;

    .shape {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.05);
      animation: float 15s ease-in-out infinite;

      &.shape-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: -50px;
        animation-delay: 0s;
      }

      &.shape-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: -30px;
        animation-delay: 5s;
      }

      &.shape-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-delay: 10s;
      }
    }
  }
}

// 右侧登录表单区域
.form-section {
  flex: 1;
  background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 80% 20%, rgba(255, 154, 65, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.02) 0%, transparent 50%);
    pointer-events: none;
  }

  .form-container {
    width: 100%;
    max-width: 500px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 40px;
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.05),
      0 1px 8px rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.6);
    position: relative;
    z-index: 1;

    .form-header {
      text-align: center;
      margin-bottom: 40px;

      .form-title {
        font-size: 2.2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 10px;
        letter-spacing: -0.5px;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .form-subtitle {
        font-size: 1rem;
        color: #7f8c8d;
        font-weight: 400;
        opacity: 0.8;
      }
    }

    .form-content {
      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 0;
        color: #6c757d;

        .el-icon {
          font-size: 24px;
          margin-bottom: 12px;
          color: #ff9a41;
        }

        p {
          font-size: 14px;
          margin: 0;
          opacity: 0.8;
        }
      }
    }

    .form-footer {
      text-align: center;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #e9ecef;

      .copyright {
        p {
          font-size: 12px;
          color: #adb5bd;
          margin: 4px 0;
          line-height: 1.5;
        }
      }
    }
  }
}

// 动画效果
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.9;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
  }

  .brand-section {
    padding: 40px 20px;
    min-height: 300px;

    .brand-info {
      .brand-title {
        font-size: 2.5rem;
      }

      .brand-subtitle {
        font-size: 1.1rem;
      }

      .brand-description {
        font-size: 0.9rem;
      }
    }

    .brand-logo .company-logo {
      width: 80px;
      height: 80px;
    }
  }

  .form-section {
    padding: 40px 20px;
    min-height: calc(100vh - 300px);

    .form-container {
      max-width: 350px;
      padding: 30px;

      .form-header .form-title {
        font-size: 2rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .brand-section {
    padding: 30px 20px;
    min-height: 250px;

    .brand-info {
      .brand-title {
        font-size: 2rem;
      }

      .brand-subtitle {
        font-size: 1rem;
      }
    }

    .brand-logo .company-logo {
      width: 60px;
      height: 60px;
    }
  }

  .form-section {
    padding: 30px 20px;

    .form-container {
      max-width: 300px;
      padding: 25px;

      .form-header .form-title {
        font-size: 1.8rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .brand-section {
    padding: 20px 15px;
    min-height: 200px;

    .brand-info {
      .brand-title {
        font-size: 1.6rem;
      }

      .brand-subtitle {
        font-size: 0.9rem;
      }
    }
  }

  .form-section {
    padding: 20px 15px;

    .form-container {
      padding: 20px;

      .form-header .form-title {
        font-size: 1.5rem;
      }
    }
  }
}
</style>
