<template>
  <div class="account-login-form">
    <el-form class="login-form" ref="accountLoginForm" :rules="rule" :model="ruleForm">
      <el-form-item prop="account">
        <el-input
          type="text"
          placeholder="用户名"
          v-model="ruleForm.username"
          @keyup.enter="onLogin"
          clearable
          autocomplete="off"
          size="large"
          class="login-input"
        >
          <template #prefix>
            <el-icon class="input-icon">
              <User />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item prop="password">
        <el-input
          :type="isShowPassword ? 'text' : 'password'"
          placeholder="密码"
          v-model="ruleForm.password"
          autocomplete="off"
          @keyup.enter="onLogin"
          size="large"
          class="login-input"
        >
          <template #prefix>
            <el-icon class="input-icon">
              <Lock />
            </el-icon>
          </template>
          <template #suffix>
            <el-icon class="password-toggle" @click="isShowPassword = !isShowPassword">
              <View v-if="!isShowPassword" />
              <Hide v-else />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          class="login-submit-btn"
          @click="onLogin"
          :loading="loading.login"
          :disabled="!canSubmit"
          size="large"
        >
          立即登录
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts">
import { toRefs, reactive, defineComponent, computed, getCurrentInstance } from 'vue'
import { accountLogin } from '/@/api/admin'
import { User, Lock, View, Hide } from '@element-plus/icons-vue'

export default defineComponent({
  name: 'login',
  components: {
    User,
    Lock,
    View,
    Hide
  },
  setup() {
    const { proxy } = getCurrentInstance() as any
    const state = reactive({
      isShowPassword: false,
      ruleForm: {
        username: '',
        password: ''
      },
      rule: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      },
      loading: {
        login: false
      }
    })

    const canSubmit = computed(() => {
      return state.ruleForm.username !== '' && state.ruleForm.password !== ''
    })

    if (process.env.NODE_ENV === 'development') {
      state.ruleForm.username = 'admin'
      state.ruleForm.password = '123456'
    }

    // 表单验证
    const formRulesValidate = () => {
      return new Promise((resolve) => {
        proxy.$refs.accountLoginForm.validate((valid: any) => {
          if (valid) resolve(valid)
        })
      })
    }

    // 登录
    const onLogin = async () => {
      await formRulesValidate()
      try {
        state.loading.login = true
        const rs = await accountLogin(state.ruleForm)
        // 关闭 loading
        state.loading.login = false
        proxy.mittBus.emit('loginSuccess', rs)
        // 弹出一个长时间警告
        // ElNotification({
        //   title: '警告',
        //   message: '账号密码登录功能即将在1月13号下线,请替换成企业微信扫码登录',
        //   type: 'warning',
        //   duration: 0
        // })
      } catch (error) {
        state.loading.login = false
      }
    }

    return {
      onLogin,
      canSubmit,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
.account-login-form {
  width: 100%;

  .login-form {
    .el-form-item {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .login-input {
      :deep(.el-input__wrapper) {
        padding: 16px 20px;
        border-radius: 12px;
        border: 2px solid #e9ecef;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        box-shadow: none;

        &:hover {
          border-color: #ff9a41;
          background-color: #fff;
        }

        &.is-focus {
          border-color: #ff9a41;
          background-color: #fff;
          box-shadow: 0 0 0 4px rgba(255, 154, 65, 0.1);
        }
      }

      :deep(.el-input__inner) {
        font-size: 16px;
        color: #2c3e50;
        line-height: 1.5;

        &::placeholder {
          color: #adb5bd;
          font-weight: 400;
        }
      }

      .input-icon {
        color: #6c757d;
        font-size: 18px;
        margin-right: 8px;
      }

      .password-toggle {
        color: #6c757d;
        font-size: 18px;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #ff9a41;
        }
      }
    }

    .login-submit-btn {
      width: 100%;
      height: 56px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 12px;
      background: linear-gradient(135deg, #ff9a41 0%, #ff6b35 100%);
      border: none;
      margin-top: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #e8873a 0%, #e55a2e 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 154, 65, 0.3);
      }

      &:active {
        transform: translateY(0);
      }

      &:disabled {
        background: #e9ecef;
        color: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;

        &:hover {
          background: #e9ecef;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }
}

// 表单验证错误样式
:deep(.el-form-item.is-error) {
  .el-input__wrapper {
    border-color: #dc3545 !important;
    background-color: #fff !important;
    box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.1) !important;
  }
}

:deep(.el-form-item__error) {
  color: #dc3545;
  font-size: 14px;
  margin-top: 8px;
  font-weight: 500;
}
</style>
