<template>
  <div class="scan-login-container">
    <div class="scan-content">
      <div class="scan-qr-wrapper">
        <div id="wx_login_panel" class="wx-login-panel"></div>
      </div>
    </div>

    <!-- 企业微信登录引导弹窗 -->
    <el-dialog
      v-model="showWeComGuidanceDialog"
      title="登录提示"
      width="420px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="canCloseDialog"
      class="guidance-dialog"
    >
      <div class="guidance-content">
        <div class="icon-wrapper">
          <el-icon size="48" color="#E6A23C">
            <Warning />
          </el-icon>
        </div>
        <p class="guidance-text">检测到您正在使用企业微信环境，请注意：</p>
        <div class="guidance-steps">
          <p class="step-item error-step">❌ 请不要点击"在企业微信桌面端打开"</p>
          <p class="step-item success-step">✅ 请点击"继续在浏览器中登录访问"</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="closeGuidanceDialog" class="confirm-btn">
            我知道了
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { toRefs, reactive, defineComponent, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { getWxWorkLoginConfig, checkWxWorkLogin } from '/@/api/admin'
import { ElMessageBox, ElDialog, ElButton, ElIcon } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'

// 企业微信 JS-SDK 类型声明
declare global {
  interface Window {
    ww: {
      createWWLoginPanel: (options: any) => any
    }
  }
}

export default defineComponent({
  name: 'loginScan',
  components: {
    Warning
  },
  setup() {
    const { proxy } = getCurrentInstance() as any
    const state = reactive({
      loading: true,
      isWeComLoggedIn: false,
      showWeComGuidanceDialog: false
    })

    let wwLoginPanel: any = null
    let currentLoginState = ''
    // 初始化企业微信登录组件
    const initWxWorkLogin = async () => {
      try {
        state.loading = true

        // 检查企业微信 JS-SDK 是否已加载
        await ensureWxWorkSDK()

        const config = await getWxWorkLoginConfig()
        const { appid, agentid, redirectUri, state: loginState } = config

        // 保存当前的 loginState 供回调使用
        currentLoginState = loginState

        console.log('企业微信登录配置:', { appid, agentid, redirectUri, loginState })

        // 使用官方文档的新 API 创建企业微信登录组件
        try {
          wwLoginPanel = window.ww.createWWLoginPanel({
            el: '#wx_login_panel', // 使用 el 而不是 id
            params: {
              login_type: 'CorpApp',
              appid,
              agentid,
              redirect_uri: redirectUri,
              state: loginState,
              redirect_type: 'callback', // 使用回调模式
              panel_size: 'small', // 尺寸 320x380px (可选: small=320x380px)
              lang: 'zh'
            },

            // 检查企业微信登录状态，自动登录但不显示桌面端按钮
            onCheckWeComLogin({ isWeComLogin }) {
              console.log('检查企业微信登录状态:', { isWeComLogin })
              state.isWeComLoggedIn = isWeComLogin
              if (isWeComLogin) {
                // 显示引导弹窗
                showGuidanceDialog()
              }
            },

            // 登录成功回调
            async onLoginSuccess({ code }) {
              console.log('企业微信登录成功，获得 code:', { code })

              try {
                state.loading = true

                // 使用 code 和 state 调用后端接口验证并获取用户信息
                const loginResult = await checkWxWorkLogin({
                  code,
                  state: currentLoginState
                })

                if (loginResult && loginResult.token) {
                  console.log('登录验证成功:', loginResult)
                  proxy.mittBus.emit('loginSuccess', loginResult)
                } else {
                  console.error('登录验证失败，未获得有效 token')
                }
              } catch (error) {
                console.error('登录验证出错:', error)
              } finally {
                state.loading = false
              }
            },

            // 登录失败回调
            onLoginFail(err: any) {
              console.error('企业微信登录失败:', err)
              state.loading = false
            }
          })

          console.log('企业微信登录组件初始化成功:', wwLoginPanel)
        } catch (error) {
          console.error('创建企业微信登录组件失败:', error)
          state.loading = false
          throw error
        }

        state.loading = false
        console.log('企业微信登录组件初始化完成')
      } catch (error) {
        console.error('初始化企业微信登录失败:', error)
        state.loading = false
      }
    }

    // 页面加载时自动初始化
    onMounted(() => {
      initWxWorkLogin()
    })

    // 页面销毁时
    onUnmounted(() => {
      if (wwLoginPanel && wwLoginPanel.unmount) {
        wwLoginPanel.unmount()
      }
    })

    // 显示引导弹窗
    const showGuidanceDialog = () => {
      state.showWeComGuidanceDialog = true
    }

    // 关闭引导弹窗
    const closeGuidanceDialog = () => {
      state.showWeComGuidanceDialog = false
    }
    // 确保企业微信 SDK 已加载
    const ensureWxWorkSDK = () => {
      return new Promise((resolve, reject) => {
        let retryCount = 0
        const maxRetries = 10

        const checkSDK = () => {
          console.log(`检查企业微信 SDK 第 ${retryCount + 1} 次:`, {
            wwExists: typeof window.ww !== 'undefined',
            wwType: typeof window.ww,
            hasCreateWWLoginPanel: window.ww && typeof window.ww.createWWLoginPanel === 'function'
          })

          // 检查新版 wecom-jssdk-2.0.2 的 ww.createWWLoginPanel
          if (
            typeof window.ww !== 'undefined' &&
            typeof window.ww.createWWLoginPanel === 'function'
          ) {
            console.log('找到企微 JS-SDK 2.0.2 ww.createWWLoginPanel')
            resolve(true)
            return
          }

          retryCount++
          if (retryCount < maxRetries) {
            // 等待 500ms 再检查
            setTimeout(checkSDK, 500)
          } else {
            reject(new Error('多次尝试后仍未找到企业微信 SDK 2.0.2'))
          }
        }

        // 立即开始检查
        checkSDK()
      })
    }

    return {
      initWxWorkLogin,
      showGuidanceDialog,
      closeGuidanceDialog,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
.scan-login-container {
  width: 100%;
  text-align: center;

  .scan-content {
    .scan-header {
      margin-bottom: 32px;

      .scan-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 64px;
        height: 64px;
        background: linear-gradient(135deg, #ff9a41 0%, #ff6b35 100%);
        border-radius: 16px;
        color: white;
        margin: 0 auto 16px;
        box-shadow: 0 8px 25px rgba(255, 154, 65, 0.3);
      }

      h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
      }

      p {
        font-size: 1rem;
        color: #6c757d;
        margin: 0;
      }
    }

    .scan-qr-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 32px;
      min-height: 320px;

      .wx-login-panel {
        display: flex;
        justify-content: center;
        align-items: center;
        border: none;
        border-radius: 0;
        background: transparent;
        box-shadow: none;
        padding: 0;
        width: 600px;

        :deep(iframe) {
          border-radius: 12px;
        }
      }
    }

    .scan-tips {
      display: flex;
      justify-content: space-around;
      max-width: 350px;
      margin: 0 auto;

      .tip-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;

        .tip-number {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          background: linear-gradient(135deg, #ff9a41 0%, #ff6b35 100%);
          color: white;
          border-radius: 50%;
          font-size: 14px;
          font-weight: 600;
        }

        .tip-text {
          font-size: 14px;
          color: #6c757d;
          font-weight: 500;
          white-space: nowrap;
        }
      }
    }
  }
}

// 引导弹窗样式
.guidance-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
  }

  :deep(.el-dialog__header) {
    padding: 24px 24px 16px;
    background: linear-gradient(135deg, #ff9a41 0%, #ff6b35 100%);
    color: white;

    .el-dialog__title {
      font-weight: 600;
      font-size: 1.1rem;
    }
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px 24px;
    text-align: center;
  }
}

.guidance-content {
  text-align: center;

  .icon-wrapper {
    margin-bottom: 20px;
  }

  .guidance-text {
    font-size: 16px;
    color: #2c3e50;
    margin-bottom: 24px;
    line-height: 1.6;
    font-weight: 500;
  }

  .guidance-steps {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;

    .step-item {
      margin: 12px 0;
      font-size: 15px;
      line-height: 1.6;
      font-weight: 500;

      &.error-step {
        color: #dc3545;
      }

      &.success-step {
        color: #28a745;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;

  .confirm-btn {
    padding: 12px 32px;
    border-radius: 8px;
    font-weight: 600;
    background: linear-gradient(135deg, #ff9a41 0%, #ff6b35 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #e8873a 0%, #e55a2e 100%);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .scan-login-container {
    .scan-content {
      .scan-header {
        margin-bottom: 24px;

        .scan-icon {
          width: 48px;
          height: 48px;
          margin-bottom: 12px;

          svg {
            width: 24px;
            height: 24px;
          }
        }

        h3 {
          font-size: 1.25rem;
        }

        p {
          font-size: 0.9rem;
        }
      }

      .scan-qr-wrapper {
        margin-bottom: 24px;
        min-height: 280px;

        .wx-login-panel {
          padding: 0;
          width: 100%;
          max-width: 400px;
        }
      }

      .scan-tips {
        flex-direction: column;
        gap: 16px;

        .tip-item {
          flex-direction: row;
          justify-content: center;
          gap: 12px;
        }
      }
    }
  }
}
</style>
