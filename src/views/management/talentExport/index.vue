<template>
  <div class="talent-export-container">
    <div class="box">
      <!-- 筛选条件区域 -->
      <FilterForm ref="filterFormRef" @search="handleSearch" @export="handleExport" />

      <!-- 数据预览区域 -->
      <div class="preview-section" v-if="showPreview">
        <div class="preview-header">
          <h3>数据预览</h3>
          <span class="total-count">共找到 {{ pagination.total }} 条符合条件的数据</span>
        </div>

        <!-- 批量操作栏 -->
        <div class="batch-operations" v-if="selectedTalents.size > 0">
          <div class="selected-info">
            <el-icon class="selected-icon"><Select /></el-icon>
            <span>已选中 {{ selectedTalents.size }} 个人才</span>
            <el-button size="small" type="text" @click="clearAllSelection">清空选择</el-button>
          </div>
          <div class="batch-actions">
            <el-button type="primary" @click="openTagDialog"> 批量添加标签 </el-button>
          </div>
        </div>

        <!-- 数据表格 -->
        <el-table
          v-loading="loading"
          :data="previewData"
          border
          size="small"
          style="width: 100%"
          :empty-text="previewData.length === 0 && !loading ? '暂无符合条件的数据' : ''"
          @selection-change="handleSelectionChange"
          ref="tableRef"
          row-key="id"
        >
          <el-table-column type="selection" width="55" align="center" :reserve-selection="true" />
          <el-table-column prop="id" label="ID" width="120" align="center" />
          <el-table-column prop="name" label="姓名" width="120" align="center" />
          <el-table-column prop="age" label="年龄" width="80" align="center" />
          <el-table-column prop="householdRegister" label="户籍/国籍" width="120" align="center" />
          <el-table-column prop="residence" label="籍贯" width="120" align="center" />
          <el-table-column prop="tags" label="人才标签" width="200" align="center">
            <template #default="{ row }">
              <div class="talent-tags">
                <span v-if="row.tags">{{ row.tags }}</span>
                <span v-else class="no-data">无</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="avatar" label="自定义头像" width="120" align="center">
            <template #default="{ row }">
              <div class="avatar-preview">
                <el-image
                  v-if="row.avatar"
                  :src="row.avatar"
                  :preview-src-list="[row.avatar]"
                  :hide-on-click-modal="true"
                  fit="cover"
                  class="avatar-thumbnail"
                  :preview-teleported="true"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                      <span>加载失败</span>
                    </div>
                  </template>
                </el-image>
                <span v-else class="no-avatar">未上传</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="applyCount" label="站内投递量" width="120" align="center" />
          <el-table-column prop="majorInfo" label="最高学历专业" width="180" align="center" />
          <el-table-column prop="doctorSchool" label="博士毕业院校" width="150" align="center">
            <template #default="{ row }">
              <span v-if="row.doctorSchool">{{ row.doctorSchool }}</span>
              <span v-else class="no-data">无</span>
            </template>
          </el-table-column>
          <el-table-column prop="masterSchool" label="硕士毕业院校" width="150" align="center">
            <template #default="{ row }">
              <span v-if="row.masterSchool">{{ row.masterSchool }}</span>
              <span v-else class="no-data">无</span>
            </template>
          </el-table-column>
          <el-table-column prop="bachelorSchool" label="本科毕业院校" width="150" align="center">
            <template #default="{ row }">
              <span v-if="row.bachelorSchool">{{ row.bachelorSchool }}</span>
              <span v-else class="no-data">无</span>
            </template>
          </el-table-column>
          <el-table-column prop="registerTime" label="注册时间" width="160" align="center" />
          <el-table-column prop="lastActiveTime" label="最近活跃时间" width="160" align="center" />
          <!-- 操作列 -->
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                @click="handleNationalityAnalysis(row)"
                :loading="row.analyzing"
              >
                外籍分析
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <Paging
          class="mt-20 center"
          :total="pagination.total"
          :page="pagination.page"
          :size="pagination.pageSize"
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- 标签管理对话框 -->
    <el-dialog
      v-model="showTagDialog"
      title="批量贴标签"
      width="500px"
      :before-close="handleTagDialogClose"
    >
      <div class="tag-management">
        <div class="selected-talents-info">
          <p class="info-title">将为以下 {{ selectedTalents.size }} 个人才贴标签：</p>
          <div class="talent-list">
            <el-tag
              v-for="talentId in Array.from(selectedTalents).slice(0, 10)"
              :key="String(talentId)"
              size="small"
              class="talent-tag"
              type="info"
            >
              {{ getTalentNameById(String(talentId)) }}
            </el-tag>
            <span v-if="selectedTalents.size > 10" class="more-count">
              ... 等共 {{ selectedTalents.size }} 个人才
            </span>
          </div>
        </div>

        <el-divider />

        <div class="tag-form-section">
          <el-form ref="tagFormRef" :model="tagFormData" :rules="tagRules">
            <el-form-item label="" prop="tag">
              <el-select
                v-model="tagFormData.tag"
                class="w100"
                multiple
                clearable
                placeholder="请选择标签"
                filterable
                style="width: 100%"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-link type="primary" :underline="false" @click="handleAddNewTag"> 新建标签 </el-link>
          </div>
          <div class="footer-right">
            <el-button @click="handleTagDialogClose">取消</el-button>
            <el-button type="primary" @click="handleConfirmTags" :loading="tagLoading">
              确定
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 新建标签对话框 -->
    <el-dialog v-model="showNewTagDialog" title="新建人才标签" width="450px">
      <el-form ref="newTagFormRef" :model="newTagFormData" :rules="newTagRules">
        <el-form-item label="" prop="tagName">
          <el-input
            v-model="newTagFormData.tagName"
            placeholder="请输入标签名称"
            maxlength="20"
            show-word-limit
            clearable
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseNewTag">取消</el-button>
          <el-button type="primary" @click="handleConfirmNewTag" :loading="newTagLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 国籍分析结果弹窗 -->
    <NationalityAnalysisDialog ref="nationalityAnalysisDialogRef" />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture, Select, PriceTag, Download } from '@element-plus/icons-vue'
import FilterForm from './components/FilterForm.vue'
import Paging from '/@/components/base/paging.vue'
import NationalityAnalysisDialog from './components/NationalityAnalysisDialog.vue'
import { previewTalentData, exportSelectedTalents } from '/@/api/talentExport'
import { resumeAddTag, getResumeTagList, batchAddTag } from '/@/api/person'
import { analyzeTalentNationality } from '/@/api/talentAnalysis'

export default {
  name: 'TalentExport',
  components: {
    FilterForm,
    Paging,
    NationalityAnalysisDialog,
    Picture,
    Select,
    PriceTag,
    Download
  },
  setup() {
    const filterFormRef = ref()
    const tableRef = ref()
    const tagFormRef = ref()
    const newTagFormRef = ref()
    const nationalityAnalysisDialogRef = ref()

    const state = reactive({
      loading: false,
      showPreview: false,
      previewData: [],
      pagination: {
        total: 0,
        page: 1,
        pageSize: 20
      },
      currentFilters: {}, // 保存当前的筛选条件
      selectedTalents: new Set(), // 已选中的人才 ID集合
      talentNameMap: new Map(), // 人才 ID到姓名的映射
      showTagDialog: false,
      showNewTagDialog: false,
      tagLoading: false,
      newTagLoading: false,
      tagList: [] as any[], // 所有标签列表
      tagFormData: {
        tag: [] as string[]
      },
      newTagFormData: {
        tagName: ''
      },
      tagRules: {
        tag: [{ required: false, message: '请选择标签', trigger: 'change' }]
      },
      newTagRules: {
        tagName: [{ required: true, message: '请输入标签名称', trigger: 'blur' }]
      }
    })

    // 处理表格选择变化
    const handleSelectionChange = (selection: any[]) => {
      // 获取当前页的所有数据ID
      const currentPageIds = new Set(state.previewData.map((item: any) => item.id))

      // 先从selectedTalents中移除当前页的所有项
      currentPageIds.forEach((id) => {
        state.selectedTalents.delete(id)
        state.talentNameMap.delete(id)
      })

      // 添加当前页面中被选中的项
      selection.forEach((item: any) => {
        state.selectedTalents.add(item.id)
        state.talentNameMap.set(item.id, item.name)
      })
    }

    // 清空所有选择
    const clearAllSelection = () => {
      state.selectedTalents.clear()
      state.talentNameMap.clear()
      tableRef.value?.clearSelection()
    }

    // 根据 ID 获取人才姓名
    const getTalentNameById = (id: string) => {
      return state.talentNameMap.get(id) || `ID: ${id}`
    }

    // 加载标签列表
    const loadTagList = async () => {
      try {
        const response = await getResumeTagList({})
        state.tagList = response || []
      } catch (error) {}
    }

    // 处理添加标签
    const handleAddTags = async () => {
      if (state.tagFormData.tag.length === 0) {
        ElMessage.warning('请选择要添加的标签')
        return
      }

      if (state.selectedTalents.size === 0) {
        ElMessage.warning('请先选择人才')
        return
      }

      try {
        state.tagLoading = true

        // 批量为人才贴标签
        batchAddTag({
          tagIds: state.tagFormData.tag.join(','),
          resumeIds: Array.from(state.selectedTalents)
            .map((id) => {
              // 根据id找到对应的resumeId
              const item = state.previewData.find((item) => item.id === id)
              return item ? item.resumeId : id
            })
            .join(',')
        }).then((r) => {
          // 成功提示已在公共方法中处理
          // 清理选中的内容
          clearAllSelection()
          // 重新加载数据
          loadPreviewData()
        })

        // 关闭对话框并重置表单
        state.showTagDialog = false
        state.tagFormData.tag = []

        // 刷新数据
        await loadPreviewData()
      } catch (error) {
      } finally {
        state.tagLoading = false
      }
    }

    // 确认贴标签
    const handleConfirmTags = () => {
      tagFormRef.value?.validate((valid: boolean) => {
        if (valid) {
          handleAddTags()
        }
      })
    }

    // 关闭标签对话框
    const handleTagDialogClose = () => {
      state.showTagDialog = false
      state.tagFormData.tag = []
      tagFormRef.value?.resetFields()
    }

    // 打开新建标签对话框
    const handleAddNewTag = () => {
      state.showNewTagDialog = true
      state.newTagFormData.tagName = ''
    }

    // 关闭新建标签对话框
    const handleCloseNewTag = () => {
      state.showNewTagDialog = false
      state.newTagFormData.tagName = ''
      newTagFormRef.value?.resetFields()
    }

    // 确认新建标签
    const handleConfirmNewTag = () => {
      newTagFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
          try {
            state.newTagLoading = true

            await resumeAddTag({
              tag: state.newTagFormData.tagName
            })

            // 关闭新建标签对话框
            handleCloseNewTag()

            // 刷新标签列表
            await loadTagList()
          } catch (error) {
          } finally {
            state.newTagLoading = false
          }
        }
      })
    }

    // 打开标签对话框
    const openTagDialog = async () => {
      if (state.selectedTalents.size === 0) {
        ElMessage.warning('请先选择人才')
        return
      }

      await loadTagList()
      state.showTagDialog = true
    }

    // 导出选中的数据
    const exportSelected = async () => {
      if (state.selectedTalents.size === 0) {
        ElMessage.warning('请先选择要导出的人才')
        return
      }

      try {
        const confirmed = await ElMessageBox.confirm(
          `将导出已选中的 ${state.selectedTalents.size} 个人才数据，是否继续？`,
          '确认导出',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        )

        if (confirmed) {
          const params = {
            talentIds: Array.from(state.selectedTalents),
            ...state.currentFilters
          }

          await exportSelectedTalents(params)
        }
      } catch (error) {
        if (error !== 'cancel') {
        }
      }
    }

    // 获取标签类型（用于不同颜色显示）
    // const getTagType = (index: number) => {
    //   const types = ['primary', 'success', 'warning', 'danger', 'info']
    //   return types[index % types.length]
    // }

    // 处理搜索
    const handleSearch = async (filters: any) => {
      try {
        state.currentFilters = { ...filters }
        state.pagination.page = 1
        await loadPreviewData()
      } finally {
        // 通知FilterForm组件关闭loading
        filterFormRef.value?.setLoading(false)
      }
    }

    // 处理导出
    const handleExport = () => {
      // 导出功能已在FilterForm组件中实现
      // 这里不需要额外处理，因为导出是在筛选组件中直接调用API
    }

    // 处理分页变化
    const handlePageChange = (pageInfo: any) => {
      state.pagination.page = pageInfo.page
      state.pagination.pageSize = pageInfo.limit
      loadPreviewData()
    }

    /**
     * 处理国籍分析按钮点击
     * @param row 当前行数据
     */
    const handleNationalityAnalysis = async (row: any) => {
      if (!row.id) {
        ElMessage.warning('人才ID不存在')
        return
      }

      try {
        // 显示确认对话框
        await ElMessageBox.confirm(
          'AI国籍分析需要较长时间处理，分析结果仅供参考，不作为最终判断依据。是否继续？',
          '外籍人才分析',
          {
            confirmButtonText: '确定分析',
            cancelButtonText: '取消',
            type: 'info',
            dangerouslyUseHTMLString: false,
            closeOnClickModal: false
          }
        )

        // 设置当前行的分析状态为加载中
        row.analyzing = true

        // 调用国籍分析API
        const response = await analyzeTalentNationality(row.resumeId)

        // 显示分析结果弹窗
        nationalityAnalysisDialogRef.value?.open(response)
      } catch (error) {
        if (error === 'cancel') {
          // 用户取消操作，不显示错误消息
          return
        }
      } finally {
        // 重置当前行的分析状态
        row.analyzing = false
      }
    }

    // 加载预览数据
    const loadPreviewData = async () => {
      try {
        state.loading = true
        const params = {
          ...state.currentFilters,
          page: state.pagination.page,
          pageSize: state.pagination.pageSize
        }

        const response = await previewTalentData(params)
        state.previewData = response.list || []
        state.pagination.total = response.pages?.total || 0
        state.showPreview = true
      } catch (error) {
      } finally {
        state.loading = false
      }
    }

    return {
      filterFormRef,
      tableRef,
      tagFormRef,
      newTagFormRef,
      nationalityAnalysisDialogRef,
      handleSelectionChange,
      clearAllSelection,
      getTalentNameById,
      loadTagList,
      openTagDialog,
      handleAddTags,
      handleConfirmTags,
      handleTagDialogClose,
      handleAddNewTag,
      handleCloseNewTag,
      handleConfirmNewTag,
      exportSelected,
      handleSearch,
      handleExport,
      handlePageChange,
      handleNationalityAnalysis,
      ...toRefs(state)
    }
  }
}
</script>

<style lang="scss" scoped>
.talent-export-container {
  padding: 20px;

  .preview-section {
    margin-top: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.12),
      0 0 6px rgba(0, 0, 0, 0.04);
    padding: 20px;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;

      h3 {
        margin: 0;
        color: #303133;
        font-size: 18px;
        font-weight: 600;
      }

      .total-count {
        color: #909399;
        font-size: 14px;
        background: #f5f7fa;
        padding: 5px 12px;
        border-radius: 4px;
      }
    }

    .talent-tags {
      max-width: 180px;
      word-break: break-all;
      line-height: 1.4;
      font-size: 13px;
    }

    .avatar-preview {
      display: flex;
      justify-content: center;
      align-items: center;

      .avatar-thumbnail {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        border: 2px solid #e4e7ed;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          transform: scale(1.1);
        }
      }

      .image-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: #f5f7fa;
        border-radius: 50%;
        color: #c0c4cc;
        font-size: 10px;

        .el-icon {
          font-size: 16px;
          margin-bottom: 2px;
        }
      }

      .no-avatar {
        color: #909399;
        font-size: 12px;
      }
    }

    .no-data {
      color: #c0c4cc;
      font-style: italic;
      font-size: 12px;
    }

    :deep(.el-table) {
      .el-table__header {
        th {
          background-color: #fafafa;
          color: #606266;
          font-weight: 600;
        }
      }

      .el-table__body {
        tr:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }

  // 空状态样式
  .empty-state {
    text-align: center;
    padding: 60px 0;
    color: #909399;

    .empty-icon {
      font-size: 64px;
      margin-bottom: 20px;
    }

    .empty-text {
      font-size: 16px;
      margin-bottom: 10px;
    }

    .empty-description {
      font-size: 14px;
      color: #c0c4cc;
    }
  }

  // 响应式布局
  @media (max-width: 768px) {
    padding: 10px;

    .preview-section {
      padding: 15px;

      .preview-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      :deep(.el-table) {
        font-size: 12px;

        // 操作列在小屏幕下的样式调整
        .el-table__fixed-right {
          .el-button {
            padding: 4px 8px;
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 批量操作栏样式
.batch-operations {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 20px;

  .selected-info {
    display: flex;
    align-items: center;
    color: #606266;
    font-weight: 500;

    .selected-icon {
      margin-right: 8px;
      font-size: 16px;
    }

    .el-button {
      margin-left: 15px;
    }
  }

  .batch-actions {
    display: flex;
    gap: 10px;
  }
}

// 标签管理对话框样式
.tag-management {
  .selected-talents-info {
    .info-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
      font-weight: 500;
    }

    .talent-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 10px;

      .talent-tag {
        border-radius: 12px;
      }

      .more-count {
        color: #909399;
        font-size: 12px;
        line-height: 24px;
      }
    }
  }

  .tag-form-section {
    .w100 {
      width: 100%;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .footer-left {
    .el-link {
      font-size: 12px;
    }
  }

  .footer-right {
    display: flex;
    gap: 10px;
  }
}
</style>
