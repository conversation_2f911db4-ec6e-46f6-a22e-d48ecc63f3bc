<template>
  <el-dialog
    v-model="visible"
    title="人才国籍分析结果"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="analysisData" class="analysis-content">
      <!-- 基本信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">基本信息</span>
          </div>
        </template>
        <div class="talent-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">简历ID：</span>
              <span class="value">{{ analysisData.resumeId }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 分析结果卡片 -->
      <el-card class="result-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">分析结果</span>
          </div>
        </template>
        <div class="analysis-result">
          <div class="result-main">
            <div class="result-status">
              <el-tag
                :type="analysisData.isForeign ? 'danger' : 'success'"
                size="large"
                class="status-tag"
              >
                {{ analysisData.isForeign ? '外籍人才' : '本国人才' }}
              </el-tag>
            </div>
            <div class="confidence-info">
              <div class="confidence-label">置信度：</div>
              <div class="confidence-bar">
                <el-progress
                  :percentage="Math.round(analysisData.confidence * 100)"
                  :color="getConfidenceColor(analysisData.confidence)"
                  :stroke-width="8"
                />
              </div>
              <div class="confidence-level">
                <el-tag :type="getConfidenceLevelType(analysisData.confidence)" size="small">
                  {{ getConfidenceLevel(analysisData.confidence) }}置信度
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 国家和地区信息 -->
          <div v-if="analysisData.country || analysisData.region" class="country-info">
            <div v-if="analysisData.country" class="info-item">
              <span class="label">推测国家：</span>
              <span class="value">{{ analysisData.country }}</span>
            </div>
            <div v-if="analysisData.region" class="info-item">
              <span class="label">推测地区：</span>
              <span class="value">{{ analysisData.region }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 分析依据卡片 -->
      <el-card class="reasons-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">分析依据</span>
          </div>
        </template>
        <div class="analysis-reasons">
          <ul class="reasons-list">
            <li v-for="(reason, index) in analysisData.reasons" :key="index" class="reason-item">
              <el-icon class="reason-icon"><Check /></el-icon>
              <span>{{ reason }}</span>
            </li>
          </ul>
        </div>
      </el-card>

      <!-- 详细信息卡片 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">详细信息</span>
          </div>
        </template>
        <div class="detail-info">
          <!-- 详细分析 -->
          <div class="detail-section">
            <h4 class="section-title">详细分析</h4>
            <div class="detailed-analysis">
              <div v-html="formatDetailedAnalysis(analysisData.detailedAnalysis)"></div>
            </div>
          </div>

          <!-- 风险因素 -->
          <div
            class="detail-section"
            v-if="analysisData.riskFactors && analysisData.riskFactors.length > 0"
          >
            <h4 class="section-title">风险因素</h4>
            <div class="risk-factors">
              <ul class="risk-list">
                <li
                  v-for="(risk, index) in analysisData.riskFactors"
                  :key="index"
                  class="risk-item"
                >
                  <el-icon class="risk-icon"><Warning /></el-icon>
                  <span>{{ risk }}</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- AI原始响应（仅开发环境显示） -->
          <div class="detail-section" v-if="showLLMResponse">
            <h4 class="section-title">
              AI原始响应
              <el-button size="small" text @click="toggleLLMResponse">
                {{ showLLMResponseContent ? '隐藏' : '显示' }}
              </el-button>
            </h4>
            <div v-if="showLLMResponseContent" class="llm-response">
              <el-input
                v-model="analysisData.llmResponse"
                type="textarea"
                :rows="8"
                readonly
                class="response-textarea"
              />
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { reactive, toRefs, computed } from 'vue'
import { Check, Warning } from '@element-plus/icons-vue'

export default {
  name: 'NationalityAnalysisDialog',
  components: {
    Check,
    Warning
  },
  setup() {
    const state = reactive({
      visible: false,
      analysisData: null as any,
      showLLMResponseContent: false
    })

    // 是否显示LLM响应（仅在开发环境或有调试权限时显示）
    const showLLMResponse = computed(() => {
      return (
        process.env.NODE_ENV === 'development' ||
        (state.analysisData && state.analysisData.llmResponse)
      )
    })

    /**
     * 打开弹窗
     * @param data 分析结果数据
     */
    const open = (data: any) => {
      state.analysisData = data
      state.visible = true
      state.showLLMResponseContent = false
    }

    /**
     * 关闭弹窗
     */
    const handleClose = () => {
      state.visible = false
      state.analysisData = null
      state.showLLMResponseContent = false
    }

    /**
     * 切换LLM响应显示状态
     */
    const toggleLLMResponse = () => {
      state.showLLMResponseContent = !state.showLLMResponseContent
    }

    /**
     * 获取置信度等级
     * @param confidence 置信度数值
     */
    const getConfidenceLevel = (confidence: number) => {
      if (confidence >= 0.7) return '高'
      if (confidence >= 0.4) return '中'
      return '低'
    }

    /**
     * 获取置信度颜色
     * @param confidence 置信度数值
     */
    const getConfidenceColor = (confidence: number) => {
      if (confidence >= 0.7) return '#67c23a'
      if (confidence >= 0.4) return '#e6a23c'
      return '#f56c6c'
    }

    /**
     * 获取置信度等级类型
     * @param confidence 置信度数值
     */
    const getConfidenceLevelType = (confidence: number) => {
      if (confidence >= 0.7) return 'success'
      if (confidence >= 0.4) return 'warning'
      return 'danger'
    }

    /**
     * 格式化详细分析文本
     * @param text 原始文本
     */
    const formatDetailedAnalysis = (text: string) => {
      if (!text) return ''

      return (
        text
          // 处理换行符
          .replace(/\n/g, '<br>')
          // 处理 Markdown 加粗语法 **text**
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          // 处理数字序号，添加更好的样式
          .replace(/^(\d+\.\s)/gm, '<span class="number-point">$1</span>')
          // 处理单引号包围的内容，添加高亮
          .replace(/'([^']+)'/g, '<span class="highlight-text">\'$1\'</span>')
      )
    }

    return {
      open,
      handleClose,
      toggleLLMResponse,
      formatDetailedAnalysis,
      getConfidenceLevel,
      getConfidenceColor,
      getConfidenceLevelType,
      showLLMResponse,
      ...toRefs(state)
    }
  }
}
</script>

<style lang="scss" scoped>
.analysis-content {
  .info-card,
  .result-card,
  .reasons-card,
  .detail-card {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-header {
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  // 基本信息样式
  .talent-info {
    .info-row {
      display: flex;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .info-item {
      flex: 1;
      display: flex;
      align-items: center;

      .label {
        color: #606266;
        margin-right: 8px;
        min-width: 60px;
      }

      .value {
        color: #303133;
        font-weight: 500;
      }
    }
  }

  // 分析结果样式
  .analysis-result {
    .result-main {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      gap: 20px;
    }

    .result-status {
      .status-tag {
        font-size: 16px;
        padding: 8px 16px;
        font-weight: 600;
      }
    }

    .confidence-info {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 15px;

      .confidence-label {
        color: #606266;
        font-weight: 500;
        min-width: 60px;
      }

      .confidence-bar {
        flex: 1;
        max-width: 200px;
      }

      .confidence-level {
        min-width: 80px;
      }
    }

    .country-info {
      margin-top: 15px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 6px;
      display: flex;
      gap: 30px;
      flex-wrap: wrap;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          color: #606266;
          margin-right: 8px;
          min-width: 80px;
        }

        .value {
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }

  // 分析依据样式
  .analysis-reasons {
    .reasons-list {
      list-style: none;
      padding: 0;
      margin: 0;

      .reason-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px 0;

        &:last-child {
          margin-bottom: 0;
        }

        .reason-icon {
          color: #67c23a;
          margin-right: 8px;
          font-size: 16px;
        }
      }
    }
  }

  // 详细信息样式
  .detail-info {
    .detail-section {
      margin-bottom: 25px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .no-data {
        color: #909399;
        font-style: italic;
      }
    }

    // 详细分析样式
    .detailed-analysis {
      line-height: 1.8;
      color: #303133;

      :deep(br) {
        margin-bottom: 8px;
      }

      :deep(strong) {
        color: #409eff;
        font-weight: 600;
      }

      :deep(.number-point) {
        color: #e6a23c;
        font-weight: 600;
        margin-right: 4px;
      }

      :deep(.highlight-text) {
        background-color: #f0f9ff;
        color: #1976d2;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 13px;
      }

      // 段落间距
      :deep(br + br) {
        margin-bottom: 12px;
      }
    }

    // 风险因素样式
    .risk-factors {
      .risk-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .risk-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          padding: 8px 0;

          &:last-child {
            margin-bottom: 0;
          }

          .risk-icon {
            color: #e6a23c;
            margin-right: 8px;
            font-size: 16px;
          }
        }
      }
    }

    // LLM响应样式
    .llm-response {
      margin-top: 10px;

      .response-textarea {
        .el-textarea__inner {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .analysis-content {
    .talent-info {
      .info-row {
        flex-direction: column;
        gap: 10px;
      }
    }

    .analysis-result {
      .result-main {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
      }

      .confidence-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        width: 100%;

        .confidence-bar {
          max-width: 100%;
        }
      }

      .country-info {
        flex-direction: column;
        gap: 15px;

        .info-item {
          flex-direction: column;
          align-items: flex-start;

          .label {
            min-width: auto;
          }
        }
      }
    }

    .detail-info {
      .section-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }
  }
}
</style>
