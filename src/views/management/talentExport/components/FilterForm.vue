<template>
  <el-card class="filter-card">
    <template #header>
      <div class="card-header">
        <span>人才筛选条件</span>
      </div>
    </template>

    <el-form ref="formRef" :model="formData" label-width="120px" class="filter-form">
      <!-- 第一行：活跃时间范围 + 姓名筛选条件 -->
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="活跃时间范围" prop="dateRange">
            <div class="date-range-container">
              <div class="date-range-shortcuts">
                <el-button size="small" @click="setTimeRange('week')" type="primary" plain>
                  近一周
                </el-button>
                <el-button size="small" @click="setTimeRange('month')" type="primary" plain>
                  近一个月
                </el-button>
                <el-button size="small" @click="setTimeRange('quarter')" type="primary" plain>
                  近三个月
                </el-button>
                <el-button size="small" @click="setTimeRange('year')" type="primary" plain>
                  近一年
                </el-button>
              </div>
              <DatePickerRange
                v-model:start="formData.startDate"
                v-model:end="formData.endDate"
                placeholder="请选择活跃时间范围"
                style="width: 100%"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名筛选条件" prop="nameConditions">
            <el-checkbox-group v-model="formData.nameConditions" class="name-conditions" disabled>
              <el-checkbox :label="1" name="hasEnglish" disabled>包含英文</el-checkbox>
              <el-checkbox :label="2" name="chineseMoreThan4" disabled>中文>4字</el-checkbox>
              <el-checkbox :label="3" name="hasMultipleNumbers" disabled>含≥2数字</el-checkbox>
              <el-checkbox :label="4" name="hasSpecialKeywords" disabled>特定关键词</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：特殊关键词输入 -->
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="特殊关键词" prop="specialKeywords">
            <div class="keywords-input-container">
              <!-- 已选择的关键词标签 -->
              <div class="selected-keywords" v-if="formData.specialKeywords.length > 0">
                <div class="keywords-header">
                  <div class="keywords-title-section">
                    <span class="keywords-title">已选择关键词：</span>
                    <span class="keywords-count">{{ formData.specialKeywords.length }} 个</span>
                  </div>
                  <div class="keywords-actions">
                    <el-button size="small" @click="selectAllKeywords" type="primary" plain>
                      全选
                    </el-button>
                    <el-button size="small" @click="clearAllKeywords" type="danger" plain>
                      清空
                    </el-button>
                  </div>
                </div>
                <div class="keywords-list">
                  <el-tag
                    v-for="keyword in formData.specialKeywords"
                    :key="keyword"
                    closable
                    @close="removeKeyword(keyword)"
                    class="keyword-tag"
                  >
                    {{ keyword }}
                  </el-tag>
                </div>
              </div>

              <!-- 关键词输入框 -->
              <div class="keyword-input-section">
                <el-row :gutter="12">
                  <el-col :span="14">
                    <el-input
                      v-model="keywordInput"
                      placeholder="输入关键词后按回车或Tab添加"
                      @keydown.enter.prevent="addKeyword"
                      @keydown.tab.prevent="addKeyword"
                    >
                      <template #append>
                        <el-button
                          @click="addKeyword"
                          :disabled="!keywordInput.trim()"
                          type="primary"
                          size="small"
                        >
                          添加
                        </el-button>
                      </template>
                    </el-input>
                  </el-col>
                  <el-col :span="10">
                    <el-select
                      v-model="selectedPresetKeyword"
                      placeholder="选择预设关键词"
                      @change="addPresetKeyword"
                      clearable
                      style="width: 100%"
                    >
                      <el-option
                        v-for="keyword in availablePresetKeywords"
                        :key="keyword"
                        :label="keyword"
                        :value="keyword"
                      />
                    </el-select>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：户籍国籍选择和标签筛选 -->
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="户籍/国籍" prop="targetCountries">
            <div class="countries-select-container">
              <div class="countries-header">
                <div class="countries-actions">
                  <el-button size="small" @click="selectAllCountries" type="primary" plain>
                    全选
                  </el-button>
                  <el-button size="small" @click="clearAllCountries" type="danger" plain>
                    清空
                  </el-button>
                </div>
                <div class="countries-count">
                  <span class="count-text">已选择 {{ formData.targetCountries.length }} 个</span>
                </div>
              </div>
              <el-select
                v-model="formData.targetCountries"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择目标国家/地区"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="country in targetCountriesList"
                  :key="country.k"
                  :label="country.v"
                  :value="country.k"
                />
              </el-select>
            </div>
          </el-form-item>
        </el-col>

        <!-- 包含标签 -->
        <el-col :span="8">
          <el-form-item label="包含标签">
            <div class="form-item-content">
              <el-select
                v-model="formData.includeTags"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择包含的标签"
                filterable
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="tag in tagList"
                  :key="tag.value"
                  :label="tag.label"
                  :value="tag.value"
                />
              </el-select>
            </div>
          </el-form-item>
        </el-col>

        <!-- 剔除标签 -->
        <el-col :span="8">
          <el-form-item label="剔除标签">
            <div class="form-item-content">
              <el-select
                v-model="formData.excludeTags"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择要剔除的标签"
                filterable
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="tag in tagList"
                  :key="tag.value"
                  :label="tag.label"
                  :value="tag.value"
                />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 操作按钮 -->
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label-width="0px">
            <div class="button-group">
              <el-button type="primary" @click="handleSearch" :loading="loading"> 查询 </el-button>
              <el-button type="success" @click="handleExport" :loading="exportLoading">
                导出
              </el-button>
              <el-button @click="handleReset"> 重置 </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script lang="ts">
import { reactive, toRefs, ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download, RefreshRight } from '@element-plus/icons-vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import { getFilterConfig, exportTalentData } from '/@/api/talentExport'
import { getResumeTagList } from '/@/api/person'

export default {
  name: 'FilterForm',
  components: {
    DatePickerRange,
    Search,
    Download,
    RefreshRight
  },
  emits: ['search', 'export'],
  setup(_props, { emit }) {
    const formRef = ref()

    const state = reactive({
      loading: false,
      exportLoading: false,
      formData: {
        startDate: '',
        endDate: '',
        nameConditions: [] as number[], // 姓名筛选条件数组
        specialKeywords: [] as string[], // 特殊关键词数组
        targetCountries: [] as number[], // 目标国家数组
        includeTags: [] as number[], // 包含标签数组
        excludeTags: [] as number[] // 剔除标签数组
      },
      specialKeywordsList: [] as string[], // 特殊关键词列表（预设）
      targetCountriesList: [] as { k: number; v: string }[], // 目标国家列表
      tagList: [] as { value: number; label: string }[], // 标签列表
      keywordInput: '', // 关键词输入框的值
      selectedPresetKeyword: '' // 选中的预设关键词
    })

    // 初始化配置数据
    const initConfig = async () => {
      try {
        const config = await getFilterConfig()
        console.log('获取到的配置数据:', config)

        // 设置特殊关键词列表
        state.specialKeywordsList = config.specialKeywords || []
        console.log('特殊关键词列表:', state.specialKeywordsList)

        // 设置目标国家列表
        state.targetCountriesList = config.targetCountries || []
        console.log('目标国家列表:', state.targetCountriesList)

        // 获取标签列表
        const tagResponse = await getResumeTagList({})
        state.tagList = tagResponse.map((tag: any) => ({
          value: tag.value,
          label: tag.label
        }))
        console.log('标签列表:', state.tagList)

        // 设置默认时间范围为最近一周
        setTimeRange('week')

        // 默认选择所有姓名筛选条件
        state.formData.nameConditions = [1, 2, 3, 4]

        // 默认选择所有特殊关键词
        state.formData.specialKeywords = [...state.specialKeywordsList]

        // 默认选择所有国家/地区
        state.formData.targetCountries = state.targetCountriesList.map((country) => country.k)
      } catch (error) {
        console.error('获取配置失败:', error)
        ElMessage.error('获取配置失败，请刷新页面重试')

        // 如果API调用失败，使用默认数据
        initDefaultData()
      }
    }

    // 初始化默认数据（当API调用失败时使用）
    const initDefaultData = () => {
      // 设置默认特殊关键词
      state.specialKeywordsList = [
        '阿里',
        '穆罕默德',
        '穆罕',
        '穆德',
        '法蒂玛',
        '哈桑',
        '侯赛因',
        '阿卜杜拉',
        '易卜拉欣',
        '奥马尔',
        '阿米尔',
        '祖拜达',
        '赛义德',
        '拉吉',
        '普里亚',
        '迪帕克',
        '夏克提',
        '萨米尔',
        '卡马尔',
        '纳西尔',
        '莎米拉',
        '贾韦德',
        '阿米娜',
        '奥卢塞贡',
        '西塞',
        '卡伦',
        '马赫迪',
        '莱拉',
        '优素福',
        '法鲁克',
        '莎菲卡',
        '易卜拉希马',
        '阿莎',
        '贾迈勒',
        '祖玛',
        '哈迪娅',
        '穆萨'
      ]

      // 设置默认目标国家（使用模拟数据）
      state.targetCountriesList = [
        { k: 3871, v: '越南' },
        { k: 3872, v: '老挝' },
        { k: 3873, v: '柬埔寨' },
        { k: 3874, v: '缅甸' },
        { k: 3875, v: '泰国' },
        { k: 3876, v: '马来西亚' },
        { k: 3877, v: '新加坡' },
        { k: 3878, v: '印度尼西亚' },
        { k: 3879, v: '菲律宾' },
        { k: 3880, v: '文莱' },
        { k: 3881, v: '东帝汶' },
        { k: 3882, v: '印度' },
        { k: 3883, v: '巴基斯坦' },
        { k: 3884, v: '斯里兰卡' },
        { k: 3885, v: '尼泊尔' },
        { k: 3886, v: '孟加拉国' },
        { k: 3887, v: '不丹' },
        { k: 3888, v: '马尔代夫' },
        { k: 3889, v: '土耳其' },
        { k: 3890, v: '格鲁吉亚' },
        { k: 3891, v: '阿塞拜疆' },
        { k: 3892, v: '亚美尼亚' },
        { k: 3893, v: '塞浦路斯' },
        { k: 3894, v: '黎巴嫩' },
        { k: 3895, v: '巴勒斯坦' },
        { k: 3896, v: '以色列' },
        { k: 3897, v: '叙利亚' },
        { k: 3898, v: '伊拉克' },
        { k: 3899, v: '伊朗' },
        { k: 3900, v: '阿富汗' },
        { k: 3901, v: '约旦' },
        { k: 3902, v: '沙特阿拉伯' },
        { k: 3903, v: '也门' },
        { k: 3904, v: '阿曼' },
        { k: 3905, v: '科威特' },
        { k: 3906, v: '巴林' },
        { k: 3907, v: '卡塔尔' },
        { k: 3908, v: '阿拉伯联合酋长国' },
        { k: 3909, v: '哈萨克斯坦' },
        { k: 3910, v: '吉尔吉斯斯坦' },
        { k: 3911, v: '塔吉克斯坦' },
        { k: 3912, v: '乌兹别克斯坦' },
        { k: 3913, v: '土库曼斯坦' }
      ]

      console.log('使用默认数据初始化完成')
    }

    // 计算可用的预设关键词（排除已选择的）
    const availablePresetKeywords = computed(() => {
      return state.specialKeywordsList.filter(
        (keyword) => !state.formData.specialKeywords.includes(keyword)
      )
    })

    // 添加关键词
    const addKeyword = () => {
      const keyword = state.keywordInput.trim()
      if (keyword && !state.formData.specialKeywords.includes(keyword)) {
        state.formData.specialKeywords.push(keyword)
        state.keywordInput = ''
      }
    }

    // 添加预设关键词
    const addPresetKeyword = (keyword: string) => {
      if (keyword && !state.formData.specialKeywords.includes(keyword)) {
        state.formData.specialKeywords.push(keyword)
      }
      state.selectedPresetKeyword = ''
    }

    // 移除关键词
    const removeKeyword = (keyword: string) => {
      const index = state.formData.specialKeywords.indexOf(keyword)
      if (index > -1) {
        state.formData.specialKeywords.splice(index, 1)
      }
    }

    // 全选关键词
    const selectAllKeywords = () => {
      state.formData.specialKeywords = [...state.specialKeywordsList]
    }

    // 清空关键词
    const clearAllKeywords = () => {
      state.formData.specialKeywords = []
    }

    // 设置时间范围
    const setTimeRange = (type: string) => {
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

      let startDate: Date

      switch (type) {
        case 'week':
          startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case 'quarter':
          startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case 'year':
          startDate = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = today
      }

      // 格式化为 YYYY-MM-DD
      const formatDate = (date: Date) => {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      }

      state.formData.startDate = formatDate(startDate)
      state.formData.endDate = formatDate(today)
    }

    // 全选国家
    const selectAllCountries = () => {
      state.formData.targetCountries = state.targetCountriesList.map((country) => country.k)
    }

    // 清空国家选择
    const clearAllCountries = () => {
      state.formData.targetCountries = []
    }

    // 处理搜索
    const handleSearch = () => {
      // 验证必填项
      if (!state.formData.startDate || !state.formData.endDate) {
        ElMessage.warning('请选择活跃时间范围')
        return
      }

      // 验证筛选条件 - 户籍/国籍必须至少选一个
      const hasCountryCondition = state.formData.targetCountries.length > 0

      if (!hasCountryCondition) {
        ElMessage.warning('请至少选择一个户籍/国籍')
        return
      }

      // 特殊关键词必须至少选一个
      if (state.formData.specialKeywords.length === 0) {
        ElMessage.warning('请至少选择一个特殊关键词')
        return
      }

      state.loading = true
      // 构建筛选参数
      const filters = buildFilterParams()
      emit('search', filters)
    }

    // 处理导出
    const handleExport = async () => {
      // 验证必填项
      if (!state.formData.startDate || !state.formData.endDate) {
        ElMessage.warning('请选择活跃时间范围')
        return
      }

      // 验证筛选条件 - 户籍/国籍必须至少选一个
      const hasCountryCondition = state.formData.targetCountries.length > 0

      if (!hasCountryCondition) {
        ElMessage.warning('请至少选择一个户籍/国籍')
        return
      }

      // 特殊关键词必须至少选一个
      if (state.formData.specialKeywords.length === 0) {
        ElMessage.warning('请至少选择一个特殊关键词')
        return
      }

      try {
        state.exportLoading = true
        const params = buildFilterParams()
        await exportTalentData(params)
        ElMessage.success('数据开始导出，成功下载后会在企业微信通知，请后续留意')
      } catch (error) {
        console.error('导出失败:', error)
        ElMessage.error('导出失败，请重试')
      } finally {
        state.exportLoading = false
      }
    }

    // 构建筛选参数
    const buildFilterParams = () => {
      const params: any = {
        startDate: state.formData.startDate,
        endDate: state.formData.endDate
      }

      // 处理姓名筛选条件
      if (state.formData.nameConditions.includes(1)) {
        params.hasEnglish = 1
      }
      if (state.formData.nameConditions.includes(2)) {
        params.chineseMoreThan4 = 1
      }
      if (state.formData.nameConditions.includes(3)) {
        params.hasMultipleNumbers = 1
      }

      // 处理特殊关键词
      if (state.formData.nameConditions.includes(4) && state.formData.specialKeywords.length > 0) {
        params.specialKeywords = state.formData.specialKeywords.join(',')
      }

      // 处理目标国家
      if (state.formData.targetCountries.length > 0) {
        params.targetCountries = state.formData.targetCountries.join(',')
      }

      // 处理包含标签
      if (state.formData.includeTags.length > 0) {
        params.includeTags = state.formData.includeTags.join(',')
      }

      // 处理剔除标签
      if (state.formData.excludeTags.length > 0) {
        params.excludeTags = state.formData.excludeTags.join(',')
      }

      return params
    }

    // 重置表单
    const handleReset = () => {
      formRef.value?.resetFields()
      state.formData = {
        startDate: '',
        endDate: '',
        nameConditions: [] as number[],
        specialKeywords: [] as string[],
        targetCountries: [] as number[],
        includeTags: [] as number[],
        excludeTags: [] as number[]
      }
      // 清空输入框
      state.keywordInput = ''
      state.selectedPresetKeyword = ''
      // 重新初始化配置数据
      initConfig()
    }

    onMounted(() => {
      initConfig()
    })

    // 设置loading状态（供父组件调用）
    const setLoading = (loading: boolean) => {
      state.loading = loading
    }

    return {
      formRef,
      setLoading,
      handleSearch,
      handleExport,
      handleReset,
      addKeyword,
      addPresetKeyword,
      removeKeyword,
      selectAllKeywords,
      clearAllKeywords,
      setTimeRange,
      selectAllCountries,
      clearAllCountries,
      availablePresetKeywords,
      ...toRefs(state)
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-card {
  margin-bottom: 20px;

  .card-header {
    font-weight: bold;
    color: #303133;
    font-size: 16px;
  }

  .button-group {
    .el-button {
      margin-right: 10px;
      min-width: 100px;
    }
  }

  :deep(.el-form) {
    .el-form-item {
      margin-bottom: 20px;
    }

    .name-conditions {
      display: flex;
      flex-wrap: wrap;
      gap: 8px 16px;

      .el-checkbox {
        margin: 0;

        .el-checkbox__input {
          .el-checkbox__inner {
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            width: 16px;
            height: 16px;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
            }

            &::after {
              border-width: 2px;
              width: 4px;
              height: 8px;
            }
          }
        }

        .el-checkbox__label {
          font-size: 14px;
          color: #606266;
          font-weight: 400;
          padding-left: 8px;
          line-height: 1.5;
        }

        &.is-checked {
          .el-checkbox__input {
            .el-checkbox__inner {
              background-color: #409eff;
              border-color: #409eff;
            }
          }

          .el-checkbox__label {
            color: #409eff;
            font-weight: 500;
          }
        }

        &.is-disabled {
          .el-checkbox__input {
            .el-checkbox__inner {
              background-color: #f5f7fa;
              border-color: #e4e7ed;
              cursor: not-allowed;
            }

            &.is-checked {
              .el-checkbox__inner {
                background-color: #c0c4cc;
                border-color: #c0c4cc;

                &::after {
                  border-color: #ffffff;
                }
              }
            }
          }

          .el-checkbox__label {
            color: #c0c4cc;
            cursor: not-allowed;
          }
        }
      }
    }

    .el-select {
      .el-input__wrapper {
        min-height: 32px;
      }
    }
  }

  // 时间范围容器样式
  .date-range-container {
    width: 100%;

    .date-range-shortcuts {
      display: flex;
      gap: 8px;
      margin-bottom: 8px;
      flex-wrap: wrap;

      .el-button {
        font-size: 12px;
        padding: 4px 8px;
        height: 24px;

        &.el-button--small {
          &.is-plain {
            &.el-button--primary {
              border-color: #409eff;
              color: #409eff;

              &:hover {
                background: #409eff;
                color: #ffffff;
              }
            }
          }
        }
      }
    }
  }

  // 关键词输入容器样式
  .keywords-input-container {
    width: 100%;

    .selected-keywords {
      margin-bottom: 10px;

      .keywords-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .keywords-title-section {
          display: flex;
          align-items: center;
          gap: 8px;

          .keywords-title {
            font-size: 12px;
            color: #909399;
          }

          .keywords-count {
            font-size: 12px;
            color: #409eff;
          }
        }

        .keywords-actions {
          display: flex;
          gap: 8px;

          .el-button {
            font-size: 12px;
            padding: 4px 8px;
            height: 24px;

            &.el-button--small {
              &.is-plain {
                &.el-button--primary {
                  border-color: #409eff;
                  color: #409eff;

                  &:hover {
                    background: #409eff;
                    color: #ffffff;
                  }
                }

                &.el-button--danger {
                  border-color: #f56c6c;
                  color: #f56c6c;

                  &:hover {
                    background: #f56c6c;
                    color: #ffffff;
                  }
                }
              }
            }
          }
        }
      }

      .keywords-list {
        .keyword-tag {
          margin: 3px 5px 3px 0;
          font-size: 12px;
        }
      }
    }

    .keyword-input-section {
      .el-row {
        .el-col {
          &:first-child {
            .el-input {
              .el-input-group__append {
                .el-button {
                  &.el-button--small {
                    padding: 0 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 国家选择容器样式
  .countries-select-container {
    width: 100%;

    .countries-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .countries-actions {
        display: flex;
        gap: 10px;
      }

      .countries-count {
        .count-text {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  // 响应式布局
  @media (max-width: 768px) {
    :deep(.el-row) {
      .el-col {
        width: 100% !important;
        max-width: 100% !important;
      }
    }

    .date-range-container {
      .date-range-shortcuts {
        justify-content: flex-start;

        .el-button {
          margin-bottom: 4px;
        }
      }
    }

    .keywords-input-container {
      .keyword-input-section {
        .el-row {
          .el-col {
            margin-bottom: 10px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    .countries-select-container {
      .countries-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }

    .button-group {
      .el-button {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
