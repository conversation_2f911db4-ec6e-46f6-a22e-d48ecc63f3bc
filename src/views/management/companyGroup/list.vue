<template>
  <div class="container">
    <el-form label-width="80px" ref="formRef" :model="formData">
      <div class="flex">
        <div class="span-3">
          <el-form-item label="群组查询" prop="keyword">
            <el-input placeholder="请输入群组id或名称" clearable v-model="formData.keyword" />
          </el-form-item>
        </div>

        <div class="span-3">
          <el-form-item label="创建时间" prop="addTimeStart">
            <DatePickerRange
              v-model:start="formData.addTimeStart"
              v-model:end="formData.addTimeEnd"
            />
          </el-form-item>
        </div>

        <div class="span-3">
          <el-form-item>
            <el-button type="primary" @click="getList">查询</el-button>
            <el-button @click="handleRestForm">重置</el-button>
            <el-button @click="handleDownload">下载</el-button>
            <el-button type="primary" @click="handleOpenDialog">创建群组</el-button>
          </el-form-item>
        </div>
      </div>
    </el-form>

    <el-table border class="mt-20" :data="tableData" v-loading="loading">
      <el-table-column prop="id" label="群组ID" />
      <el-table-column prop="groupName" label="群组名称" />
      <el-table-column prop="sortNumber" label="排序" />
      <el-table-column prop="companyNumber" label="数量">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="toCompanyList(row)">{{
            row.companyNumber
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="addTime" label="创建时间" />
      <el-table-column prop="name" label="创建人员" />

      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row.id)">编辑</el-button>
          <el-button
            link
            type="primary"
            :disabled="row.companyNumber !== '0'"
            @click="handleDelete(row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div class="mt-20">
      <Paging :total="totalCount" @change="handlePageChange" />
    </div>

    <groupDialog v-model="groupDialogVisible" @update="getList" :id="id" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import { ElMessageBox } from 'element-plus'
import groupDialog from './components/groupDialog.vue'
import { deleteGroup, exportCompanyGroup, getCompanyGroupList } from '/@/api/companyGroup'
import Paging from '/@/components/base/paging.vue'
import router from '/@/router'

const tableData = ref([])
const formRef = ref()
const formData = ref({
  keyword: '',
  addTimeStart: '',
  addTimeEnd: '',
  page: 1,
  pageSize: 20
})

const id = ref('')

const loading = ref(false)

const groupDialogVisible = ref(false)

const totalCount = ref(0)

// 接受路由参数

const getList = async () => {
  loading.value = true
  const { list, total } = await getCompanyGroupList(formData.value)
  tableData.value = list
  totalCount.value = total
  loading.value = false
}

const handleRestForm = () => {
  formRef.value.resetFields()
  getList()
}

const handleOpenDialog = () => {
  groupDialogVisible.value = true
  id.value = ''
}

const handleDownload = async () => {
  const { keyword, addTimeStart, addTimeEnd } = formData.value
  const postData = { keyword, addTimeStart, addTimeEnd }
  await exportCompanyGroup(postData)
}

const handleEdit = (editId: string) => {
  handleOpenDialog()
  id.value = editId
}

const handleDelete = (groupId: string) => {
  ElMessageBox.confirm('删除后将不可恢复，确认要删除该群组吗？', '删除群组')
    .then(async () => {
      await deleteGroup({ id: groupId })
      getList()
    })
    .catch(() => {})
}

const handlePageChange = (data: { page: number; limit: number }) => {
  formData.value.page = data.page
  formData.value.pageSize = data.limit
  getList()
}

// 去到某个路由 company/query?groupId=xxx
const toCompanyList = (row: any) => {
  router.push({
    path: '/company/query',
    query: { groupId: row.id }
  })
}

getList()
</script>

<style lang="scss" scoped>
.container {
  background-color: var(--color-whites);
  padding: 20px;
}
</style>
