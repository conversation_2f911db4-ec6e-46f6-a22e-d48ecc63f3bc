@use 'config' as *;
@use 'function' as *;
@use '../common/var' as *;
@use 'sass:map';

// 全局变量声明
$B: null;

/* Break-points
 -------------------------- */
@mixin res($key, $map: $--breakpoints) {
  // 循环断点Map，如果存在则返回
  @if map.has-key($map, $key) {
    @media only screen and #{unquote(map.get($map, $key))} {
      @content;
    }
  } @else {
    @warn "Undefeined points: `#{$map}`";
  }
}

/* Scrollbar
 -------------------------- */
@mixin scroll-bar {
  $--scrollbar-thumb-background: #b4bccc;
  $--scrollbar-track-background: #fff;

  &::-webkit-scrollbar {
    z-index: 11;
    width: 6px;

    &:horizontal {
      height: 6px;
    }

    &-thumb {
      border-radius: 5px;
      width: 6px;
      background: $--scrollbar-thumb-background;
    }

    &-corner {
      background: $--scrollbar-track-background;
    }

    &-track {
      background: $--scrollbar-track-background;

      &-piece {
        background: $--scrollbar-track-background;
        width: 6px;
      }
    }
  }
}

/* Placeholder
 -------------------------- */
@mixin placeholder {
  &::-webkit-input-placeholder {
    @content;
  }

  &::-moz-placeholder {
    @content;
  }

  &:-ms-input-placeholder {
    @content;
  }
}

/* BEM
 -------------------------- */
@mixin b($block) {
  $B: $namespace + '-' + $block !global;

  .#{$B} {
    @content;
  }
}

@mixin e($element) {
  $E: $element !global;
  $selector: &;
  $currentSelector: '';

  @each $unit in $element {
    $currentSelector: #{$currentSelector + '.' + $B + $element-separator + $unit + ','};
  }

  @if hitAllSpecialNestRule($selector) {
    @at-root {
      #{$selector} {
        #{$currentSelector} {
          @content;
        }
      }
    }
  } @else {
    @at-root {
      #{$currentSelector} {
        @content;
      }
    }
  }
}

@mixin m($modifier) {
  $selector: &;
  $currentSelector: '';

  @each $unit in $modifier {
    $currentSelector: #{$currentSelector + $selector + $modifier-separator + $unit + ','};
  }

  @at-root {
    #{$currentSelector} {
      @content;
    }
  }
}

@mixin configurable-m($modifier, $E-flag: false) {
  $selector: &;
  $interpolation: '';

  @if $E-flag {
    $interpolation: $element-separator + $E-flag;
  }

  @at-root {
    #{$selector} {
      .#{$B + $interpolation + $modifier-separator + $modifier} {
        @content;
      }
    }
  }
}

@mixin spec-selector($specSelector: '', $element: $E, $modifier: false, $block: $B) {
  $modifierCombo: '';

  @if $modifier {
    $modifierCombo: $modifier-separator + $modifier;
  }

  @at-root {
    #{&}#{$specSelector}.#{$block + $element-separator + $element + $modifierCombo } {
      @content;
    }
  }
}

@mixin meb($modifier: false, $element: $E, $block: $B) {
  $selector: &;
  $modifierCombo: '';

  @if $modifier {
    $modifierCombo: $modifier-separator + $modifier;
  }

  @at-root {
    #{$selector} {
      .#{$block + $element-separator + $element + $modifierCombo} {
        @content;
      }
    }
  }
}

@mixin when($state) {
  @at-root {
    &.#{$state-prefix + $state} {
      @content;
    }
  }
}

@mixin extend-rule($name) {
  @extend #{'%shared-' + $name};
}

@mixin share-rule($name) {
  $rule-name: '%shared-' + $name;

  @at-root #{$rule-name} {
    @content;
  }
}

@mixin pseudo($pseudo) {
  @at-root #{&}#{':#{$pseudo}'} {
    @content;
  }
}

@mixin picker-popper($background, $border, $box-shadow) {
  &.#{$namespace}-popper[role='tooltip'] {
    background: $background;
    border: $border;
    box-shadow: $box-shadow;

    .#{$namespace}-popper__arrow {
      &::before {
        border: $border;
      }
    }

    @each $placement,
      $adjacency in ('top': 'left', 'bottom': 'right', 'left': 'bottom', 'right': 'top')
    {
      &[data-popper-placement^='#{$placement}'] {
        .#{$namespace}-popper__arrow::before {
          border-#{$placement}-color: transparent;
          border-#{$adjacency}-color: transparent;
        }
      }
    }
  }
}

// dark
@mixin dark($block) {
  .dark {
    @include b($block) {
      @content;
    }
  }
}
