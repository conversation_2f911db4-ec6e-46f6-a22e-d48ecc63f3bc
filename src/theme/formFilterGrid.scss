.filter-container-template {
  .filter-grid-6 {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: auto auto;
    column-gap: 5px;
    grid-template-areas: 'a b c d e f' 'g g g g g g';

    $areas: (1, a), (2, b), (3, c), (4, d), (5, e), (6, f);

    @each $i, $area in $areas {
      & > .el-form-item:nth-child(#{$i}) {
        grid-area: #{$area};
      }
    }

    .fill-template {
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      column-gap: 5px;
      grid-area: g;
    }

    .el-form-item {
      align-items: center;
    }

    .el-form-item__label {
      display: flex;
      align-items: center;
      line-height: 1.2;
      text-align: right;
      width: 94px;
      padding-right: 6px;
    }
  }

  .filter-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .filter-btn-group {
      display: flex;
      flex-grow: 1;
      justify-content: flex-end;
      align-items: center;
    }
  }
}
