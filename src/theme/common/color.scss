// 顺序按照UI设计图：https://lanhuapp.com/web/#/item/project/detailDetach?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=991bd842-b40e-4b89-b7cf-d8e194ec0936&versionId=bde87871-2253-4961-b59e-8072f4b0ee62&docId=24faa850-1268-4f6c-b63d-120d829bf266&docType=axure&pageId=19733f56261b4f96a892c2e190ffe9bc&image_id=99d52d42-41c4-4a50-afe5-02a8b66a0c86&project_id=991bd842-b40e-4b89-b7cf-d8e194ec0936&fromEditor=true&type=image
$btn-colors: (
  // 翡翠绿
  emerald:
    (
      color: #fff,
      background: #2ad7c1,
      border-color: #2ad7c1
    ),
  // 蓝绿
  teal:
    (
      color: #fff,
      background: #30c9d7,
      border-color: #30c9d7
    ),
  // 紫色
  purple:
    (
      color: #fff,
      background: #a366ef,
      border-color: #a366ef
    ),
  // 蓝色
  blue:
    (
      color: #fff,
      background: #497fff,
      border-color: #497fff
    ),
  // 绿色
  green:
    (
      color: #fff,
      background: #67c23a,
      border-color: #67c23a
    ),
  // 红色
  red:
    (
      color: #fff,
      background: #ea6069,
      border-color: #ea6069
    ),
  // 黄色
  yellow:
    (
      color: #fff,
      background: #ffb20f,
      border-color: #ffb20f
    ),
  // 灰色
  gray:
    (
      color: #fff,
      background: #909399,
      border-color: #909399
    ),
  // 橙色
  orange:
    (
      color: #fff,
      background: #f88d5e,
      border-color: #f88d5e
    ),
  // 淡蓝色
  bluish:
    (
      color: #fff,
      background: #409eff,
      border-color: #409eff
    ),
  // 白色
  white:
    (
      color: #606266,
      background: #fff,
      border-color: #dbdee6
    )
);
