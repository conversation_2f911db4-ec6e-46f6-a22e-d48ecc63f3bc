<template>
  <div class="layout-navbars-breadcrumb-user" :style="{ flex: layoutUserFlexNum }">
    <!-- <div class="layout-navbars-breadcrumb-user-icon">
      <el-popover
        placement="bottom"
        trigger="click"
        v-model:visible="isShowUserNewsPopover"
        :width="300"
        popper-class="el-popover-pupop-user-news"
      >
        <template #reference>
          <div class="center">
            <el-badge :value="0" @click="isShowUserNewsPopover = !isShowUserNewsPopover">
              <i class="iconfont icon-tongzhi2" title="消息通知"></i>
            </el-badge>
            <span class="font12 ml8">消息</span>
          </div>
        </template>
        <transition name="el-zoom-in-top">
          <UserNews v-show="isShowUserNewsPopover" />
        </transition>
      </el-popover>
    </div> -->
    <!-- <div class="layout-navbars-breadcrumb-user-icon ai-center" @click="lockScreen">
      <i class="el-icon-unlock" title="返回首页"></i>
      <span class="font12 ml3">锁屏</span>
    </div>
    <div class="layout-navbars-breadcrumb-user-icon ai-center" @click="goHome">
      <i class="iconfont icon-shouye" title="返回首页"></i>
      <span class="font12 ml3">返回首页</span>
    </div> -->
    <el-dialog title="修改密码" v-model="resetPasswordDialogVisible">
      <el-form :model="resetPasswordForm" ref="formRef" :rules="resetPasswordRules">
        <el-form-item label="旧密码" :label-width="resetPasswordFormLabelWidth" prop="password">
          <el-input
            show-password
            v-model="resetPasswordForm.password"
            placeholder="请输入旧密码"
          ></el-input>
        </el-form-item>
        <el-form-item label="新密码" :label-width="resetPasswordFormLabelWidth" prop="newPassword">
          <el-input
            show-password
            v-model="resetPasswordForm.newPassword"
            placeholder="请输入新密码"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="重复新密码"
          :label-width="resetPasswordFormLabelWidth"
          prop="rePassword"
        >
          <el-input
            show-password
            v-model="resetPasswordForm.rePassword"
            placeholder="请重复新密码"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetPasswordDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitResetForm()" :loading="resetPasswordLoading"
          >确 定
        </el-button>
      </div>
    </el-dialog>
    <span class="download" @click="showDownloadList">下载中心</span>&nbsp;&nbsp;&nbsp;
    <el-dropdown :show-timeout="70" :hide-timeout="50" @command="onHandleCommandClick">
      <span class="layout-navbars-breadcrumb-user-link">
        <img :src="avatar" class="layout-navbars-breadcrumb-user-link-photo mr5" />
        {{ userName }}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="showEditPassword">修改密码</el-dropdown-item>
          <el-dropdown-item command="logOut">退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <el-drawer v-model="isShowDownloadList" title="下载任务" size="90%">
      <!--  表格 -->
      <el-table :data="downloadList" style="width: 100%">
        <el-table-column prop="typeTxt" label="类型"></el-table-column>
        <el-table-column prop="addTime" label="申请时间"></el-table-column>
        <el-table-column prop="statusTxt" label="状态"></el-table-column>
        <el-table-column prop="beginTime" label="开始时间"></el-table-column>
        <el-table-column prop="finishTime" label="完成时间"></el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              :type="canDownload(scope.row) ? 'primary' : 'default'"
              @click="handleDownload(scope.row)"
            >
              {{ canDownload(scope.row) ? '下载' : '取消' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--  加载更多 -->
      <div class="load-more" v-if="downloadList.length > 0">
        <el-button type="primary" @click="loadMore" :loading="loadMoreLoading">加载更多</el-button>
      </div>
    </el-drawer>
    <Search ref="searchRef" />
  </div>
</template>

<script lang="ts">
import { computed, getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import screenfull from 'screenfull'
import { resetRoute } from '/@/router/index'
import { useStore } from '/@/store/index'
// import { useTitle } from '/@/utils/setWebTitle'
import { Local } from '/@/utils/storage'
// import UserNews from '/@/layout/navBars/breadcrumb/userNews.vue'
import Search from '/@/layout/navBars/breadcrumb/search.vue'
import { editPassord } from '/@/api/admin'
import { getList, getUrl, cancelDownloadTask } from '/@/api/downloadTask'

import defaultAvatar from '/@/assets/images/avatar.png'

export default {
  name: 'layoutBreadcrumbUser',
  components: { Search },
  setup() {
    const { proxy } = getCurrentInstance() as any
    const router = useRouter()
    const store = useStore()
    // const title = useTitle()
    const searchRef = ref()
    // const resetPasswordForm = ref()
    const state = reactive({
      isScreenfull: false,
      isShowUserNewsPopover: false,
      disabledSize: '',
      resetPasswordLoading: false,
      resetPasswordDialogVisible: false,
      isShowDownloadList: false,
      downloadList: [],
      downloadPage: 1,
      loadMoreLoading: false,
      resetPasswordForm: {
        password: '',
        newPassword: '',
        rePassword: ''
      },
      resetPasswordFormLabelWidth: '120px',
      resetPasswordRules: {
        password: [
          {
            required: true,
            message: '请输入旧密码',
            trigger: 'blur'
          },
          {
            min: 6,
            max: 12,
            message: '长度在 6 到 12 个字符',
            trigger: 'blur'
          }
        ],
        newPassword: [
          {
            required: true,
            message: '请输入新密码',
            trigger: 'blur'
          },
          {
            min: 6,
            max: 12,
            message: '长度在 6 到 12 个字符',
            trigger: 'blur'
          }
        ],
        rePassword: [
          {
            required: true,
            message: '请重复密码',
            trigger: 'blur'
          },
          {
            min: 6,
            max: 12,
            message: '长度在 6 到 12 个字符',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error('请再次输入密码'))
              } else if (value !== state.resetPasswordForm.newPassword) {
                callback(new Error('两次输入密码不一致!'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    })
    // 获取用户信息 vuex
    const getUserInfos: any = computed(() => {
      return store.state.userInfos.userInfos
    })
    // 用户头像
    const avatar = computed(() => getUserInfos.value.photo || defaultAvatar)
    // 用户名
    const userName = computed(
      () =>
        getUserInfos.value.name ||
        getUserInfos.value.account ||
        getUserInfos.value.mobile ||
        getUserInfos.value.email
    )
    // 获取布局配置信息
    const getThemeConfig = computed(() => {
      return store.state.themeConfig.themeConfig
    })
    // 设置分割样式
    const layoutUserFlexNum = computed(() => {
      const { layout, isClassicSplitMenu } = getThemeConfig.value
      let num: any = ''
      if (
        layout === 'defaults' ||
        (layout === 'classic' && !isClassicSplitMenu) ||
        layout === 'columns'
      )
        num = 1
      else num = null
      return num
    })
    // 全屏点击时
    const onScreenfullClick = () => {
      if (!screenfull.isEnabled) {
        ElMessage.warning('暂不不支持全屏')
        return false
      }
      screenfull.toggle()
      screenfull.on('change', () => {
        if (screenfull.isFullscreen) state.isScreenfull = true
        else state.isScreenfull = false
      })
      return true
    }
    // 布局配置 icon 点击时
    const onLayoutSetingClick = () => {
      proxy.mittBus.emit('openSetingsDrawer')
    }
    // 下拉菜单点击时
    const onHandleCommandClick = (path: string) => {
      if (path === 'logOut') {
        ElMessageBox({
          closeOnClickModal: false,
          closeOnPressEscape: false,
          title: '提示',
          message: '此操作将退出登录，是否继续',
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              instance.confirmButtonText = '退出中'
              setTimeout(() => {
                done()
                setTimeout(() => {
                  instance.confirmButtonLoading = false
                }, 300)
              }, 700)
            } else {
              done()
            }
          }
        })
          .then(() => {
            Local.clear() // 清除缓存/token等
            store.dispatch('userInfos/setUserInfos', {}) // 清除store中的用户信息
            resetRoute() // 删除/重置路由
            router.push('/login')
            setTimeout(() => {
              ElMessage.success('退出成功')
            }, 300)
          })
          .catch(() => {})
      } else if (path === 'showEditPassword') {
        state.resetPasswordForm = {
          password: '',
          newPassword: '',
          rePassword: ''
        }
        state.resetPasswordDialogVisible = true
      } else {
        router.push(path)
      }
    }
    // 菜单搜索点击
    const onSearchClick = () => {
      searchRef.value.openSearch()
    }
    // 修改密码
    const submitResetForm = () => {
      proxy.$refs.formRef.validate((valid) => {
        if (valid) {
          state.resetPasswordLoading = true
          editPassord(state.resetPasswordForm)
            .then(() => {
              state.resetPasswordLoading = false
              Local.clear() // 清除缓存/token等
              store.dispatch('userInfos/setUserInfos', {}) // 清除store中的用户信息
              resetRoute() // 删除/重置路由
              router.push('/login')
            })
            .catch(() => {
              state.resetPasswordLoading = false
            })
        }
      })
    }

    // 初始化全局组件大小
    const initComponentSize = () => {
      // switch (Local.get('themeConfig').globalComponentSize) {
      //   case '':
      //     state.disabledSize = ''
      //     break
      //   case 'medium':
      //     state.disabledSize = 'medium'
      //     break
      //   case 'small':
      //     state.disabledSize = 'small'
      //     break
      //   case 'mini':
      //     state.disabledSize = 'mini'
      //     break
      // }
    }

    // 组件大小改变
    const onComponentSizeChange = (size: string) => {
      Local.remove('themeConfig')
      getThemeConfig.value.globalComponentSize = size
      Local.set('themeConfig', getThemeConfig.value)
      proxy.$ELEMENT.size = size
      initComponentSize()
      window.location.reload()
    }

    // 去首页
    const goHome = () => {
      router.push('/home')
    }
    // 锁屏
    const lockScreen = () => {
      store.state.themeConfig.themeConfig.isLockScreen = true
      store.state.themeConfig.themeConfig.lockScreenTime = 0
    }

    const showDownloadList = () => {
      getList({ page: state.downloadPage }).then((res) => {
        state.downloadList = res.list
        state.isShowDownloadList = true
      })
    }

    const canDownload = (data: any) => Number(data.status) === 1

    const handleDownload = async (item: any) => {
      const { addTime, typeTxt, id } = item
      try {
        if (canDownload(item)) {
          const r = await getUrl(id)
          const { url } = r
          // 先提示下载成功,内容比较大需要等等的文案
          ElMessage.success('生成成功,开始下载,文件较大需要等等')
          // 新标签页打开
          window.open(url)
          return
        }

        await cancelDownloadTask(id)
        ElMessage.success(`${typeTxt} ${addTime} 取消成功!`)
      } catch {
        //
      }
    }

    const loadMore = () => {
      state.loadMoreLoading = true
      state.downloadPage += 1
      getList({ page: state.downloadPage }).then((res) => {
        state.downloadList = state.downloadList.concat(res.list)
        state.loadMoreLoading = false
      })
      // setTimeout(() => {
      //   state.loadMoreLoading = false
      //   state.downloadList = state.downloadList.concat(state.downloadList)
      // }, 1000)
    }
    // 页面加载时
    onMounted(() => {
      if (Local.get('themeConfig')) {
        initComponentSize()
      }
    })
    return {
      avatar,
      userName,
      getUserInfos,
      onLayoutSetingClick,
      onHandleCommandClick,
      onScreenfullClick,
      onSearchClick,
      onComponentSizeChange,
      searchRef,
      layoutUserFlexNum,
      goHome,
      lockScreen,
      submitResetForm,
      showDownloadList,
      canDownload,
      handleDownload,
      loadMore,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.download {
  color: var(--el-text-color-regular);
  cursor: pointer;
}

.load-more {
  text-align: center;
  padding: 10px 0;
  cursor: pointer;
  color: var(--el-text-color-regular);
}

.layout-navbars-breadcrumb-user {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  &-link {
    height: 100%;
    display: flex;
    align-items: center;
    white-space: nowrap;
    cursor: default;

    &-photo {
      width: 25px;
      height: 25px;
      border-radius: 100%;
    }
  }

  &-icon {
    padding: 0 10px;
    cursor: pointer;
    color: var(--bg-topBarColor);
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;

    &:hover {
      background: rgba(0, 0, 0, 0.04);

      i {
        display: inline-block;
        animation: logoAnimation 0.3s ease-in-out;
      }
    }
  }

  :deep(.el-dropdown) {
    color: var(--bg-topBarColor);
  }

  :deep(.el-badge) {
    height: 40px;
    line-height: 40px;
    display: flex;
    align-items: center;
  }

  :deep(.el-badge__content.is-fixed) {
    top: 12px;
    left: -9px;
    opacity: 0.8;
    padding: 0 4px;
    border-radius: 18px;
    min-width: 18px;
  }
}
</style>
