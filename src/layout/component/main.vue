<template>
  <el-main
    class="layout-main"
    id="layout-main"
    :data-view-height="windowHeight - Number(headerHeight) - 30"
  >
    <el-scrollbar
      class="layout-scrollbar"
      ref="layoutScrollbarRef"
      :style="{
        minHeight: `calc(100vh - ${headerHeight}px`,
        '--max-height': `calc(${windowHeight}px - ${headerHeight}px - 30px)`,
        padding: currentRouteMeta.isLink && currentRouteMeta.isIframe ? 0 : '',
        transition: 'padding 0.3s ease-in-out'
      }"
    >
      <LayoutParentView />
      <Footer v-if="getThemeConfig.isFooter" />
    </el-scrollbar>
  </el-main>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  watch,
  onBeforeMount
} from 'vue'
import { useStore } from '/@/store/index'
import { useRoute } from 'vue-router'
import LayoutParentView from '/@/layout/routerView/parent.vue'
import Footer from '/@/layout/footer/index.vue'

export default defineComponent({
  name: 'layoutMain',
  components: { LayoutParentView, Footer },
  setup() {
    const { proxy } = getCurrentInstance() as any
    const route = useRoute()
    const store = useStore()
    const state = reactive({
      headerHeight: '',
      windowHeight: computed(() => window.innerHeight),
      currentRouteMeta: {}
    })
    // 获取布局配置信息
    const getThemeConfig = computed(() => {
      return store.state.themeConfig.themeConfig
    })
    // 设置 main 的高度
    const initHeaderHeight = () => {
      const { isTagsview } = store.state.themeConfig.themeConfig
      if (isTagsview) return (state.headerHeight = `84`)
      return (state.headerHeight = `50`)
    }
    // 初始化获取当前路由 meta，用于设置 iframes padding
    const initGetMeta = () => {
      state.currentRouteMeta = route.meta
    }
    // 页面加载前
    onBeforeMount(() => {
      initHeaderHeight()
      initGetMeta()
    })
    // 监听 themeConfig 配置文件的变化，更新菜单 el-scrollbar 的高度
    watch(store.state.themeConfig.themeConfig, (val) => {
      state.headerHeight = val.isTagsview ? '84' : '50'
      if (val.isFixedHeaderChange !== val.isFixedHeader) {
        if (!proxy.$refs.layoutScrollbarRef) return false
        proxy.$refs.layoutScrollbarRef.update()
      }
    })
    // 监听路由变化
    watch(
      () => route.path,
      () => {
        state.currentRouteMeta = route.meta
      }
    )
    return {
      getThemeConfig,
      ...toRefs(state)
    }
  }
})
</script>
